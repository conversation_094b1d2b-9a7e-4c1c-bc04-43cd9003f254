/**
 * Simple test script to validate the Twitter bot setup
 * Run with: node test-setup.js
 */

const fs = require('fs')
const path = require('path')

console.log('🚀 Twitter Bot Dashboard - Setup Validation')
console.log('==========================================')

// Check Node.js version
const nodeVersion = process.version
console.log(`📦 Node.js version: ${nodeVersion}`)

const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0])
if (majorVersion < 18) {
  console.log('⚠️  Warning: Node.js 18+ is recommended for Next.js 14')
  console.log('   Please upgrade Node.js for optimal performance')
} else {
  console.log('✅ Node.js version is compatible')
}

// Check if required files exist
const requiredFiles = [
  'package.json',
  '.env.local',
  'src/lib/supabase.ts',
  'src/lib/twitter.ts',
  'src/lib/openai.ts',
  'src/lib/rss.ts',
  'src/app/page.tsx',
  'src/app/feeds/page.tsx',
  'src/app/content/page.tsx'
]

console.log('\n📁 Checking required files:')
let allFilesExist = true

for (const file of requiredFiles) {
  const filePath = path.join(__dirname, file)
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`)
  } else {
    console.log(`❌ ${file} - MISSING`)
    allFilesExist = false
  }
}

// Check environment variables
console.log('\n🔧 Checking environment configuration:')
const envPath = path.join(__dirname, '.env.local')

if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8')
  
  const requiredEnvVars = [
    'TWITTER_CLIENT_ID',
    'TWITTER_CLIENT_SECRET', 
    'TWITTER_ACCESS_TOKEN',
    'TWITTER_REFRESH_TOKEN',
    'OPENAI_API_KEY',
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ]
  
  for (const envVar of requiredEnvVars) {
    if (envContent.includes(`${envVar}=your_`) || !envContent.includes(envVar)) {
      console.log(`⚠️  ${envVar} - needs configuration`)
    } else {
      console.log(`✅ ${envVar} - configured`)
    }
  }
} else {
  console.log('❌ .env.local file not found')
}

// Check package.json dependencies
console.log('\n📦 Checking dependencies:')
const packagePath = path.join(__dirname, 'package.json')

if (fs.existsSync(packagePath)) {
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
  const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies }
  
  const requiredDeps = [
    '@supabase/supabase-js',
    'twitter-api-v2',
    'openai',
    'rss-parser',
    'unfluff',
    'node-cron',
    'lucide-react'
  ]
  
  for (const dep of requiredDeps) {
    if (dependencies[dep]) {
      console.log(`✅ ${dep} - v${dependencies[dep]}`)
    } else {
      console.log(`❌ ${dep} - MISSING`)
    }
  }
}

// Summary
console.log('\n📋 Setup Summary:')
if (allFilesExist) {
  console.log('✅ All required files are present')
} else {
  console.log('❌ Some required files are missing')
}

console.log('\n🔗 Next Steps:')
console.log('1. Update Node.js to version 18+ if needed')
console.log('2. Configure all environment variables in .env.local')
console.log('3. Get your Supabase project keys from: https://app.supabase.com')
console.log('4. Set up Twitter Developer Account and get API keys')
console.log('5. Get OpenAI API key from: https://platform.openai.com')
console.log('6. Run: npm run dev (after Node.js upgrade)')
console.log('7. Visit: http://localhost:3000')

console.log('\n🎯 Features Available:')
console.log('• Twitter Analytics Dashboard')
console.log('• RSS Feed Management') 
console.log('• AI Content Generation')
console.log('• Automated Posting')
console.log('• Content Queue Management')

console.log('\n🔧 API Endpoints:')
console.log('• GET  /api/twitter/stats - Twitter analytics')
console.log('• GET  /api/rss/feeds - RSS feed management')
console.log('• GET  /api/content/queue - Content queue')
console.log('• POST /api/content/generate - AI content generation')
console.log('• POST /api/content/post - Post to Twitter')
console.log('• POST /api/scheduler - Control automation')

console.log('\n✨ Setup validation complete!')
