'use client'

import { useEffect, useState } from 'react'
import { ArrowLeft, Plus, Trash2, RefreshCw, ExternalLink, Calendar } from 'lucide-react'
import Link from 'next/link'

interface RSSFeed {
  id: string
  name: string
  url: string
  is_active: boolean
  created_at: string
  updated_at: string
}

interface FeedItem {
  title: string
  url: string
  publishedAt: string
  content: string
  categories: string[]
  author?: string
  score: number
}

export default function FeedsPage() {
  const [feeds, setFeeds] = useState<RSSFeed[]>([])
  const [feedItems, setFeedItems] = useState<FeedItem[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [showAddForm, setShowAddForm] = useState(false)
  const [newFeed, setNewFeed] = useState({ name: '', url: '' })

  const fetchFeeds = async () => {
    try {
      const response = await fetch('/api/rss/feeds')
      if (response.ok) {
        const data = await response.json()
        setFeeds(data)
      }
    } catch (error) {
      console.error('Error fetching feeds:', error)
    }
  }

  const fetchFeedItems = async () => {
    try {
      setRefreshing(true)
      const response = await fetch('/api/rss/items')
      if (response.ok) {
        const data = await response.json()
        setFeedItems(data)
      }
    } catch (error) {
      console.error('Error fetching feed items:', error)
    } finally {
      setRefreshing(false)
    }
  }

  const addFeed = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const response = await fetch('/api/rss/feeds', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newFeed)
      })
      
      if (response.ok) {
        setNewFeed({ name: '', url: '' })
        setShowAddForm(false)
        await fetchFeeds()
      }
    } catch (error) {
      console.error('Error adding feed:', error)
    }
  }

  const toggleFeed = async (id: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/rss/feeds/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_active: !isActive })
      })
      
      if (response.ok) {
        await fetchFeeds()
      }
    } catch (error) {
      console.error('Error toggling feed:', error)
    }
  }

  const deleteFeed = async (id: string) => {
    if (!confirm('Are you sure you want to delete this feed?')) return
    
    try {
      const response = await fetch(`/api/rss/feeds/${id}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        await fetchFeeds()
      }
    } catch (error) {
      console.error('Error deleting feed:', error)
    }
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      await Promise.all([fetchFeeds(), fetchFeedItems()])
      setLoading(false)
    }
    loadData()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading RSS feeds...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center">
              <Link href="/" className="mr-4">
                <ArrowLeft className="h-6 w-6 text-gray-600 hover:text-gray-900" />
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">RSS Feeds</h1>
                <p className="text-sm text-gray-600 mt-1">
                  Manage your RSS feeds and view aggregated content
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={fetchFeedItems}
                disabled={refreshing}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh Content
              </button>
              <button
                type="button"
                onClick={() => setShowAddForm(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Feed
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* RSS Feeds List */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Configured Feeds</h2>
                <p className="text-sm text-gray-600 mt-1">{feeds.length} feeds configured</p>
              </div>
              <div className="divide-y divide-gray-200">
                {feeds.map((feed) => (
                  <div key={feed.id} className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <h3 className="text-sm font-medium text-gray-900 truncate">
                          {feed.name}
                        </h3>
                        <p className="text-xs text-gray-500 truncate mt-1">
                          {feed.url}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          type="button"
                          onClick={() => toggleFeed(feed.id, feed.is_active)}
                          className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                            feed.is_active ? 'bg-blue-600' : 'bg-gray-200'
                          }`}
                        >
                          <span
                            className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                              feed.is_active ? 'translate-x-5' : 'translate-x-0'
                            }`}
                          />
                        </button>
                        <button
                          type="button"
                          onClick={() => deleteFeed(feed.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Feed Items */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Latest Content</h2>
                <p className="text-sm text-gray-600 mt-1">
                  {feedItems.length} items found (deduplicated and scored)
                </p>
              </div>
              <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
                {feedItems.map((item, index) => (
                  <div key={index} className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h3 className="text-sm font-medium text-gray-900 mb-2">
                          {item.title}
                        </h3>
                        <div className="flex items-center text-xs text-gray-500 space-x-4 mb-2">
                          <span className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            {new Date(item.publishedAt).toLocaleDateString()}
                          </span>
                          {item.author && <span>by {item.author}</span>}
                          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            Score: {item.score.toFixed(0)}
                          </span>
                        </div>
                        {item.categories.length > 0 && (
                          <div className="flex flex-wrap gap-1 mb-2">
                            {item.categories.slice(0, 3).map((category, idx) => (
                              <span
                                key={idx}
                                className="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded"
                              >
                                {category}
                              </span>
                            ))}
                          </div>
                        )}
                        <p className="text-xs text-gray-600 line-clamp-2">
                          {item.content.slice(0, 200)}...
                        </p>
                      </div>
                      <a
                        href={item.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="ml-4 text-blue-600 hover:text-blue-900"
                      >
                        <ExternalLink className="h-4 w-4" />
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add Feed Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Add RSS Feed</h3>
              <form onSubmit={addFeed}>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Feed Name
                  </label>
                  <input
                    type="text"
                    value={newFeed.name}
                    onChange={(e) => setNewFeed({ ...newFeed, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Feed URL
                  </label>
                  <input
                    type="url"
                    value={newFeed.url}
                    onChange={(e) => setNewFeed({ ...newFeed, url: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                  >
                    Add Feed
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
