import { NextRequest, NextResponse } from 'next/server'
import { dbOperations } from '@/lib/supabase'

export async function GET() {
  try {
    const preferences = await dbOperations.getUserPreferences()
    
    if (!preferences) {
      // Return default preferences if none exist
      return NextResponse.json({
        max_topics_to_select: 4,
        posting_interval_minutes: 30,
        ai_tone: 'analytical',
        include_personal_touch: true,
        auto_post_enabled: false
      })
    }
    
    return NextResponse.json(preferences)
  } catch (error) {
    console.error('Error fetching preferences:', error)
    return NextResponse.json(
      { error: 'Failed to fetch preferences' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const updates = await request.json()
    const preferences = await dbOperations.updateUserPreferences(updates)
    return NextResponse.json(preferences)
  } catch (error) {
    console.error('Error updating preferences:', error)
    return NextResponse.json(
      { error: 'Failed to update preferences' },
      { status: 500 }
    )
  }
}
