import { NextResponse } from 'next/server'
import migrateExistingData from '@/scripts/migrate-existing-bot'

export async function POST() {
  try {
    await migrateExistingData()
    return NextResponse.json({ 
      message: 'Migration completed successfully',
      success: true 
    })
  } catch (error) {
    console.error('Migration failed:', error)
    return NextResponse.json(
      { 
        error: 'Migration failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
