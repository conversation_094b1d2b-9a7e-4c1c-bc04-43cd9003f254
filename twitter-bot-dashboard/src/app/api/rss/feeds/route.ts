import { NextRequest, NextResponse } from 'next/server'
import { dbOperations } from '@/lib/supabase'

export async function GET() {
  try {
    const feeds = await dbOperations.getRSSFeeds()
    return NextResponse.json(feeds)
  } catch (error) {
    console.error('Error fetching RSS feeds:', error)
    return NextResponse.json(
      { error: 'Failed to fetch RSS feeds' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { name, url } = await request.json()
    
    if (!name || !url) {
      return NextResponse.json(
        { error: 'Name and URL are required' },
        { status: 400 }
      )
    }
    
    const feed = await dbOperations.addRSSFeed(name, url)
    return NextResponse.json(feed, { status: 201 })
  } catch (error) {
    console.error('Error adding RSS feed:', error)
    return NextResponse.json(
      { error: 'Failed to add RSS feed' },
      { status: 500 }
    )
  }
}
