import { NextRequest, NextResponse } from 'next/server'
import { dbOperations } from '@/lib/supabase'

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const updates = await request.json()
    const feed = await dbOperations.updateRSSFeed(id, updates)
    return NextResponse.json(feed)
  } catch (error) {
    console.error('Error updating RSS feed:', error)
    return NextResponse.json(
      { error: 'Failed to update RSS feed' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    // Note: We'll need to add a delete method to dbOperations
    // For now, we'll just deactivate the feed
    await dbOperations.updateRSSFeed(id, { is_active: false })
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting RSS feed:', error)
    return NextResponse.json(
      { error: 'Failed to delete RSS feed' },
      { status: 500 }
    )
  }
}
