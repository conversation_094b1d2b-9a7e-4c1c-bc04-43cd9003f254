import { NextRequest, NextResponse } from 'next/server'
import { dbOperations } from '@/lib/supabase'
import { twitterOperations } from '@/lib/twitter'

export async function POST(request: NextRequest) {
  try {
    const { contentIds } = await request.json()
    
    if (!contentIds || !Array.isArray(contentIds) || contentIds.length === 0) {
      return NextResponse.json(
        { error: 'Content IDs array is required' },
        { status: 400 }
      )
    }
    
    // Get selected content items
    const contentQueue = await dbOperations.getContentQueue(100)
    const selectedItems = contentQueue.filter(item => 
      contentIds.includes(item.id) && !item.is_posted
    )
    
    if (selectedItems.length === 0) {
      return NextResponse.json(
        { error: 'No valid content items found' },
        { status: 400 }
      )
    }
    
    let posted = 0
    const results = []
    
    for (const item of selectedItems) {
      try {
        // Use AI-generated content if available, otherwise create basic tweet
        const tweetContent = item.ai_generated_content || 
          `${item.original_title}\n\n${item.original_url}`
        
        // Post to Twitter
        const tweetData = await twitterOperations.postTweet(tweetContent)
        
        // Save to posted tweets database
        await dbOperations.addPostedTweet({
          tweet_id: tweetData.id,
          content: tweetContent,
          original_url: item.original_url,
          original_title: item.original_title,
          impressions: 0,
          retweets: 0,
          likes: 0,
          replies: 0,
          posted_at: tweetData.created_at
        })
        
        posted++
        results.push({
          id: item.id,
          success: true,
          tweetId: tweetData.id
        })
        
        // Add delay between posts to avoid rate limiting
        if (posted < selectedItems.length) {
          await new Promise(resolve => setTimeout(resolve, 2000))
        }
      } catch (error) {
        console.error(`Error posting content ${item.id}:`, error)
        results.push({
          id: item.id,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
    
    // Mark successfully posted items as posted
    const successfulIds = results
      .filter(r => r.success)
      .map(r => r.id)
    
    if (successfulIds.length > 0) {
      await dbOperations.markContentAsPosted(successfulIds)
    }
    
    return NextResponse.json({
      posted,
      total: selectedItems.length,
      results
    })
  } catch (error) {
    console.error('Error posting content:', error)
    return NextResponse.json(
      { error: 'Failed to post content' },
      { status: 500 }
    )
  }
}
