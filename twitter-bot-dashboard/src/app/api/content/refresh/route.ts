import { NextResponse } from 'next/server'
import { dbOperations } from '@/lib/supabase'
import rssOperations from '@/lib/rss'

export async function POST() {
  try {
    // Get active RSS feeds
    const feeds = await dbOperations.getRSSFeeds()
    const activeFeeds = feeds.filter(feed => feed.is_active)
    
    if (activeFeeds.length === 0) {
      return NextResponse.json({ message: 'No active RSS feeds found' })
    }
    
    // Get aggregated content from all active feeds
    const feedUrls = activeFeeds.map(feed => feed.url)
    const content = await rssOperations.getAggregatedContent(feedUrls)
    
    // Score and filter content
    const scoredContent = rssOperations.scoreContent(content)
    
    // Get existing content URLs to avoid duplicates
    const existingQueue = await dbOperations.getContentQueue(1000)
    const existingUrls = new Set(existingQueue.map(item => item.original_url))
    
    // Add new content to queue
    let added = 0
    for (const item of scoredContent.slice(0, 20)) { // Limit to top 20 items
      if (!existingUrls.has(item.url)) {
        try {
          // Find the RSS feed ID
          const feedUrl = activeFeeds.find(feed => 
            item.url.includes(new URL(feed.url).hostname)
          )?.id
          
          await dbOperations.addToContentQueue({
            original_url: item.url,
            original_title: item.title,
            original_content: item.content,
            rss_feed_id: feedUrl,
            is_selected: false,
            is_posted: false,
            priority_score: item.score
          })
          
          added++
        } catch (error) {
          console.error(`Error adding content item: ${item.title}`, error)
        }
      }
    }
    
    return NextResponse.json({
      message: `Added ${added} new items to content queue`,
      totalProcessed: scoredContent.length,
      added
    })
  } catch (error) {
    console.error('Error refreshing content queue:', error)
    return NextResponse.json(
      { error: 'Failed to refresh content queue' },
      { status: 500 }
    )
  }
}
