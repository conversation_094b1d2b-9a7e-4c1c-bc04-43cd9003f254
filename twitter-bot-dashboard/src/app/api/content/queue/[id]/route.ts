import { NextRequest, NextResponse } from 'next/server'
import { dbOperations } from '@/lib/supabase'

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const updates = await request.json()
    const updatedItem = await dbOperations.updateContentQueue(id, updates)
    return NextResponse.json(updatedItem)
  } catch (error) {
    console.error('Error updating content queue item:', error)
    return NextResponse.json(
      { error: 'Failed to update content queue item' },
      { status: 500 }
    )
  }
}
