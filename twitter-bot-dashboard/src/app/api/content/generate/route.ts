import { NextRequest, NextResponse } from 'next/server'
import aiOperations from '@/lib/openai'

export async function POST(request: NextRequest) {
  try {
    const { title, content, url, options = {} } = await request.json()
    
    if (!title || !content || !url) {
      return NextResponse.json(
        { error: 'Title, content, and URL are required' },
        { status: 400 }
      )
    }
    
    const generatedContent = await aiOperations.generateTweetContent(
      title,
      content,
      url,
      options
    )
    
    return NextResponse.json(generatedContent)
  } catch (error) {
    console.error('Error generating content:', error)
    return NextResponse.json(
      { error: 'Failed to generate content' },
      { status: 500 }
    )
  }
}
