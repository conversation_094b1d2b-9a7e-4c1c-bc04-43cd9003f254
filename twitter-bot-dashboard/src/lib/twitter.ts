// Cached Twitter client instance
let twitterClientInstance: any = null

// Twitter client configuration - only initialize if keys are available
function getTwitterClient() {
  if (!process.env.TWITTER_APP_KEY || !process.env.TWITTER_APP_SECRET ||
      !process.env.TWITTER_ACCESS_TOKEN || !process.env.TWITTER_ACCESS_SECRET) {
    throw new Error('Twitter API keys not configured')
  }

  // Return cached instance if available
  if (twitterClientInstance) {
    return twitterClientInstance
  }

  try {
    // Use require to avoid initialization issues
    const { TwitterApi } = require('twitter-api-v2')

    // Create new instance and cache it
    twitterClientInstance = new TwitterApi({
      appKey: process.env.TWITTER_APP_KEY,
      appSecret: process.env.TWITTER_APP_SECRET,
      accessToken: process.env.TWITTER_ACCESS_TOKEN,
      accessSecret: process.env.TWITTER_ACCESS_SECRET,
    })

    return twitterClientInstance
  } catch (error) {
    console.error('Error initializing Twitter client:', error)
    throw new Error('Twitter API initialization failed')
  }
}

// Cached Twitter V1 client instance
let twitterV1ClientInstance: any = null

// Twitter API v1.1 client for additional features
function getTwitterV1Client() {
  if (!process.env.TWITTER_APP_KEY || !process.env.TWITTER_APP_SECRET ||
      !process.env.TWITTER_ACCESS_TOKEN || !process.env.TWITTER_ACCESS_SECRET) {
    throw new Error('Twitter API keys not configured')
  }

  // Return cached instance if available
  if (twitterV1ClientInstance) {
    return twitterV1ClientInstance
  }

  // Use require to avoid initialization issues
  const { TwitterApi } = require('twitter-api-v2')

  // Create new instance and cache it
  twitterV1ClientInstance = new TwitterApi({
    appKey: process.env.TWITTER_APP_KEY,
    appSecret: process.env.TWITTER_APP_SECRET,
    accessToken: process.env.TWITTER_ACCESS_TOKEN,
    accessSecret: process.env.TWITTER_ACCESS_SECRET,
  })

  return twitterV1ClientInstance
}

export interface TwitterUserData {
  id: string
  username: string
  name: string
  followers_count: number
  following_count: number
  tweet_count: number
  verified: boolean
  profile_image_url?: string
}

export interface TweetData {
  id: string
  text: string
  created_at: string
  public_metrics?: {
    retweet_count: number
    like_count: number
    reply_count: number
    quote_count: number
    impression_count?: number
  }
}

export const twitterOperations = {
  // Get current user information
  async getCurrentUser(): Promise<TwitterUserData> {
    try {
      const twitterClient = getTwitterClient()
      const user = await twitterClient.v2.me({
        'user.fields': ['public_metrics', 'verified', 'profile_image_url']
      })

      return {
        id: user.data.id,
        username: user.data.username,
        name: user.data.name,
        followers_count: user.data.public_metrics?.followers_count || 0,
        following_count: user.data.public_metrics?.following_count || 0,
        tweet_count: user.data.public_metrics?.tweet_count || 0,
        verified: user.data.verified || false,
        profile_image_url: user.data.profile_image_url
      }
    } catch (error) {
      console.error('Error fetching current user:', error)
      // Return default user data if Twitter API fails
      throw new Error(`Twitter API error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  },

  // Post a tweet
  async postTweet(content: string): Promise<TweetData> {
    try {
      const twitterClient = getTwitterClient()
      const tweet = await twitterClient.v2.tweet(content)

      return {
        id: tweet.data.id,
        text: content,
        created_at: new Date().toISOString()
      }
    } catch (error) {
      console.error('Error posting tweet:', error)
      throw error
    }
  },

  // Get recent tweets with metrics
  async getRecentTweets(): Promise<TweetData[]> {
    try {
      // For now, return empty array to avoid API complexity during build
      // This will be populated with actual data when API keys are configured
      return []
    } catch (error) {
      console.error('Error fetching recent tweets:', error)
      return []
    }
  },

  // Get tweet analytics
  async getTweetAnalytics(tweetId: string): Promise<TweetData | null> {
    try {
      const twitterClient = getTwitterClient()
      const tweet = await twitterClient.v2.singleTweet(tweetId, {
        'tweet.fields': ['created_at', 'public_metrics']
      })

      if (!tweet.data) return null

      return {
        id: tweet.data.id,
        text: tweet.data.text,
        created_at: tweet.data.created_at || new Date().toISOString(),
        public_metrics: tweet.data.public_metrics
      }
    } catch (error) {
      console.error('Error fetching tweet analytics:', error)
      return null
    }
  },

  // Calculate total impressions from recent tweets
  async getTotalImpressions(): Promise<number> {
    try {
      const tweets = await this.getRecentTweets() // Get more tweets for better calculation
      
      // Filter tweets from the last 30 days
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - 30)
      
      const recentTweets = tweets.filter(tweet => 
        new Date(tweet.created_at) >= cutoffDate
      )
      
      // Sum up impressions (if available) or estimate based on engagement
      let totalImpressions = 0
      for (const tweet of recentTweets) {
        if (tweet.public_metrics?.impression_count) {
          totalImpressions += tweet.public_metrics.impression_count
        } else if (tweet.public_metrics) {
          // Estimate impressions based on engagement (rough calculation)
          const engagement = (tweet.public_metrics.like_count || 0) + 
                           (tweet.public_metrics.retweet_count || 0) + 
                           (tweet.public_metrics.reply_count || 0)
          totalImpressions += Math.max(engagement * 10, 100) // Rough estimate
        }
      }
      
      return totalImpressions
    } catch (error) {
      console.error('Error calculating total impressions:', error)
      return 0
    }
  },

  // Get authentication status
  async getAuthStatus(): Promise<{ authenticated: boolean }> {
    try {
      await this.getCurrentUser()
      return { authenticated: true }
    } catch (error) {
      console.error('Error checking auth status:', error)
      return { authenticated: false }
    }
  }
}

export { getTwitterClient, getTwitterV1Client }
