{"name": "twitter-bot-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@extractus/article-extractor": "^8.0.19", "@supabase/supabase-js": "^2.50.2", "@types/node-cron": "^3.0.11", "lucide-react": "^0.525.0", "next": "15.3.4", "node-cron": "^4.1.1", "openai": "^5.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "rss-parser": "^3.13.0", "twitter-api-v2": "^1.15.2", "unfluff": "^3.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}