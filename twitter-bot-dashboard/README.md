# Twitter Bot Dashboard

An automated Twitter bot with RSS feed integration, AI-powered content generation, and a comprehensive dashboard for monitoring and management.

## Features

- **Twitter Analytics Dashboard**: Real-time monitoring of followers, tweets, impressions, and engagements
- **RSS Feed Management**: Configure and manage multiple RSS feeds with content deduplication
- **AI Content Generation**: OpenAI-powered tweet generation with hooks and personal touch
- **Content Queue**: Review, select, and schedule content for posting
- **Automated Posting**: Configurable automated posting with intelligent content selection
- **Supabase Integration**: Robust database for analytics, content, and configuration storage

## Prerequisites

- Node.js 18+ and npm
- Twitter Developer Account with API keys
- OpenAI API key
- Supabase project

## Setup Instructions

### 1. Environment Configuration

Copy the `.env.local` file and fill in your credentials:

```bash
# Twitter API Configuration
TWITTER_CLIENT_ID=your_twitter_client_id
TWITTER_CLIENT_SECRET=your_twitter_client_secret
TWITTER_ACCESS_TOKEN=your_twitter_access_token
TWITTER_REFRESH_TOKEN=your_twitter_refresh_token

# Twitter API v1.1 (for additional features)
TWITTER_APP_KEY=your_twitter_app_key
TWITTER_APP_SECRET=your_twitter_app_secret
TWITTER_ACCESS_SECRET=your_twitter_access_secret

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://fmhujzbqfzyyffgzwtzb.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 2. Twitter API Setup

1. Create a Twitter Developer Account
2. Create a new app with OAuth 2.0 enabled
3. Set up OAuth 2.0 with read/write permissions
4. Generate your API keys and tokens
5. Use the existing `auth.js` script to get OAuth tokens if needed

### 3. Supabase Setup

The database schema is automatically created. You need to:

1. Get your Supabase project URL and keys
2. Update the `.env.local` file with your Supabase credentials
3. The database tables will be created automatically when you first run the app

### 4. Installation and Running

```bash
# Install dependencies
npm install

# Run the development server
npm run dev

# Open http://localhost:3000 in your browser
```

### 5. Initial Data Migration

If you have an existing Twitter bot with posted.json data:

```bash
# Run migration to import existing data
curl -X POST http://localhost:3000/api/migrate
```

## Usage

### Dashboard

- **Main Dashboard**: View Twitter analytics, followers, tweets, and impressions
- **RSS Feeds Page**: Manage RSS feeds and view aggregated content
- **Content Queue**: Review AI-generated content and select items for posting

### API Endpoints

- `GET /api/twitter/stats` - Get Twitter analytics
- `GET /api/rss/feeds` - Get RSS feeds
- `POST /api/rss/feeds` - Add new RSS feed
- `GET /api/rss/items` - Get aggregated RSS content
- `GET /api/content/queue` - Get content queue
- `POST /api/content/generate` - Generate AI content
- `POST /api/content/post` - Post selected content
- `GET /api/scheduler` - Get scheduler status
- `POST /api/scheduler` - Control scheduler (start/stop)

### Automated Posting

1. Enable auto-posting in preferences
2. Configure posting interval (default: 30 minutes)
3. The scheduler will automatically:
   - Refresh RSS feeds
   - Generate AI content for high-scoring items
   - Post the best content automatically

### Content Selection

1. Go to the Content Queue page
2. Review AI-generated content
3. Select up to 4 topics (configurable)
4. Click "Post Selected" to publish to Twitter

## Architecture

- **Frontend**: Next.js 14 with TypeScript and Tailwind CSS
- **Backend**: Next.js API routes
- **Database**: Supabase (PostgreSQL)
- **AI**: OpenAI GPT-4 for content generation
- **Scheduling**: Node-cron for automated posting
- **Twitter API**: v2 with OAuth 2.0

## Database Schema

- `rss_feeds`: RSS feed configurations
- `posted_tweets`: Posted tweet history with analytics
- `twitter_analytics`: Historical Twitter metrics
- `user_preferences`: User settings and preferences
- `content_queue`: AI-generated content queue

## Customization

### AI Content Generation

Modify `src/lib/openai.ts` to customize:
- Tone and style of generated content
- Hook generation strategies
- Personal touch messages
- Engagement optimization

### RSS Feed Processing

Modify `src/lib/rss.ts` to customize:
- Content scoring algorithms
- Deduplication logic
- Content filtering
- Category processing

### Scheduling

Modify `src/lib/scheduler.ts` to customize:
- Posting intervals
- Content selection criteria
- Automated workflows

## Troubleshooting

### Common Issues

1. **Twitter API Rate Limits**: The app includes rate limiting protection
2. **OpenAI API Errors**: Check your API key and usage limits
3. **Supabase Connection**: Verify your project URL and keys
4. **RSS Feed Parsing**: Some feeds may require custom parsing logic

### Logs

Check the browser console and server logs for detailed error information.
