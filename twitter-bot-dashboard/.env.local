# Twitter API Configuration
TWITTER_CLIENT_ID="ZlUzVW13MHBBZXdHUktpUFVudFg6MTpjaQ"
TWITTER_CLIENT_SECRET="xgP0isOuPTQs1szA982ffshYWGcVvhqJuG2hElVgSmqWgFdWXX"
TWITTER_ACCESS_TOKEN="OXlhLUxqVE82MDktMUt3d3hpa0xMWFN2cWdzcDExQmE0QzZOMFQ5WV9WNW1lOjE3NTExODkwODExNjk6MTowOmF0OjE"
TWITTER_REFRESH_TOKEN=your_twitter_refresh_token

# Twitter API v1.1 (for additional features)
"*************************"
TWITTER_APP_SECRET="q7IuxIBapR2duTbKvg4nPnAbOi8WyqlpFAZS4ezjVrsCAO7bcu"
TWITTER_ACCESS_SECRET="qQUFCilzZxN55OmDZ52O6OPMoiKvcb9PPBcBJJXiD63xW"

# OpenAI Configuration
OPENAI_API_KEY="********************************************************************************************************************************************************************"

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL="https://fmhujzbqfzyyffgzwtzb.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZtaHVqemJxZnp5eWZmZ3p3dHpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExOTAxNTUsImV4cCI6MjA2Njc2NjE1NX0.__Hn0engp4WtLiH5O02HEps4GwU6YNwi9sF6lHQIg30"
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# App Configuration
MAX_TOPICS_TO_SELECT=4
POSTING_INTERVAL_MINUTES=30
