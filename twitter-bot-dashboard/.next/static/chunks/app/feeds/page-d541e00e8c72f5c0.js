(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[688],{8015:(e,s,t)=>{Promise.resolve().then(t.bind(t,8573))},8573:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var a=t(5155),r=t(2115),l=t(3904),n=t(7550),i=t(9946);let d=(0,i.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),c=(0,i.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),o=(0,i.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),x=(0,i.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var m=t(6874),h=t.n(m);function u(){let[e,s]=(0,r.useState)([]),[t,i]=(0,r.useState)([]),[m,u]=(0,r.useState)(!0),[g,f]=(0,r.useState)(!1),[y,p]=(0,r.useState)(!1),[b,j]=(0,r.useState)({name:"",url:""}),N=async()=>{try{let e=await fetch("/api/rss/feeds");if(e.ok){let t=await e.json();s(t)}}catch(e){console.error("Error fetching feeds:",e)}},v=async()=>{try{f(!0);let e=await fetch("/api/rss/items");if(e.ok){let s=await e.json();i(s)}}catch(e){console.error("Error fetching feed items:",e)}finally{f(!1)}},w=async e=>{e.preventDefault();try{(await fetch("/api/rss/feeds",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)})).ok&&(j({name:"",url:""}),p(!1),await N())}catch(e){console.error("Error adding feed:",e)}},k=async(e,s)=>{try{(await fetch("/api/rss/feeds/".concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_active:!s})})).ok&&await N()}catch(e){console.error("Error toggling feed:",e)}},S=async e=>{if(confirm("Are you sure you want to delete this feed?"))try{(await fetch("/api/rss/feeds/".concat(e),{method:"DELETE"})).ok&&await N()}catch(e){console.error("Error deleting feed:",e)}};return((0,r.useEffect)(()=>{(async()=>{u(!0),await Promise.all([N(),v()]),u(!1)})()},[]),m)?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(l.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading RSS feeds..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between py-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h(),{href:"/",className:"mr-4",children:(0,a.jsx)(n.A,{className:"h-6 w-6 text-gray-600 hover:text-gray-900"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"RSS Feeds"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Manage your RSS feeds and view aggregated content"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("button",{type:"button",onClick:v,disabled:g,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:[(0,a.jsx)(l.A,{className:"h-4 w-4 mr-2 ".concat(g?"animate-spin":"")}),"Refresh Content"]}),(0,a.jsxs)("button",{type:"button",onClick:()=>p(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,a.jsx)(d,{className:"h-4 w-4 mr-2"}),"Add Feed"]})]})]})})}),(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 shadow-sm",children:[(0,a.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Configured Feeds"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[e.length," feeds configured"]})]}),(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:e.map(e=>(0,a.jsx)("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900 truncate",children:e.name}),(0,a.jsx)("p",{className:"text-xs text-gray-500 truncate mt-1",children:e.url})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,a.jsx)("button",{type:"button",onClick:()=>k(e.id,e.is_active),className:"relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ".concat(e.is_active?"bg-blue-600":"bg-gray-200"),children:(0,a.jsx)("span",{className:"pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ".concat(e.is_active?"translate-x-5":"translate-x-0")})}),(0,a.jsx)("button",{type:"button",onClick:()=>S(e.id),className:"text-red-600 hover:text-red-900",children:(0,a.jsx)(c,{className:"h-4 w-4"})})]})]})},e.id))})]})}),(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 shadow-sm",children:[(0,a.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Latest Content"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[t.length," items found (deduplicated and scored)"]})]}),(0,a.jsx)("div",{className:"divide-y divide-gray-200 max-h-96 overflow-y-auto",children:t.map((e,s)=>(0,a.jsx)("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-2",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center text-xs text-gray-500 space-x-4 mb-2",children:[(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(o,{className:"h-3 w-3 mr-1"}),new Date(e.publishedAt).toLocaleDateString()]}),e.author&&(0,a.jsxs)("span",{children:["by ",e.author]}),(0,a.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded",children:["Score: ",e.score.toFixed(0)]})]}),e.categories.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-1 mb-2",children:e.categories.slice(0,3).map((e,s)=>(0,a.jsx)("span",{className:"inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded",children:e},s))}),(0,a.jsxs)("p",{className:"text-xs text-gray-600 line-clamp-2",children:[e.content.slice(0,200),"..."]})]}),(0,a.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"ml-4 text-blue-600 hover:text-blue-900",children:(0,a.jsx)(x,{className:"h-4 w-4"})})]})},s))})]})})]})}),y&&(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,a.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Add RSS Feed"}),(0,a.jsxs)("form",{onSubmit:w,children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Feed Name"}),(0,a.jsx)("input",{type:"text",value:b.name,onChange:e=>j({...b,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Feed URL"}),(0,a.jsx)("input",{type:"url",value:b.url,onChange:e=>j({...b,url:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>p(!1),className:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700",children:"Add Feed"})]})]})]})})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[425,441,684,358],()=>s(8015)),_N_E=e.O()}]);