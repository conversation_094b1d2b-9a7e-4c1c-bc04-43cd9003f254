(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[800],{7:(e,t,s)=>{Promise.resolve().then(s.bind(s,9149))},9149:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var a=s(5155),n=s(2115),r=s(3904),i=s(7550),o=s(9946);let l=(0,o.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),c=(0,o.A)("wand-sparkles",[["path",{d:"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72",key:"ul74o6"}],["path",{d:"m14 7 3 3",key:"1r5n42"}],["path",{d:"M5 6v4",key:"ilb8ba"}],["path",{d:"M19 14v4",key:"blhpug"}],["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M7 8H3",key:"zfb6yr"}],["path",{d:"M21 16h-4",key:"1cnmox"}],["path",{d:"M11 3H9",key:"1obp7u"}]]);var d=s(6874),h=s.n(d);function x(){let[e,t]=(0,n.useState)([]),[s,o]=(0,n.useState)(new Set),[d,x]=(0,n.useState)(!0),[m,u]=(0,n.useState)(new Set),[p,g]=(0,n.useState)(!1),[f,b]=(0,n.useState)(4),y=async()=>{try{x(!0);let e=await fetch("/api/content/queue");if(e.ok){let s=await e.json();t(s);let a=new Set(s.filter(e=>e.is_selected).map(e=>e.id));o(a)}}catch(e){console.error("Error fetching content queue:",e)}finally{x(!1)}},j=async s=>{let a=e.find(e=>e.id===s);if(a)try{u(e=>new Set([...e,s]));let e=await fetch("/api/content/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:a.original_title,content:a.original_content,url:a.original_url})});if(e.ok){let a=await e.json();t(e=>e.map(e=>e.id===s?{...e,ai_generated_content:a.tweetContent,short_hook:a.shortHook,long_hook:a.longHook,personal_touch:a.personalTouch}:e))}}catch(e){console.error("Error generating content:",e)}finally{u(e=>{let t=new Set(e);return t.delete(s),t})}},N=async e=>{let t=new Set(s);if(t.has(e))t.delete(e);else{if(t.size>=f)return void alert("You can only select up to ".concat(f," topics at a time."));t.add(e)}o(t);try{await fetch("/api/content/queue/".concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_selected:t.has(e)})})}catch(e){console.error("Error updating selection:",e)}},w=async()=>{if(0===s.size)return void alert("Please select at least one item to post.");try{g(!0);let e=await fetch("/api/content/post",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contentIds:Array.from(s)})});if(e.ok){let t=await e.json();alert("Successfully posted ".concat(t.posted," out of ").concat(s.size," selected items.")),await y(),o(new Set)}}catch(e){console.error("Error posting content:",e),alert("Error posting content. Please try again.")}finally{g(!1)}},v=async()=>{try{(await fetch("/api/content/refresh",{method:"POST"})).ok&&await y()}catch(e){console.error("Error refreshing queue:",e)}};return((0,n.useEffect)(()=>{y(),fetch("/api/preferences").then(e=>e.json()).then(e=>{e.max_topics_to_select&&b(e.max_topics_to_select)}).catch(console.error)},[]),d)?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(r.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading content queue..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between py-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h(),{href:"/",className:"mr-4",children:(0,a.jsx)(i.A,{className:"h-6 w-6 text-gray-600 hover:text-gray-900"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Content Queue"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["Select and generate content for posting (",s.size,"/",f," selected)"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("button",{type:"button",onClick:v,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,a.jsx)(r.A,{className:"h-4 w-4 mr-2"}),"Refresh Queue"]}),(0,a.jsxs)("button",{type:"button",onClick:w,disabled:p||0===s.size,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:[(0,a.jsx)(l,{className:"h-4 w-4 mr-2 ".concat(p?"animate-pulse":"")}),p?"Posting...":"Post Selected (".concat(s.size,")")]})]})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:e.map(e=>(0,a.jsx)("div",{className:"bg-white rounded-lg border-2 shadow-sm transition-all ".concat(s.has(e.id)?"border-blue-500 ring-2 ring-blue-200":"border-gray-200 hover:border-gray-300"," ").concat(e.is_posted?"opacity-50":""),children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-900 line-clamp-2 mb-2",children:e.original_title}),(0,a.jsxs)("div",{className:"flex items-center text-xs text-gray-500 space-x-2",children:[(0,a.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded",children:["Score: ",e.priority_score.toFixed(0)]}),e.is_posted&&(0,a.jsx)("span",{className:"bg-green-100 text-green-800 px-2 py-1 rounded",children:"Posted"})]})]}),(0,a.jsx)("input",{type:"checkbox",checked:s.has(e.id),onChange:()=>N(e.id),disabled:e.is_posted,className:"ml-4 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("p",{className:"text-xs text-gray-600 line-clamp-3",children:[e.original_content.slice(0,200),"..."]})}),e.ai_generated_content?(0,a.jsxs)("div",{className:"mb-4 p-3 bg-blue-50 rounded-lg",children:[(0,a.jsx)("h4",{className:"text-xs font-medium text-blue-900 mb-2",children:"Generated Tweet:"}),(0,a.jsx)("p",{className:"text-sm text-blue-800",children:e.ai_generated_content}),e.short_hook&&(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-blue-700",children:"Hook: "}),(0,a.jsx)("span",{className:"text-xs text-blue-600",children:e.short_hook})]})]}):(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("button",{type:"button",onClick:()=>j(e.id),disabled:m.has(e.id),className:"w-full inline-flex items-center justify-center px-3 py-2 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:[(0,a.jsx)(c,{className:"h-4 w-4 mr-2 ".concat(m.has(e.id)?"animate-spin":"")}),m.has(e.id)?"Generating...":"Generate Content"]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,a.jsx)("a",{href:e.original_url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800",children:"View Original"}),(0,a.jsx)("span",{children:new Date(e.created_at).toLocaleDateString()})]})]})},e.id))}),0===e.length&&(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("p",{className:"text-gray-500",children:"No content in queue. Refresh to load new content from RSS feeds."})})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[425,441,684,358],()=>t(7)),_N_E=e.O()}]);