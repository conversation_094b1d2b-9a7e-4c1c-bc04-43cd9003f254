const CHUNK_PUBLIC_PATH = "server/app/api/rss/items/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_@supabase_node-fetch_lib_index_a2f6920d.js");
runtime.loadChunk("server/chunks/node_modules_next_e223d9dd._.js");
runtime.loadChunk("server/chunks/node_modules_tr46_816df9d9._.js");
runtime.loadChunk("server/chunks/node_modules_ws_daabdc74._.js");
runtime.loadChunk("server/chunks/node_modules_@supabase_auth-js_dist_module_4e4e8dc6._.js");
runtime.loadChunk("server/chunks/node_modules_xmlbuilder_lib_b1c583b3._.js");
runtime.loadChunk("server/chunks/node_modules_47d3daaf._.js");
runtime.loadChunk("server/chunks/node_modules_linkedom_4d9c72e2._.js");
runtime.loadChunk("server/chunks/node_modules_6d697547._.js");
runtime.loadChunk("server/chunks/node_modules_@mozilla_readability_1297b18e._.js");
runtime.loadChunk("server/chunks/node_modules_fbb000a9._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__067e4b03._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/rss/items/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/rss/items/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/rss/items/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
