(()=>{var e={};e.id=353,e.ids=[353],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33455:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(5648),n=r.n(s),i=r(87537);let o=new(n()),a={async parseFeed(e){try{return(await o.parseURL(e)).items.map(e=>({title:e.title||"",link:e.link||"",pubDate:e.pubDate,contentSnippet:e.contentSnippet,content:e.content,guid:e.guid||e.link||"",categories:e.categories||[],author:e.author}))}catch(t){throw console.error(`Error parsing RSS feed ${e}:`,t),t}},async extractContent(e){try{let t=await (0,i.o6)(e);return t?.content||null}catch(t){return console.error(`Error extracting content from ${e}:`,t),null}},async processRSSItems(e){let t=[];for(let r of e)try{let e=await this.extractContent(r.link);e&&t.push({title:r.title,url:r.link,content:e,publishedAt:r.pubDate||new Date().toISOString(),categories:r.categories||[],author:r.author,guid:r.guid||r.link})}catch(e){console.error(`Error processing item ${r.title}:`,e)}return t},deduplicateContent(e){let t=new Set,r=[];for(let s of e){let e=s.title.toLowerCase().replace(/[^\w\s]/g,"").trim(),n=`${s.url}|${e}`;t.has(n)||(t.add(n),r.push(s))}return r},async getAggregatedContent(e){let t=[];for(let r of e)try{let e=await this.parseFeed(r),s=await this.processRSSItems(e);t.push(...s)}catch(e){console.error(`Error processing feed ${r}:`,e)}return this.deduplicateContent(t).sort((e,t)=>new Date(t.publishedAt).getTime()-new Date(e.publishedAt).getTime())},filterContent:(e,t=[])=>0===t.length?e:e.filter(e=>{let r=`${e.title} ${e.content} ${e.categories.join(" ")}`.toLowerCase();return t.some(e=>r.includes(e.toLowerCase()))}),scoreContent:e=>e.map(e=>{let t;t=0+Math.max(0,100-(Date.now()-new Date(e.publishedAt).getTime())/36e5);let r=e.title.split(" ").length;r>=5&&r<=15&&(t+=20);let s=e.content.split(" ").length;s>=200&&s<=2e3&&(t+=30);let n=["tech","technology","ai","startup","innovation","software","app"];return e.categories.some(e=>n.some(t=>e.toLowerCase().includes(t)))&&(t+=25),{...e,score:t}}).sort((e,t)=>t.score-e.score)}},34631:e=>{"use strict";e.exports=require("tls")},35701:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{POST:()=>l});var n=r(96559),i=r(48088),o=r(37719),a=r(32190),c=r(56621),u=r(33455);async function l(){try{let e=(await c.J0.getRSSFeeds()).filter(e=>e.is_active);if(0===e.length)return a.NextResponse.json({message:"No active RSS feeds found"});let t=e.map(e=>e.url),r=await u.A.getAggregatedContent(t),s=u.A.scoreContent(r),n=await c.J0.getContentQueue(1e3),i=new Set(n.map(e=>e.original_url)),o=0;for(let t of s.slice(0,20))if(!i.has(t.url))try{let r=e.find(e=>t.url.includes(new URL(e.url).hostname))?.id;await c.J0.addToContentQueue({original_url:t.url,original_title:t.title,original_content:t.content,rss_feed_id:r,is_selected:!1,is_posted:!1,priority_score:t.score}),o++}catch(e){console.error(`Error adding content item: ${t.title}`,e)}return a.NextResponse.json({message:`Added ${o} new items to content queue`,totalProcessed:s.length,added:o})}catch(e){return console.error("Error refreshing content queue:",e),a.NextResponse.json({error:"Failed to refresh content queue"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/content/refresh/route",pathname:"/api/content/refresh",filename:"route",bundlePath:"app/api/content/refresh/route"},resolvedPagePath:"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/refresh/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:f}=d;function w(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}},39727:()=>{},41204:e=>{"use strict";e.exports=require("string_decoder")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},48817:e=>{"use strict";e.exports=require("postcss")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{J0:()=>a});var s=r(66437);let n="https://fmhujzbqfzyyffgzwtzb.supabase.co",i=process.env.SUPABASE_SERVICE_ROLE_KEY,o=(0,s.UU)(n,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZtaHVqemJxZnp5eWZmZ3p3dHpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExOTAxNTUsImV4cCI6MjA2Njc2NjE1NX0.__Hn0engp4WtLiH5O02HEps4GwU6YNwi9sF6lHQIg30");(0,s.UU)(n,i);let a={async getRSSFeeds(){let{data:e,error:t}=await o.from("rss_feeds").select("*").order("created_at",{ascending:!1});if(t)throw t;return e},async addRSSFeed(e,t){let{data:r,error:s}=await o.from("rss_feeds").insert({name:e,url:t}).select().single();if(s)throw s;return r},async updateRSSFeed(e,t){let{data:r,error:s}=await o.from("rss_feeds").update({...t,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(s)throw s;return r},async getPostedTweets(e=50){let{data:t,error:r}=await o.from("posted_tweets").select("*").order("posted_at",{ascending:!1}).limit(e);if(r)throw r;return t},async addPostedTweet(e){let{data:t,error:r}=await o.from("posted_tweets").insert(e).select().single();if(r)throw r;return t},async getLatestAnalytics(){let{data:e,error:t}=await o.from("twitter_analytics").select("*").order("recorded_at",{ascending:!1}).limit(1).single();if(t&&"PGRST116"!==t.code)throw t;return e},async addAnalytics(e){let{data:t,error:r}=await o.from("twitter_analytics").insert(e).select().single();if(r)throw r;return t},async getUserPreferences(){let{data:e,error:t}=await o.from("user_preferences").select("*").limit(1).single();if(t&&"PGRST116"!==t.code)throw t;return e},async updateUserPreferences(e){let{data:t,error:r}=await o.from("user_preferences").update({...e,updated_at:new Date().toISOString()}).select().single();if(r)throw r;return t},async getContentQueue(e=20){let{data:t,error:r}=await o.from("content_queue").select("*").order("created_at",{ascending:!1}).limit(e);if(r)throw r;return t},async addToContentQueue(e){let{data:t,error:r}=await o.from("content_queue").insert(e).select().single();if(r)throw r;return t},async updateContentQueue(e,t){let{data:r,error:s}=await o.from("content_queue").update({...t,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(s)throw s;return r},async getSelectedContent(){let{data:e,error:t}=await o.from("content_queue").select("*").eq("is_selected",!0).eq("is_posted",!1).order("priority_score",{ascending:!1});if(t)throw t;return e},async markContentAsPosted(e){let{data:t,error:r}=await o.from("content_queue").update({is_posted:!0,is_selected:!1,updated_at:new Date().toISOString()}).in("id",e).select();if(r)throw r;return t},async deleteRSSFeed(e){let{error:t}=await o.from("rss_feeds").delete().eq("id",e);if(t)throw t;return!0}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66136:e=>{"use strict";e.exports=require("timers")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,437,38],()=>r(35701));module.exports=s})();