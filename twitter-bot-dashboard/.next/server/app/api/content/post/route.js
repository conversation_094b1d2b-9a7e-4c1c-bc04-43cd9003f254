(()=>{var e={};e.id=598,e.ids=[598],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},48592:(e,t,r)=>{"use strict";r.d(t,{e$:()=>n});var s=r(73395);function i(){if(!process.env.TWITTER_APP_KEY||!process.env.TWITTER_APP_SECRET||!process.env.TWITTER_ACCESS_TOKEN||!process.env.TWITTER_ACCESS_SECRET)throw Error("Twitter API keys not configured");return new s.Yl({appKey:process.env.TWITTER_APP_KEY,appSecret:process.env.TWITTER_APP_SECRET,accessToken:process.env.TWITTER_ACCESS_TOKEN,accessSecret:process.env.TWITTER_ACCESS_SECRET})}let n={async getCurrentUser(){try{let e=i(),t=await e.v2.me({"user.fields":["public_metrics","verified","profile_image_url"]});return{id:t.data.id,username:t.data.username,name:t.data.name,followers_count:t.data.public_metrics?.followers_count||0,following_count:t.data.public_metrics?.following_count||0,tweet_count:t.data.public_metrics?.tweet_count||0,verified:t.data.verified||!1,profile_image_url:t.data.profile_image_url}}catch(e){throw console.error("Error fetching current user:",e),e}},async postTweet(e){try{let t=i();return{id:(await t.v2.tweet(e)).data.id,text:e,created_at:new Date().toISOString()}}catch(e){throw console.error("Error posting tweet:",e),e}},async getRecentTweets(){try{return[]}catch(e){return console.error("Error fetching recent tweets:",e),[]}},async getTweetAnalytics(e){try{let t=i(),r=await t.v2.singleTweet(e,{"tweet.fields":["created_at","public_metrics"]});if(!r.data)return null;return{id:r.data.id,text:r.data.text,created_at:r.data.created_at||new Date().toISOString(),public_metrics:r.data.public_metrics}}catch(e){return console.error("Error fetching tweet analytics:",e),null}},async getTotalImpressions(){try{let e=await this.getRecentTweets(),t=new Date;t.setDate(t.getDate()-30);let r=e.filter(e=>new Date(e.created_at)>=t),s=0;for(let e of r)if(e.public_metrics?.impression_count)s+=e.public_metrics.impression_count;else if(e.public_metrics){let t=(e.public_metrics.like_count||0)+(e.public_metrics.retweet_count||0)+(e.public_metrics.reply_count||0);s+=Math.max(10*t,100)}return s}catch(e){return console.error("Error calculating total impressions:",e),0}},async getAuthStatus(){try{return await this.getCurrentUser(),{authenticated:!0}}catch(e){return console.error("Error checking auth status:",e),{authenticated:!1}}}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{J0:()=>o});var s=r(66437);let i="https://fmhujzbqfzyyffgzwtzb.supabase.co",n=process.env.SUPABASE_SERVICE_ROLE_KEY,a=(0,s.UU)(i,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZtaHVqemJxZnp5eWZmZ3p3dHpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExOTAxNTUsImV4cCI6MjA2Njc2NjE1NX0.__Hn0engp4WtLiH5O02HEps4GwU6YNwi9sF6lHQIg30");(0,s.UU)(i,n);let o={async getRSSFeeds(){let{data:e,error:t}=await a.from("rss_feeds").select("*").order("created_at",{ascending:!1});if(t)throw t;return e},async addRSSFeed(e,t){let{data:r,error:s}=await a.from("rss_feeds").insert({name:e,url:t}).select().single();if(s)throw s;return r},async updateRSSFeed(e,t){let{data:r,error:s}=await a.from("rss_feeds").update({...t,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(s)throw s;return r},async getPostedTweets(e=50){let{data:t,error:r}=await a.from("posted_tweets").select("*").order("posted_at",{ascending:!1}).limit(e);if(r)throw r;return t},async addPostedTweet(e){let{data:t,error:r}=await a.from("posted_tweets").insert(e).select().single();if(r)throw r;return t},async getLatestAnalytics(){let{data:e,error:t}=await a.from("twitter_analytics").select("*").order("recorded_at",{ascending:!1}).limit(1).single();if(t&&"PGRST116"!==t.code)throw t;return e},async addAnalytics(e){let{data:t,error:r}=await a.from("twitter_analytics").insert(e).select().single();if(r)throw r;return t},async getUserPreferences(){let{data:e,error:t}=await a.from("user_preferences").select("*").limit(1).single();if(t&&"PGRST116"!==t.code)throw t;return e},async updateUserPreferences(e){let{data:t,error:r}=await a.from("user_preferences").update({...e,updated_at:new Date().toISOString()}).select().single();if(r)throw r;return t},async getContentQueue(e=20){let{data:t,error:r}=await a.from("content_queue").select("*").order("created_at",{ascending:!1}).limit(e);if(r)throw r;return t},async addToContentQueue(e){let{data:t,error:r}=await a.from("content_queue").insert(e).select().single();if(r)throw r;return t},async updateContentQueue(e,t){let{data:r,error:s}=await a.from("content_queue").update({...t,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(s)throw s;return r},async getSelectedContent(){let{data:e,error:t}=await a.from("content_queue").select("*").eq("is_selected",!0).eq("is_posted",!1).order("priority_score",{ascending:!1});if(t)throw t;return e},async markContentAsPosted(e){let{data:t,error:r}=await a.from("content_queue").update({is_posted:!0,is_selected:!1,updated_at:new Date().toISOString()}).in("id",e).select();if(r)throw r;return t},async deleteRSSFeed(e){let{error:t}=await a.from("rss_feeds").delete().eq("id",e);if(t)throw t;return!0}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85896:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>w,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>_});var s={};r.r(s),r.d(s,{POST:()=>l});var i=r(96559),n=r(48088),a=r(37719),o=r(32190),c=r(56621),u=r(48592);async function l(e){try{let{contentIds:t}=await e.json();if(!t||!Array.isArray(t)||0===t.length)return o.NextResponse.json({error:"Content IDs array is required"},{status:400});let r=(await c.J0.getContentQueue(100)).filter(e=>t.includes(e.id)&&!e.is_posted);if(0===r.length)return o.NextResponse.json({error:"No valid content items found"},{status:400});let s=0,i=[];for(let e of r)try{let t=e.ai_generated_content||`${e.original_title}

${e.original_url}`,n=await u.e$.postTweet(t);await c.J0.addPostedTweet({tweet_id:n.id,content:t,original_url:e.original_url,original_title:e.original_title,impressions:0,retweets:0,likes:0,replies:0,posted_at:n.created_at}),s++,i.push({id:e.id,success:!0,tweetId:n.id}),s<r.length&&await new Promise(e=>setTimeout(e,2e3))}catch(t){console.error(`Error posting content ${e.id}:`,t),i.push({id:e.id,success:!1,error:t instanceof Error?t.message:"Unknown error"})}let n=i.filter(e=>e.success).map(e=>e.id);return n.length>0&&await c.J0.markContentAsPosted(n),o.NextResponse.json({posted:s,total:r.length,results:i})}catch(e){return console.error("Error posting content:",e),o.NextResponse.json({error:"Failed to post content"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/content/post/route",pathname:"/api/content/post",filename:"route",bundlePath:"app/api/content/post/route"},resolvedPagePath:"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/post/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:_,serverHooks:w}=d;function f(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:_})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,437,395],()=>r(85896));module.exports=s})();