(()=>{var e={};e.id=957,e.ids=[957],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33509:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let n=new(r(40694)).Ay({apiKey:process.env.OPENAI_API_KEY}),a={async generateTweetContent(e,t,r,a={}){let{tone:s="analytical",includePersonalTouch:o=!0,maxLength:i=280}=a;try{let a=`
You are an expert social media content creator specializing in tech and startup content. 
Create engaging Twitter content based on this article:

Title: ${e}
Content: ${t.slice(0,2e3)}
URL: ${r}

Requirements:
1. Generate a SHORT HOOK (1-2 sentences, max 50 characters) that grabs attention
2. Generate a LONG HOOK (2-3 sentences, max 100 characters) that provides more context
3. Generate a PERSONAL TOUCH message that adds your unique perspective or insight
4. Create the final TWEET CONTENT (max ${i} characters) combining the best elements
5. Suggest relevant hashtags

Tone: ${s}
Include personal perspective: ${o}

Format your response as JSON:
{
  "shortHook": "...",
  "longHook": "...",
  "personalTouch": "...",
  "tweetContent": "...",
  "hashtags": ["hashtag1", "hashtag2", "hashtag3"]
}

Best practices for maximum engagement:
- Start with a compelling hook
- Use numbers, statistics, or surprising facts when available
- Ask questions or create curiosity gaps
- Include actionable insights
- Use power words and emotional triggers
- Keep it conversational and authentic
- Add your unique perspective or hot take
`,c=await n.chat.completions.create({model:"gpt-4o",messages:[{role:"user",content:a}],max_tokens:500,temperature:.7}),g=c.choices[0].message.content?.trim();if(!g)throw Error("No response from OpenAI");let l=JSON.parse(g);return{shortHook:l.shortHook||"",longHook:l.longHook||"",personalTouch:l.personalTouch||"",tweetContent:l.tweetContent||"",hashtags:Array.isArray(l.hashtags)?l.hashtags:[]}}catch(n){return console.error("Error generating tweet content:",n),this.generateFallbackContent(e,t,r,a)}},async generateTweetVariations(e,t,r,n=3,a={}){let s=[];for(let o=0;o<n;o++)try{let n=await this.generateTweetContent(e,t,r,{...a,tone:["analytical","casual","enthusiastic"][o%3]});s.push(n)}catch(e){console.error(`Error generating variation ${o+1}:`,e)}return s},async generateContentSummary(e,t){try{let r=`
Summarize this article in 2-3 sentences, focusing on the key insights and takeaways:

Title: ${e}
Content: ${t.slice(0,1500)}

Make it engaging and highlight what makes this newsworthy or interesting.
`,a=await n.chat.completions.create({model:"gpt-4o",messages:[{role:"user",content:r}],max_tokens:150,temperature:.5});return a.choices[0].message.content?.trim()||"Summary not available"}catch(e){return console.error("Error generating summary:",e),"Summary not available"}},async analyzeEngagementPotential(e,t){try{let r=`
Analyze this content for Twitter engagement potential:

Title: ${e}
Content: ${t.slice(0,1e3)}

Rate the engagement potential (1-10) and provide:
1. Key factors that make it engaging or not
2. Suggestions to improve engagement

Format as JSON:
{
  "score": 7,
  "factors": ["factor1", "factor2"],
  "suggestions": ["suggestion1", "suggestion2"]
}
`,a=await n.chat.completions.create({model:"gpt-4o",messages:[{role:"user",content:r}],max_tokens:300,temperature:.3}),s=a.choices[0].message.content?.trim();if(!s)throw Error("No response from OpenAI");return JSON.parse(s)}catch(e){return console.error("Error analyzing engagement potential:",e),{score:5,factors:["Unable to analyze"],suggestions:["Try again later"]}}},generateFallbackContent(e,t,r,n={}){let a=e.slice(0,50)+(e.length>50?"...":""),s=`Interesting read: ${e.slice(0,80)}${e.length>80?"...":""}`,o="Worth checking out this perspective on the latest tech developments.",i=`${a}

${o}

${r}`;return{shortHook:a,longHook:s,personalTouch:o,tweetContent:i.length>(n.maxLength||280)?`${e.slice(0,200)}...

${r}`:i,hashtags:["tech","news","startup"]}}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},86033:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>p});var n={};r.r(n),r.d(n,{POST:()=>g});var a=r(96559),s=r(48088),o=r(37719),i=r(32190),c=r(33509);async function g(e){try{let{title:t,content:r,url:n,options:a={}}=await e.json();if(!t||!r||!n)return i.NextResponse.json({error:"Title, content, and URL are required"},{status:400});let s=await c.A.generateTweetContent(t,r,n,a);return i.NextResponse.json(s)}catch(e){return console.error("Error generating content:",e),i.NextResponse.json({error:"Failed to generate content"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/content/generate/route",pathname:"/api/content/generate",filename:"route",bundlePath:"app/api/content/generate/route"},resolvedPagePath:"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/generate/route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:u,workUnitAsyncStorage:p,serverHooks:h}=l;function m(){return(0,o.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:p})}},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,580,694],()=>r(86033));module.exports=n})();