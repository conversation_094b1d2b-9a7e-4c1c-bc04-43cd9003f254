{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/content/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { ArrowLeft, RefreshCw, Wand2, Send } from 'lucide-react'\nimport Link from 'next/link'\n\ninterface ContentItem {\n  id: string\n  original_url: string\n  original_title: string\n  original_content: string\n  ai_generated_content?: string\n  short_hook?: string\n  long_hook?: string\n  personal_touch?: string\n  is_selected: boolean\n  is_posted: boolean\n  priority_score: number\n  created_at: string\n}\n\ninterface GeneratedContent {\n  shortHook: string\n  longHook: string\n  personalTouch: string\n  tweetContent: string\n  hashtags: string[]\n}\n\nexport default function ContentPage() {\n  const [contentQueue, setContentQueue] = useState<ContentItem[]>([])\n  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set())\n  const [loading, setLoading] = useState(true)\n  const [generating, setGenerating] = useState<Set<string>>(new Set())\n  const [posting, setPosting] = useState(false)\n  const [maxTopics, setMaxTopics] = useState(4)\n\n  const fetchContentQueue = async () => {\n    try {\n      setLoading(true)\n      const response = await fetch('/api/content/queue')\n      if (response.ok) {\n        const data = await response.json()\n        setContentQueue(data)\n        \n        // Initialize selected items from database\n        const selected = new Set<string>(\n          data.filter((item: ContentItem) => item.is_selected).map((item: ContentItem) => item.id)\n        )\n        setSelectedItems(selected)\n      }\n    } catch (error) {\n      console.error('Error fetching content queue:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const generateContent = async (itemId: string) => {\n    const item = contentQueue.find(i => i.id === itemId)\n    if (!item) return\n\n    try {\n      setGenerating(prev => new Set([...prev, itemId]))\n      \n      const response = await fetch('/api/content/generate', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          title: item.original_title,\n          content: item.original_content,\n          url: item.original_url\n        })\n      })\n      \n      if (response.ok) {\n        const generatedContent: GeneratedContent = await response.json()\n        \n        // Update the item in the queue\n        setContentQueue(prev => prev.map(i => \n          i.id === itemId \n            ? {\n                ...i,\n                ai_generated_content: generatedContent.tweetContent,\n                short_hook: generatedContent.shortHook,\n                long_hook: generatedContent.longHook,\n                personal_touch: generatedContent.personalTouch\n              }\n            : i\n        ))\n      }\n    } catch (error) {\n      console.error('Error generating content:', error)\n    } finally {\n      setGenerating(prev => {\n        const newSet = new Set(prev)\n        newSet.delete(itemId)\n        return newSet\n      })\n    }\n  }\n\n  const toggleSelection = async (itemId: string) => {\n    const newSelected = new Set(selectedItems)\n    \n    if (newSelected.has(itemId)) {\n      newSelected.delete(itemId)\n    } else {\n      // Enforce max topics limit\n      if (newSelected.size >= maxTopics) {\n        alert(`You can only select up to ${maxTopics} topics at a time.`)\n        return\n      }\n      newSelected.add(itemId)\n    }\n    \n    setSelectedItems(newSelected)\n    \n    // Update in database\n    try {\n      await fetch(`/api/content/queue/${itemId}`, {\n        method: 'PATCH',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ is_selected: newSelected.has(itemId) })\n      })\n    } catch (error) {\n      console.error('Error updating selection:', error)\n    }\n  }\n\n  const postSelectedContent = async () => {\n    if (selectedItems.size === 0) {\n      alert('Please select at least one item to post.')\n      return\n    }\n\n    try {\n      setPosting(true)\n      const response = await fetch('/api/content/post', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ \n          contentIds: Array.from(selectedItems) \n        })\n      })\n      \n      if (response.ok) {\n        const result = await response.json()\n        alert(`Successfully posted ${result.posted} out of ${selectedItems.size} selected items.`)\n        \n        // Refresh the queue\n        await fetchContentQueue()\n        setSelectedItems(new Set())\n      }\n    } catch (error) {\n      console.error('Error posting content:', error)\n      alert('Error posting content. Please try again.')\n    } finally {\n      setPosting(false)\n    }\n  }\n\n  const refreshQueue = async () => {\n    try {\n      const response = await fetch('/api/content/refresh', {\n        method: 'POST'\n      })\n      \n      if (response.ok) {\n        await fetchContentQueue()\n      }\n    } catch (error) {\n      console.error('Error refreshing queue:', error)\n    }\n  }\n\n  useEffect(() => {\n    fetchContentQueue()\n    \n    // Fetch user preferences for max topics\n    fetch('/api/preferences')\n      .then(res => res.json())\n      .then(data => {\n        if (data.max_topics_to_select) {\n          setMaxTopics(data.max_topics_to_select)\n        }\n      })\n      .catch(console.error)\n  }, [])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <RefreshCw className=\"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600\" />\n          <p className=\"text-gray-600\">Loading content queue...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between py-6\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"mr-4\">\n                <ArrowLeft className=\"h-6 w-6 text-gray-600 hover:text-gray-900\" />\n              </Link>\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">Content Queue</h1>\n                <p className=\"text-sm text-gray-600 mt-1\">\n                  Select and generate content for posting ({selectedItems.size}/{maxTopics} selected)\n                </p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button\n                type=\"button\"\n                onClick={refreshQueue}\n                className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                <RefreshCw className=\"h-4 w-4 mr-2\" />\n                Refresh Queue\n              </button>\n              <button\n                type=\"button\"\n                onClick={postSelectedContent}\n                disabled={posting || selectedItems.size === 0}\n                className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50\"\n              >\n                <Send className={`h-4 w-4 mr-2 ${posting ? 'animate-pulse' : ''}`} />\n                {posting ? 'Posting...' : `Post Selected (${selectedItems.size})`}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Content Grid */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\">\n          {contentQueue.map((item) => (\n            <div\n              key={item.id}\n              className={`bg-white rounded-lg border-2 shadow-sm transition-all ${\n                selectedItems.has(item.id)\n                  ? 'border-blue-500 ring-2 ring-blue-200'\n                  : 'border-gray-200 hover:border-gray-300'\n              } ${item.is_posted ? 'opacity-50' : ''}`}\n            >\n              <div className=\"p-6\">\n                {/* Header */}\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-sm font-semibold text-gray-900 line-clamp-2 mb-2\">\n                      {item.original_title}\n                    </h3>\n                    <div className=\"flex items-center text-xs text-gray-500 space-x-2\">\n                      <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded\">\n                        Score: {item.priority_score.toFixed(0)}\n                      </span>\n                      {item.is_posted && (\n                        <span className=\"bg-green-100 text-green-800 px-2 py-1 rounded\">\n                          Posted\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                  <input\n                    type=\"checkbox\"\n                    checked={selectedItems.has(item.id)}\n                    onChange={() => toggleSelection(item.id)}\n                    disabled={item.is_posted}\n                    className=\"ml-4 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                </div>\n\n                {/* Original Content Preview */}\n                <div className=\"mb-4\">\n                  <p className=\"text-xs text-gray-600 line-clamp-3\">\n                    {item.original_content.slice(0, 200)}...\n                  </p>\n                </div>\n\n                {/* AI Generated Content */}\n                {item.ai_generated_content ? (\n                  <div className=\"mb-4 p-3 bg-blue-50 rounded-lg\">\n                    <h4 className=\"text-xs font-medium text-blue-900 mb-2\">Generated Tweet:</h4>\n                    <p className=\"text-sm text-blue-800\">{item.ai_generated_content}</p>\n                    \n                    {item.short_hook && (\n                      <div className=\"mt-2\">\n                        <span className=\"text-xs font-medium text-blue-700\">Hook: </span>\n                        <span className=\"text-xs text-blue-600\">{item.short_hook}</span>\n                      </div>\n                    )}\n                  </div>\n                ) : (\n                  <div className=\"mb-4\">\n                    <button\n                      type=\"button\"\n                      onClick={() => generateContent(item.id)}\n                      disabled={generating.has(item.id)}\n                      className=\"w-full inline-flex items-center justify-center px-3 py-2 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\"\n                    >\n                      <Wand2 className={`h-4 w-4 mr-2 ${generating.has(item.id) ? 'animate-spin' : ''}`} />\n                      {generating.has(item.id) ? 'Generating...' : 'Generate Content'}\n                    </button>\n                  </div>\n                )}\n\n                {/* Actions */}\n                <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                  <a\n                    href={item.original_url}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"text-blue-600 hover:text-blue-800\"\n                  >\n                    View Original\n                  </a>\n                  <span>{new Date(item.created_at).toLocaleDateString()}</span>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {contentQueue.length === 0 && (\n          <div className=\"text-center py-12\">\n            <p className=\"text-gray-500\">No content in queue. Refresh to load new content from RSS feeds.</p>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AA6Be,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,oBAAoB;QACxB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,gBAAgB;gBAEhB,0CAA0C;gBAC1C,MAAM,WAAW,IAAI,IACnB,KAAK,MAAM,CAAC,CAAC,OAAsB,KAAK,WAAW,EAAE,GAAG,CAAC,CAAC,OAAsB,KAAK,EAAE;gBAEzF,iBAAiB;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,MAAM,OAAO,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7C,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,cAAc,CAAA,OAAQ,IAAI,IAAI;uBAAI;oBAAM;iBAAO;YAE/C,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,KAAK,cAAc;oBAC1B,SAAS,KAAK,gBAAgB;oBAC9B,KAAK,KAAK,YAAY;gBACxB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,mBAAqC,MAAM,SAAS,IAAI;gBAE9D,+BAA+B;gBAC/B,gBAAgB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAC/B,EAAE,EAAE,KAAK,SACL;4BACE,GAAG,CAAC;4BACJ,sBAAsB,iBAAiB,YAAY;4BACnD,YAAY,iBAAiB,SAAS;4BACtC,WAAW,iBAAiB,QAAQ;4BACpC,gBAAgB,iBAAiB,aAAa;wBAChD,IACA;YAER;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,cAAc,CAAA;gBACZ,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,MAAM,CAAC;gBACd,OAAO;YACT;QACF;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,MAAM,cAAc,IAAI,IAAI;QAE5B,IAAI,YAAY,GAAG,CAAC,SAAS;YAC3B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,2BAA2B;YAC3B,IAAI,YAAY,IAAI,IAAI,WAAW;gBACjC,MAAM,CAAC,0BAA0B,EAAE,UAAU,kBAAkB,CAAC;gBAChE;YACF;YACA,YAAY,GAAG,CAAC;QAClB;QAEA,iBAAiB;QAEjB,qBAAqB;QACrB,IAAI;YACF,MAAM,MAAM,CAAC,mBAAmB,EAAE,QAAQ,EAAE;gBAC1C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,aAAa,YAAY,GAAG,CAAC;gBAAQ;YAC9D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,cAAc,IAAI,KAAK,GAAG;YAC5B,MAAM;YACN;QACF;QAEA,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,YAAY,MAAM,IAAI,CAAC;gBACzB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,MAAM,CAAC,oBAAoB,EAAE,OAAO,MAAM,CAAC,QAAQ,EAAE,cAAc,IAAI,CAAC,gBAAgB,CAAC;gBAEzF,oBAAoB;gBACpB,MAAM;gBACN,iBAAiB,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,wCAAwC;QACxC,MAAM,oBACH,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,IACpB,IAAI,CAAC,CAAA;YACJ,IAAI,KAAK,oBAAoB,EAAE;gBAC7B,aAAa,KAAK,oBAAoB;YACxC;QACF,GACC,KAAK,CAAC,QAAQ,KAAK;IACxB,GAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDACvB,cAAA,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;oDAA6B;oDACE,cAAc,IAAI;oDAAC;oDAAE;oDAAU;;;;;;;;;;;;;;;;;;;0CAI/E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU,WAAW,cAAc,IAAI,KAAK;wCAC5C,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAW,CAAC,aAAa,EAAE,UAAU,kBAAkB,IAAI;;;;;;4CAChE,UAAU,eAAe,CAAC,eAAe,EAAE,cAAc,IAAI,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;gCAEC,WAAW,CAAC,sDAAsD,EAChE,cAAc,GAAG,CAAC,KAAK,EAAE,IACrB,yCACA,wCACL,CAAC,EAAE,KAAK,SAAS,GAAG,eAAe,IAAI;0CAExC,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,KAAK,cAAc;;;;;;sEAEtB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;;wEAA8C;wEACpD,KAAK,cAAc,CAAC,OAAO,CAAC;;;;;;;gEAErC,KAAK,SAAS,kBACb,8OAAC;oEAAK,WAAU;8EAAgD;;;;;;;;;;;;;;;;;;8DAMtE,8OAAC;oDACC,MAAK;oDACL,SAAS,cAAc,GAAG,CAAC,KAAK,EAAE;oDAClC,UAAU,IAAM,gBAAgB,KAAK,EAAE;oDACvC,UAAU,KAAK,SAAS;oDACxB,WAAU;;;;;;;;;;;;sDAKd,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;;oDACV,KAAK,gBAAgB,CAAC,KAAK,CAAC,GAAG;oDAAK;;;;;;;;;;;;wCAKxC,KAAK,oBAAoB,iBACxB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,8OAAC;oDAAE,WAAU;8DAAyB,KAAK,oBAAoB;;;;;;gDAE9D,KAAK,UAAU,kBACd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAoC;;;;;;sEACpD,8OAAC;4DAAK,WAAU;sEAAyB,KAAK,UAAU;;;;;;;;;;;;;;;;;iEAK9D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,gBAAgB,KAAK,EAAE;gDACtC,UAAU,WAAW,GAAG,CAAC,KAAK,EAAE;gDAChC,WAAU;;kEAEV,8OAAC,+MAAA,CAAA,QAAK;wDAAC,WAAW,CAAC,aAAa,EAAE,WAAW,GAAG,CAAC,KAAK,EAAE,IAAI,iBAAiB,IAAI;;;;;;oDAChF,WAAW,GAAG,CAAC,KAAK,EAAE,IAAI,kBAAkB;;;;;;;;;;;;sDAMnD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAM,KAAK,YAAY;oDACvB,QAAO;oDACP,KAAI;oDACJ,WAAU;8DACX;;;;;;8DAGD,8OAAC;8DAAM,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;+BA9ElD,KAAK,EAAE;;;;;;;;;;oBAqFjB,aAAa,MAAM,KAAK,mBACvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}]}