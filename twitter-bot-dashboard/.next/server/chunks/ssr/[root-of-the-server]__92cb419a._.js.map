{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/feeds/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { ArrowLeft, Plus, Trash2, RefreshCw, ExternalLink, Calendar } from 'lucide-react'\nimport Link from 'next/link'\n\ninterface RSSFeed {\n  id: string\n  name: string\n  url: string\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\ninterface FeedItem {\n  title: string\n  url: string\n  publishedAt: string\n  content: string\n  categories: string[]\n  author?: string\n  score: number\n}\n\nexport default function FeedsPage() {\n  const [feeds, setFeeds] = useState<RSSFeed[]>([])\n  const [feedItems, setFeedItems] = useState<FeedItem[]>([])\n  const [loading, setLoading] = useState(true)\n  const [refreshing, setRefreshing] = useState(false)\n  const [showAddForm, setShowAddForm] = useState(false)\n  const [newFeed, setNewFeed] = useState({ name: '', url: '' })\n\n  const fetchFeeds = async () => {\n    try {\n      const response = await fetch('/api/rss/feeds')\n      if (response.ok) {\n        const data = await response.json()\n        setFeeds(data)\n      }\n    } catch (error) {\n      console.error('Error fetching feeds:', error)\n    }\n  }\n\n  const fetchFeedItems = async () => {\n    try {\n      setRefreshing(true)\n      const response = await fetch('/api/rss/items')\n      if (response.ok) {\n        const data = await response.json()\n        setFeedItems(data)\n      }\n    } catch (error) {\n      console.error('Error fetching feed items:', error)\n    } finally {\n      setRefreshing(false)\n    }\n  }\n\n  const addFeed = async (e: React.FormEvent) => {\n    e.preventDefault()\n    try {\n      const response = await fetch('/api/rss/feeds', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(newFeed)\n      })\n      \n      if (response.ok) {\n        setNewFeed({ name: '', url: '' })\n        setShowAddForm(false)\n        await fetchFeeds()\n      }\n    } catch (error) {\n      console.error('Error adding feed:', error)\n    }\n  }\n\n  const toggleFeed = async (id: string, isActive: boolean) => {\n    try {\n      const response = await fetch(`/api/rss/feeds/${id}`, {\n        method: 'PATCH',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ is_active: !isActive })\n      })\n      \n      if (response.ok) {\n        await fetchFeeds()\n      }\n    } catch (error) {\n      console.error('Error toggling feed:', error)\n    }\n  }\n\n  const deleteFeed = async (id: string) => {\n    if (!confirm('Are you sure you want to delete this feed?')) return\n    \n    try {\n      const response = await fetch(`/api/rss/feeds/${id}`, {\n        method: 'DELETE'\n      })\n      \n      if (response.ok) {\n        await fetchFeeds()\n      }\n    } catch (error) {\n      console.error('Error deleting feed:', error)\n    }\n  }\n\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true)\n      await Promise.all([fetchFeeds(), fetchFeedItems()])\n      setLoading(false)\n    }\n    loadData()\n  }, [])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <RefreshCw className=\"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600\" />\n          <p className=\"text-gray-600\">Loading RSS feeds...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between py-6\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"mr-4\">\n                <ArrowLeft className=\"h-6 w-6 text-gray-600 hover:text-gray-900\" />\n              </Link>\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">RSS Feeds</h1>\n                <p className=\"text-sm text-gray-600 mt-1\">\n                  Manage your RSS feeds and view aggregated content\n                </p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button\n                type=\"button\"\n                onClick={fetchFeedItems}\n                disabled={refreshing}\n                className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\"\n              >\n                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />\n                Refresh Content\n              </button>\n              <button\n                type=\"button\"\n                onClick={() => setShowAddForm(true)}\n                className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Add Feed\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* RSS Feeds List */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-lg border border-gray-200 shadow-sm\">\n              <div className=\"p-6 border-b border-gray-200\">\n                <h2 className=\"text-lg font-semibold text-gray-900\">Configured Feeds</h2>\n                <p className=\"text-sm text-gray-600 mt-1\">{feeds.length} feeds configured</p>\n              </div>\n              <div className=\"divide-y divide-gray-200\">\n                {feeds.map((feed) => (\n                  <div key={feed.id} className=\"p-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex-1 min-w-0\">\n                        <h3 className=\"text-sm font-medium text-gray-900 truncate\">\n                          {feed.name}\n                        </h3>\n                        <p className=\"text-xs text-gray-500 truncate mt-1\">\n                          {feed.url}\n                        </p>\n                      </div>\n                      <div className=\"flex items-center space-x-2 ml-4\">\n                        <button\n                          type=\"button\"\n                          onClick={() => toggleFeed(feed.id, feed.is_active)}\n                          className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${\n                            feed.is_active ? 'bg-blue-600' : 'bg-gray-200'\n                          }`}\n                        >\n                          <span\n                            className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${\n                              feed.is_active ? 'translate-x-5' : 'translate-x-0'\n                            }`}\n                          />\n                        </button>\n                        <button\n                          type=\"button\"\n                          onClick={() => deleteFeed(feed.id)}\n                          className=\"text-red-600 hover:text-red-900\"\n                        >\n                          <Trash2 className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Feed Items */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white rounded-lg border border-gray-200 shadow-sm\">\n              <div className=\"p-6 border-b border-gray-200\">\n                <h2 className=\"text-lg font-semibold text-gray-900\">Latest Content</h2>\n                <p className=\"text-sm text-gray-600 mt-1\">\n                  {feedItems.length} items found (deduplicated and scored)\n                </p>\n              </div>\n              <div className=\"divide-y divide-gray-200 max-h-96 overflow-y-auto\">\n                {feedItems.map((item, index) => (\n                  <div key={index} className=\"p-4\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1 min-w-0\">\n                        <h3 className=\"text-sm font-medium text-gray-900 mb-2\">\n                          {item.title}\n                        </h3>\n                        <div className=\"flex items-center text-xs text-gray-500 space-x-4 mb-2\">\n                          <span className=\"flex items-center\">\n                            <Calendar className=\"h-3 w-3 mr-1\" />\n                            {new Date(item.publishedAt).toLocaleDateString()}\n                          </span>\n                          {item.author && <span>by {item.author}</span>}\n                          <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded\">\n                            Score: {item.score.toFixed(0)}\n                          </span>\n                        </div>\n                        {item.categories.length > 0 && (\n                          <div className=\"flex flex-wrap gap-1 mb-2\">\n                            {item.categories.slice(0, 3).map((category, idx) => (\n                              <span\n                                key={idx}\n                                className=\"inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded\"\n                              >\n                                {category}\n                              </span>\n                            ))}\n                          </div>\n                        )}\n                        <p className=\"text-xs text-gray-600 line-clamp-2\">\n                          {item.content.slice(0, 200)}...\n                        </p>\n                      </div>\n                      <a\n                        href={item.url}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"ml-4 text-blue-600 hover:text-blue-900\"\n                      >\n                        <ExternalLink className=\"h-4 w-4\" />\n                      </a>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Add Feed Modal */}\n      {showAddForm && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n            <div className=\"mt-3\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Add RSS Feed</h3>\n              <form onSubmit={addFeed}>\n                <div className=\"mb-4\">\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Feed Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={newFeed.name}\n                    onChange={(e) => setNewFeed({ ...newFeed, name: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    required\n                  />\n                </div>\n                <div className=\"mb-4\">\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Feed URL\n                  </label>\n                  <input\n                    type=\"url\"\n                    value={newFeed.url}\n                    onChange={(e) => setNewFeed({ ...newFeed, url: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    required\n                  />\n                </div>\n                <div className=\"flex justify-end space-x-3\">\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowAddForm(false)}\n                    className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200\"\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700\"\n                  >\n                    Add Feed\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAyBe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,MAAM;QAAI,KAAK;IAAG;IAE3D,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,cAAc;YACd,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,UAAU,OAAO;QACrB,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW;oBAAE,MAAM;oBAAI,KAAK;gBAAG;gBAC/B,eAAe;gBACf,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,aAAa,OAAO,IAAY;QACpC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,eAAe,EAAE,IAAI,EAAE;gBACnD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,WAAW,CAAC;gBAAS;YAC9C;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI,CAAC,QAAQ,+CAA+C;QAE5D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,eAAe,EAAE,IAAI,EAAE;gBACnD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,WAAW;YACX,MAAM,QAAQ,GAAG,CAAC;gBAAC;gBAAc;aAAiB;YAClD,WAAW;QACb;QACA;IACF,GAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDACvB,cAAA,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;0CAK9C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAW,CAAC,aAAa,EAAE,aAAa,iBAAiB,IAAI;;;;;;4CAAI;;;;;;;kDAG9E,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDAAE,WAAU;;oDAA8B,MAAM,MAAM;oDAAC;;;;;;;;;;;;;kDAE1D,8OAAC;wCAAI,WAAU;kDACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;gDAAkB,WAAU;0DAC3B,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,KAAK,IAAI;;;;;;8EAEZ,8OAAC;oEAAE,WAAU;8EACV,KAAK,GAAG;;;;;;;;;;;;sEAGb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,MAAK;oEACL,SAAS,IAAM,WAAW,KAAK,EAAE,EAAE,KAAK,SAAS;oEACjD,WAAW,CAAC,uNAAuN,EACjO,KAAK,SAAS,GAAG,gBAAgB,eACjC;8EAEF,cAAA,8OAAC;wEACC,WAAW,CAAC,2HAA2H,EACrI,KAAK,SAAS,GAAG,kBAAkB,iBACnC;;;;;;;;;;;8EAGN,8OAAC;oEACC,MAAK;oEACL,SAAS,IAAM,WAAW,KAAK,EAAE;oEACjC,WAAU;8EAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CA7BhB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;sCAwCzB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDAAE,WAAU;;oDACV,UAAU,MAAM;oDAAC;;;;;;;;;;;;;kDAGtB,8OAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;gDAAgB,WAAU;0DACzB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,KAAK,KAAK;;;;;;8EAEb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;;8FACd,8OAAC,0MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;gFACnB,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB;;;;;;;wEAE/C,KAAK,MAAM,kBAAI,8OAAC;;gFAAK;gFAAI,KAAK,MAAM;;;;;;;sFACrC,8OAAC;4EAAK,WAAU;;gFAA8C;gFACpD,KAAK,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;;gEAG9B,KAAK,UAAU,CAAC,MAAM,GAAG,mBACxB,8OAAC;oEAAI,WAAU;8EACZ,KAAK,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,oBAC1C,8OAAC;4EAEC,WAAU;sFAET;2EAHI;;;;;;;;;;8EAQb,8OAAC;oEAAE,WAAU;;wEACV,KAAK,OAAO,CAAC,KAAK,CAAC,GAAG;wEAAK;;;;;;;;;;;;;sEAGhC,8OAAC;4DACC,MAAM,KAAK,GAAG;4DACd,QAAO;4DACP,KAAI;4DACJ,WAAU;sEAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;;;;;;+CAtCpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAkDrB,6BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAK,UAAU;;kDACd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO,QAAQ,IAAI;gDACnB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC/D,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAGZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO,QAAQ,GAAG;gDAClB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,KAAK,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC9D,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAGZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,eAAe;gDAC9B,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}]}