module.exports = {

"[project]/.next-internal/server/app/api/rss/items/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/lib/supabase.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "dbOperations": (()=>dbOperations),
    "supabase": (()=>supabase),
    "supabaseAdmin": (()=>supabaseAdmin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://fmhujzbqfzyyffgzwtzb.supabase.co");
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZtaHVqemJxZnp5eWZmZ3p3dHpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExOTAxNTUsImV4cCI6MjA2Njc2NjE1NX0.__Hn0engp4WtLiH5O02HEps4GwU6YNwi9sF6lHQIg30");
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseServiceKey);
const dbOperations = {
    // RSS Feeds
    async getRSSFeeds () {
        const { data, error } = await supabase.from('rss_feeds').select('*').order('created_at', {
            ascending: false
        });
        if (error) throw error;
        return data;
    },
    async addRSSFeed (name, url) {
        const { data, error } = await supabase.from('rss_feeds').insert({
            name,
            url
        }).select().single();
        if (error) throw error;
        return data;
    },
    async updateRSSFeed (id, updates) {
        const { data, error } = await supabase.from('rss_feeds').update({
            ...updates,
            updated_at: new Date().toISOString()
        }).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    // Posted Tweets
    async getPostedTweets (limit = 50) {
        const { data, error } = await supabase.from('posted_tweets').select('*').order('posted_at', {
            ascending: false
        }).limit(limit);
        if (error) throw error;
        return data;
    },
    async addPostedTweet (tweet) {
        const { data, error } = await supabase.from('posted_tweets').insert(tweet).select().single();
        if (error) throw error;
        return data;
    },
    // Twitter Analytics
    async getLatestAnalytics () {
        const { data, error } = await supabase.from('twitter_analytics').select('*').order('recorded_at', {
            ascending: false
        }).limit(1).single();
        if (error && error.code !== 'PGRST116') throw error;
        return data;
    },
    async addAnalytics (analytics) {
        const { data, error } = await supabase.from('twitter_analytics').insert(analytics).select().single();
        if (error) throw error;
        return data;
    },
    // User Preferences
    async getUserPreferences () {
        const { data, error } = await supabase.from('user_preferences').select('*').limit(1).single();
        if (error && error.code !== 'PGRST116') throw error;
        return data;
    },
    async updateUserPreferences (updates) {
        const { data, error } = await supabase.from('user_preferences').update({
            ...updates,
            updated_at: new Date().toISOString()
        }).select().single();
        if (error) throw error;
        return data;
    },
    // Content Queue
    async getContentQueue (limit = 20) {
        const { data, error } = await supabase.from('content_queue').select('*').order('created_at', {
            ascending: false
        }).limit(limit);
        if (error) throw error;
        return data;
    },
    async addToContentQueue (content) {
        const { data, error } = await supabase.from('content_queue').insert(content).select().single();
        if (error) throw error;
        return data;
    },
    async updateContentQueue (id, updates) {
        const { data, error } = await supabase.from('content_queue').update({
            ...updates,
            updated_at: new Date().toISOString()
        }).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    async getSelectedContent () {
        const { data, error } = await supabase.from('content_queue').select('*').eq('is_selected', true).eq('is_posted', false).order('priority_score', {
            ascending: false
        });
        if (error) throw error;
        return data;
    },
    async markContentAsPosted (ids) {
        const { data, error } = await supabase.from('content_queue').update({
            is_posted: true,
            is_selected: false,
            updated_at: new Date().toISOString()
        }).in('id', ids).select();
        if (error) throw error;
        return data;
    },
    // Delete RSS Feed
    async deleteRSSFeed (id) {
        const { error } = await supabase.from('rss_feeds').delete().eq('id', id);
        if (error) throw error;
        return true;
    }
};
}}),
"[externals]/string_decoder [external] (string_decoder, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("string_decoder", () => require("string_decoder"));

module.exports = mod;
}}),
"[externals]/timers [external] (timers, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("timers", () => require("timers"));

module.exports = mod;
}}),
"[externals]/perf_hooks [external] (perf_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("perf_hooks", () => require("perf_hooks"));

module.exports = mod;
}}),
"[externals]/postcss [external] (postcss, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("postcss", () => require("postcss"));

module.exports = mod;
}}),
"[project]/src/lib/rss.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "rssOperations": (()=>rssOperations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rss$2d$parser$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rss-parser/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$extractus$2f$article$2d$extractor$2f$src$2f$main$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@extractus/article-extractor/src/main.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$extractus$2f$article$2d$extractor$2f$src$2f$main$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@extractus/article-extractor/src/main.js [app-route] (ecmascript) <locals>");
;
;
const parser = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rss$2d$parser$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]();
const rssOperations = {
    // Parse RSS feed and return items
    async parseFeed (feedUrl) {
        try {
            const feed = await parser.parseURL(feedUrl);
            return feed.items.map((item)=>({
                    title: item.title || '',
                    link: item.link || '',
                    pubDate: item.pubDate,
                    contentSnippet: item.contentSnippet,
                    content: item.content,
                    guid: item.guid || item.link || '',
                    categories: item.categories || [],
                    author: item.author
                }));
        } catch (error) {
            console.error(`Error parsing RSS feed ${feedUrl}:`, error);
            throw error;
        }
    },
    // Extract full content from article URL
    async extractContent (url) {
        try {
            const article = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$extractus$2f$article$2d$extractor$2f$src$2f$main$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["extract"])(url);
            return article?.content || null;
        } catch (error) {
            console.error(`Error extracting content from ${url}:`, error);
            return null;
        }
    },
    // Process RSS items and extract full content
    async processRSSItems (items) {
        const processedItems = [];
        for (const item of items){
            try {
                const fullContent = await this.extractContent(item.link);
                if (fullContent) {
                    processedItems.push({
                        title: item.title,
                        url: item.link,
                        content: fullContent,
                        publishedAt: item.pubDate || new Date().toISOString(),
                        categories: item.categories || [],
                        author: item.author,
                        guid: item.guid || item.link
                    });
                }
            } catch (error) {
                console.error(`Error processing item ${item.title}:`, error);
            // Continue with next item
            }
        }
        return processedItems;
    },
    // Deduplicate content based on URL and title similarity
    deduplicateContent (items) {
        const seen = new Set();
        const deduplicated = [];
        for (const item of items){
            // Create a key based on URL and normalized title
            const normalizedTitle = item.title.toLowerCase().replace(/[^\w\s]/g, '').trim();
            const key = `${item.url}|${normalizedTitle}`;
            if (!seen.has(key)) {
                seen.add(key);
                deduplicated.push(item);
            }
        }
        return deduplicated;
    },
    // Get content from multiple RSS feeds and deduplicate
    async getAggregatedContent (feedUrls) {
        const allItems = [];
        for (const feedUrl of feedUrls){
            try {
                const items = await this.parseFeed(feedUrl);
                const processedItems = await this.processRSSItems(items);
                allItems.push(...processedItems);
            } catch (error) {
                console.error(`Error processing feed ${feedUrl}:`, error);
            // Continue with other feeds
            }
        }
        // Deduplicate and sort by publication date
        const deduplicated = this.deduplicateContent(allItems);
        return deduplicated.sort((a, b)=>new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime());
    },
    // Filter content by keywords or categories
    filterContent (items, keywords = []) {
        if (keywords.length === 0) return items;
        return items.filter((item)=>{
            const searchText = `${item.title} ${item.content} ${item.categories.join(' ')}`.toLowerCase();
            return keywords.some((keyword)=>searchText.includes(keyword.toLowerCase()));
        });
    },
    // Score content based on various factors
    scoreContent (items) {
        return items.map((item)=>{
            let score = 0;
            // Recency score (newer content gets higher score)
            const ageInHours = (Date.now() - new Date(item.publishedAt).getTime()) / (1000 * 60 * 60);
            score += Math.max(0, 100 - ageInHours) // Max 100 points for very recent content
            ;
            // Title quality score
            const titleWords = item.title.split(' ').length;
            if (titleWords >= 5 && titleWords <= 15) score += 20 // Optimal title length
            ;
            // Content length score
            const contentWords = item.content.split(' ').length;
            if (contentWords >= 200 && contentWords <= 2000) score += 30 // Good content length
            ;
            // Category relevance (tech-related categories get bonus)
            const techKeywords = [
                'tech',
                'technology',
                'ai',
                'startup',
                'innovation',
                'software',
                'app'
            ];
            const hasRelevantCategory = item.categories.some((cat)=>techKeywords.some((keyword)=>cat.toLowerCase().includes(keyword)));
            if (hasRelevantCategory) score += 25;
            return {
                ...item,
                score
            };
        }).sort((a, b)=>b.score - a.score);
    }
};
const __TURBOPACK__default__export__ = rssOperations;
}}),
"[project]/src/app/api/rss/items/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$rss$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/rss.ts [app-route] (ecmascript)");
;
;
;
async function GET() {
    try {
        // Get active RSS feeds from database
        const feeds = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].getRSSFeeds();
        const activeFeeds = feeds.filter((feed)=>feed.is_active);
        if (activeFeeds.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json([]);
        }
        // Get aggregated content from all active feeds
        const feedUrls = activeFeeds.map((feed)=>feed.url);
        const content = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$rss$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].getAggregatedContent(feedUrls);
        // Score and sort content
        const scoredContent = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$rss$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].scoreContent(content);
        // Return top 50 items
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(scoredContent.slice(0, 50));
    } catch (error) {
        console.error('Error fetching RSS items:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch RSS items'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const { feedUrl, limit = 10 } = await request.json();
        if (!feedUrl) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Feed URL is required'
            }, {
                status: 400
            });
        }
        // Parse single feed
        const items = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$rss$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].parseFeed(feedUrl);
        const processedItems = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$rss$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].processRSSItems(items.slice(0, limit));
        const scoredItems = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$rss$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].scoreContent(processedItems);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(scoredItems);
    } catch (error) {
        console.error('Error processing RSS feed:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to process RSS feed'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__067e4b03._.js.map