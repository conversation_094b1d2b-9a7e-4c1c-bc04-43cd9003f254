{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/globals.js"], "sourcesContent": ["export const API_V2_PREFIX = 'https://api.twitter.com/2/';\nexport const API_V2_LABS_PREFIX = 'https://api.twitter.com/labs/2/';\nexport const API_V1_1_PREFIX = 'https://api.twitter.com/1.1/';\nexport const API_V1_1_UPLOAD_PREFIX = 'https://upload.twitter.com/1.1/';\nexport const API_V1_1_STREAM_PREFIX = 'https://stream.twitter.com/1.1/';\n"], "names": [], "mappings": ";;;;;;;AAAO,MAAM,gBAAgB;AACtB,MAAM,qBAAqB;AAC3B,MAAM,kBAAkB;AACxB,MAAM,yBAAyB;AAC/B,MAAM,yBAAyB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/paginators/TwitterPaginator.js"], "sourcesContent": ["/** TwitterPaginator: able to get consume data from initial request, then fetch next data sequentially. */\nexport class TwitterPaginator {\n    // noinspection TypeScriptAbstractClassConstructorCanBeMadeProtected\n    constructor({ realData, rateLimit, instance, queryParams, sharedParams }) {\n        this._maxResultsWhenFetchLast = 100;\n        this._realData = realData;\n        this._rateLimit = rateLimit;\n        this._instance = instance;\n        this._queryParams = queryParams;\n        this._sharedParams = sharedParams;\n    }\n    get _isRateLimitOk() {\n        if (!this._rateLimit) {\n            return true;\n        }\n        const resetDate = this._rateLimit.reset * 1000;\n        if (resetDate < Date.now()) {\n            return true;\n        }\n        return this._rateLimit.remaining > 0;\n    }\n    makeRequest(queryParams) {\n        return this._instance.get(this.getEndpoint(), queryParams, { fullResponse: true, params: this._sharedParams });\n    }\n    makeNewInstanceFromResult(result, queryParams) {\n        // Construct a subclass\n        return new this.constructor({\n            realData: result.data,\n            rateLimit: result.rateLimit,\n            instance: this._instance,\n            queryParams,\n            sharedParams: this._sharedParams,\n        });\n    }\n    getEndpoint() {\n        return this._endpoint;\n    }\n    injectQueryParams(maxResults) {\n        return {\n            ...(maxResults ? { max_results: maxResults } : {}),\n            ...this._queryParams,\n        };\n    }\n    /* ---------------------- */\n    /* Real paginator methods */\n    /* ---------------------- */\n    /**\n     * Next page.\n     */\n    async next(maxResults) {\n        const queryParams = this.getNextQueryParams(maxResults);\n        const result = await this.makeRequest(queryParams);\n        return this.makeNewInstanceFromResult(result, queryParams);\n    }\n    /**\n     * Next page, but store it in current instance.\n     */\n    async fetchNext(maxResults) {\n        const queryParams = this.getNextQueryParams(maxResults);\n        const result = await this.makeRequest(queryParams);\n        // Await in case of async sub-methods\n        await this.refreshInstanceFromResult(result, true);\n        return this;\n    }\n    /**\n     * Fetch up to {count} items after current page,\n     * as long as rate limit is not hit and Twitter has some results\n     */\n    async fetchLast(count = Infinity) {\n        let queryParams = this.getNextQueryParams(this._maxResultsWhenFetchLast);\n        let resultCount = 0;\n        // Break at rate limit limit\n        while (resultCount < count && this._isRateLimitOk) {\n            const response = await this.makeRequest(queryParams);\n            await this.refreshInstanceFromResult(response, true);\n            resultCount += this.getPageLengthFromRequest(response);\n            if (this.isFetchLastOver(response)) {\n                break;\n            }\n            queryParams = this.getNextQueryParams(this._maxResultsWhenFetchLast);\n        }\n        return this;\n    }\n    get rateLimit() {\n        var _a;\n        return { ...(_a = this._rateLimit) !== null && _a !== void 0 ? _a : {} };\n    }\n    /** Get raw data returned by Twitter API. */\n    get data() {\n        return this._realData;\n    }\n    get done() {\n        return !this.canFetchNextPage(this._realData);\n    }\n    /**\n     * Iterate over currently fetched items.\n     */\n    *[Symbol.iterator]() {\n        yield* this.getItemArray();\n    }\n    /**\n     * Iterate over items \"indefinitely\" (until rate limit is hit / they're no more items available)\n     * This will **mutate the current instance** and fill data, metas, etc. inside this instance.\n     *\n     * If you need to handle concurrent requests, or you need to rely on immutability, please use `.fetchAndIterate()` instead.\n     */\n    async *[Symbol.asyncIterator]() {\n        yield* this.getItemArray();\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        let paginator = this;\n        let canFetchNextPage = this.canFetchNextPage(this._realData);\n        while (canFetchNextPage && this._isRateLimitOk && paginator.getItemArray().length > 0) {\n            const next = await paginator.next(this._maxResultsWhenFetchLast);\n            // Store data into current instance [needed to access includes and meta]\n            this.refreshInstanceFromResult({ data: next._realData, headers: {}, rateLimit: next._rateLimit }, true);\n            canFetchNextPage = this.canFetchNextPage(next._realData);\n            const items = next.getItemArray();\n            yield* items;\n            paginator = next;\n        }\n    }\n    /**\n     * Iterate over items \"indefinitely\" without modifying the current instance (until rate limit is hit / they're no more items available)\n     *\n     * This will **NOT** mutate the current instance, meaning that current instance will not inherit from `includes` and `meta` (v2 API only).\n     * Use `Symbol.asyncIterator` (`for-await of`) to directly access items with current instance mutation.\n     */\n    async *fetchAndIterate() {\n        for (const item of this.getItemArray()) {\n            yield [item, this];\n        }\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        let paginator = this;\n        let canFetchNextPage = this.canFetchNextPage(this._realData);\n        while (canFetchNextPage && this._isRateLimitOk && paginator.getItemArray().length > 0) {\n            const next = await paginator.next(this._maxResultsWhenFetchLast);\n            // Store data into current instance [needed to access includes and meta]\n            this.refreshInstanceFromResult({ data: next._realData, headers: {}, rateLimit: next._rateLimit }, true);\n            canFetchNextPage = this.canFetchNextPage(next._realData);\n            for (const item of next.getItemArray()) {\n                yield [item, next];\n            }\n            this._rateLimit = next._rateLimit;\n            paginator = next;\n        }\n    }\n}\n/** PreviousableTwitterPaginator: a TwitterPaginator able to get consume data from both side, next and previous. */\nexport class PreviousableTwitterPaginator extends TwitterPaginator {\n    /**\n     * Previous page (new tweets)\n     */\n    async previous(maxResults) {\n        const queryParams = this.getPreviousQueryParams(maxResults);\n        const result = await this.makeRequest(queryParams);\n        return this.makeNewInstanceFromResult(result, queryParams);\n    }\n    /**\n     * Previous page, but in current instance.\n     */\n    async fetchPrevious(maxResults) {\n        const queryParams = this.getPreviousQueryParams(maxResults);\n        const result = await this.makeRequest(queryParams);\n        await this.refreshInstanceFromResult(result, false);\n        return this;\n    }\n}\nexport default TwitterPaginator;\n"], "names": [], "mappings": "AAAA,wGAAwG;;;;;AACjG,MAAM;IACT,oEAAoE;IACpE,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE,CAAE;QACtE,IAAI,CAAC,wBAAwB,GAAG;QAChC,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,aAAa,GAAG;IACzB;IACA,IAAI,iBAAiB;QACjB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO;QACX;QACA,MAAM,YAAY,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG;QAC1C,IAAI,YAAY,KAAK,GAAG,IAAI;YACxB,OAAO;QACX;QACA,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG;IACvC;IACA,YAAY,WAAW,EAAE;QACrB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,aAAa;YAAE,cAAc;YAAM,QAAQ,IAAI,CAAC,aAAa;QAAC;IAChH;IACA,0BAA0B,MAAM,EAAE,WAAW,EAAE;QAC3C,uBAAuB;QACvB,OAAO,IAAI,IAAI,CAAC,WAAW,CAAC;YACxB,UAAU,OAAO,IAAI;YACrB,WAAW,OAAO,SAAS;YAC3B,UAAU,IAAI,CAAC,SAAS;YACxB;YACA,cAAc,IAAI,CAAC,aAAa;QACpC;IACJ;IACA,cAAc;QACV,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,kBAAkB,UAAU,EAAE;QAC1B,OAAO;YACH,GAAI,aAAa;gBAAE,aAAa;YAAW,IAAI,CAAC,CAAC;YACjD,GAAG,IAAI,CAAC,YAAY;QACxB;IACJ;IACA,0BAA0B,GAC1B,0BAA0B,GAC1B,0BAA0B,GAC1B;;KAEC,GACD,MAAM,KAAK,UAAU,EAAE;QACnB,MAAM,cAAc,IAAI,CAAC,kBAAkB,CAAC;QAC5C,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,IAAI,CAAC,yBAAyB,CAAC,QAAQ;IAClD;IACA;;KAEC,GACD,MAAM,UAAU,UAAU,EAAE;QACxB,MAAM,cAAc,IAAI,CAAC,kBAAkB,CAAC;QAC5C,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,qCAAqC;QACrC,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ;QAC7C,OAAO,IAAI;IACf;IACA;;;KAGC,GACD,MAAM,UAAU,QAAQ,QAAQ,EAAE;QAC9B,IAAI,cAAc,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,wBAAwB;QACvE,IAAI,cAAc;QAClB,4BAA4B;QAC5B,MAAO,cAAc,SAAS,IAAI,CAAC,cAAc,CAAE;YAC/C,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;YACxC,MAAM,IAAI,CAAC,yBAAyB,CAAC,UAAU;YAC/C,eAAe,IAAI,CAAC,wBAAwB,CAAC;YAC7C,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW;gBAChC;YACJ;YACA,cAAc,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,wBAAwB;QACvE;QACA,OAAO,IAAI;IACf;IACA,IAAI,YAAY;QACZ,IAAI;QACJ,OAAO;YAAE,GAAG,CAAC,KAAK,IAAI,CAAC,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,CAAC,CAAC;QAAC;IAC3E;IACA,0CAA0C,GAC1C,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,IAAI,OAAO;QACP,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS;IAChD;IACA;;KAEC,GACD,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QACjB,OAAO,IAAI,CAAC,YAAY;IAC5B;IACA;;;;;KAKC,GACD,OAAO,CAAC,OAAO,aAAa,CAAC,GAAG;QAC5B,OAAO,IAAI,CAAC,YAAY;QACxB,4DAA4D;QAC5D,IAAI,YAAY,IAAI;QACpB,IAAI,mBAAmB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS;QAC3D,MAAO,oBAAoB,IAAI,CAAC,cAAc,IAAI,UAAU,YAAY,GAAG,MAAM,GAAG,EAAG;YACnF,MAAM,OAAO,MAAM,UAAU,IAAI,CAAC,IAAI,CAAC,wBAAwB;YAC/D,wEAAwE;YACxE,IAAI,CAAC,yBAAyB,CAAC;gBAAE,MAAM,KAAK,SAAS;gBAAE,SAAS,CAAC;gBAAG,WAAW,KAAK,UAAU;YAAC,GAAG;YAClG,mBAAmB,IAAI,CAAC,gBAAgB,CAAC,KAAK,SAAS;YACvD,MAAM,QAAQ,KAAK,YAAY;YAC/B,OAAO;YACP,YAAY;QAChB;IACJ;IACA;;;;;KAKC,GACD,OAAO,kBAAkB;QACrB,KAAK,MAAM,QAAQ,IAAI,CAAC,YAAY,GAAI;YACpC,MAAM;gBAAC;gBAAM,IAAI;aAAC;QACtB;QACA,4DAA4D;QAC5D,IAAI,YAAY,IAAI;QACpB,IAAI,mBAAmB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS;QAC3D,MAAO,oBAAoB,IAAI,CAAC,cAAc,IAAI,UAAU,YAAY,GAAG,MAAM,GAAG,EAAG;YACnF,MAAM,OAAO,MAAM,UAAU,IAAI,CAAC,IAAI,CAAC,wBAAwB;YAC/D,wEAAwE;YACxE,IAAI,CAAC,yBAAyB,CAAC;gBAAE,MAAM,KAAK,SAAS;gBAAE,SAAS,CAAC;gBAAG,WAAW,KAAK,UAAU;YAAC,GAAG;YAClG,mBAAmB,IAAI,CAAC,gBAAgB,CAAC,KAAK,SAAS;YACvD,KAAK,MAAM,QAAQ,KAAK,YAAY,GAAI;gBACpC,MAAM;oBAAC;oBAAM;iBAAK;YACtB;YACA,IAAI,CAAC,UAAU,GAAG,KAAK,UAAU;YACjC,YAAY;QAChB;IACJ;AACJ;AAEO,MAAM,qCAAqC;IAC9C;;KAEC,GACD,MAAM,SAAS,UAAU,EAAE;QACvB,MAAM,cAAc,IAAI,CAAC,sBAAsB,CAAC;QAChD,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,IAAI,CAAC,yBAAyB,CAAC,QAAQ;IAClD;IACA;;KAEC,GACD,MAAM,cAAc,UAAU,EAAE;QAC5B,MAAM,cAAc,IAAI,CAAC,sBAAsB,CAAC;QAChD,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ;QAC7C,OAAO,IAAI;IACf;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/paginators/paginator.v1.js"], "sourcesContent": ["import TwitterPaginator from './TwitterPaginator';\nexport class CursoredV1Paginator extends TwitterPaginator {\n    getNextQueryParams(maxResults) {\n        var _a;\n        return {\n            ...this._queryParams,\n            cursor: (_a = this._realData.next_cursor_str) !== null && _a !== void 0 ? _a : this._realData.next_cursor,\n            ...(maxResults ? { count: maxResults } : {}),\n        };\n    }\n    isFetchLastOver(result) {\n        // If we cant fetch next page\n        return !this.canFetchNextPage(result.data);\n    }\n    canFetchNextPage(result) {\n        // If one of cursor is valid\n        return !this.isNextCursorInvalid(result.next_cursor) || !this.isNextCursorInvalid(result.next_cursor_str);\n    }\n    isNextCursorInvalid(value) {\n        return value === undefined\n            || value === 0\n            || value === -1\n            || value === '0'\n            || value === '-1';\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,4BAA4B,uLAAA,CAAA,UAAgB;IACrD,mBAAmB,UAAU,EAAE;QAC3B,IAAI;QACJ,OAAO;YACH,GAAG,IAAI,CAAC,YAAY;YACpB,QAAQ,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,eAAe,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,WAAW;YACzG,GAAI,aAAa;gBAAE,OAAO;YAAW,IAAI,CAAC,CAAC;QAC/C;IACJ;IACA,gBAAgB,MAAM,EAAE;QACpB,6BAA6B;QAC7B,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,IAAI;IAC7C;IACA,iBAAiB,MAAM,EAAE;QACrB,4BAA4B;QAC5B,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,WAAW,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,eAAe;IAC5G;IACA,oBAAoB,KAAK,EAAE;QACvB,OAAO,UAAU,aACV,UAAU,KACV,UAAU,CAAC,KACX,UAAU,OACV,UAAU;IACrB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/paginators/dm.paginator.v1.js"], "sourcesContent": ["import { CursoredV1Paginator } from './paginator.v1';\nexport class DmEventsV1Paginator extends CursoredV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'direct_messages/events/list.json';\n    }\n    refreshInstanceFromResult(response, isNextPage) {\n        const result = response.data;\n        this._rateLimit = response.rateLimit;\n        if (isNextPage) {\n            this._realData.events.push(...result.events);\n            this._realData.next_cursor = result.next_cursor;\n        }\n    }\n    getPageLengthFromRequest(result) {\n        return result.data.events.length;\n    }\n    getItemArray() {\n        return this.events;\n    }\n    /**\n     * Events returned by paginator.\n     */\n    get events() {\n        return this._realData.events;\n    }\n}\nexport class WelcomeDmV1Paginator extends CursoredV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'direct_messages/welcome_messages/list.json';\n    }\n    refreshInstanceFromResult(response, isNextPage) {\n        const result = response.data;\n        this._rateLimit = response.rateLimit;\n        if (isNextPage) {\n            this._realData.welcome_messages.push(...result.welcome_messages);\n            this._realData.next_cursor = result.next_cursor;\n        }\n    }\n    getPageLengthFromRequest(result) {\n        return result.data.welcome_messages.length;\n    }\n    getItemArray() {\n        return this.welcomeMessages;\n    }\n    get welcomeMessages() {\n        return this._realData.welcome_messages;\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,4BAA4B,sLAAA,CAAA,sBAAmB;IACxD,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,0BAA0B,QAAQ,EAAE,UAAU,EAAE;QAC5C,MAAM,SAAS,SAAS,IAAI;QAC5B,IAAI,CAAC,UAAU,GAAG,SAAS,SAAS;QACpC,IAAI,YAAY;YACZ,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,IAAI,OAAO,MAAM;YAC3C,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,WAAW;QACnD;IACJ;IACA,yBAAyB,MAAM,EAAE;QAC7B,OAAO,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM;IACpC;IACA,eAAe;QACX,OAAO,IAAI,CAAC,MAAM;IACtB;IACA;;KAEC,GACD,IAAI,SAAS;QACT,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM;IAChC;AACJ;AACO,MAAM,6BAA6B,sLAAA,CAAA,sBAAmB;IACzD,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,0BAA0B,QAAQ,EAAE,UAAU,EAAE;QAC5C,MAAM,SAAS,SAAS,IAAI;QAC5B,IAAI,CAAC,UAAU,GAAG,SAAS,SAAS;QACpC,IAAI,YAAY;YACZ,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,IAAI,OAAO,gBAAgB;YAC/D,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,WAAW;QACnD;IACJ;IACA,yBAAyB,MAAM,EAAE;QAC7B,OAAO,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM;IAC9C;IACA,eAAe;QACX,OAAO,IAAI,CAAC,eAAe;IAC/B;IACA,IAAI,kBAAkB;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB;IAC1C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/types/v1/tweet.v1.types.js"], "sourcesContent": ["export var EUploadMimeType;\n(function (EUploadMimeType) {\n    EUploadMimeType[\"Jpeg\"] = \"image/jpeg\";\n    EUploadMimeType[\"Mp4\"] = \"video/mp4\";\n    EUploadMimeType[\"Mov\"] = \"video/quicktime\";\n    EUploadMimeType[\"Gif\"] = \"image/gif\";\n    EUploadMimeType[\"Png\"] = \"image/png\";\n    EUploadMimeType[\"Srt\"] = \"text/plain\";\n    EUploadMimeType[\"Webp\"] = \"image/webp\";\n})(EUploadMimeType || (EUploadMimeType = {}));\n"], "names": [], "mappings": ";;;AAAO,IAAI;AACX,CAAC,SAAU,eAAe;IACtB,eAAe,CAAC,OAAO,GAAG;IAC1B,eAAe,CAAC,MAAM,GAAG;IACzB,eAAe,CAAC,MAAM,GAAG;IACzB,eAAe,CAAC,MAAM,GAAG;IACzB,eAAe,CAAC,MAAM,GAAG;IACzB,eAAe,CAAC,MAAM,GAAG;IACzB,eAAe,CAAC,OAAO,GAAG;AAC9B,CAAC,EAAE,mBAAmB,CAAC,kBAAkB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/types/v1/dm.v1.types.js"], "sourcesContent": ["// Creation of DMs\nexport var EDirectMessageEventTypeV1;\n(function (EDirectMessageEventTypeV1) {\n    EDirectMessageEventTypeV1[\"Create\"] = \"message_create\";\n    EDirectMessageEventTypeV1[\"WelcomeCreate\"] = \"welcome_message\";\n})(EDirectMessageEventTypeV1 || (EDirectMessageEventTypeV1 = {}));\n"], "names": [], "mappings": "AAAA,kBAAkB;;;;AACX,IAAI;AACX,CAAC,SAAU,yBAAyB;IAChC,yBAAyB,CAAC,SAAS,GAAG;IACtC,yBAAyB,CAAC,gBAAgB,GAAG;AACjD,CAAC,EAAE,6BAA6B,CAAC,4BAA4B,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/types/v1/index.js"], "sourcesContent": ["export * from './streaming.v1.types';\nexport * from './tweet.v1.types';\nexport * from './entities.v1.types';\nexport * from './user.v1.types';\nexport * from './dev-utilities.v1.types';\nexport * from './geo.v1.types';\nexport * from './trends.v1.types';\nexport * from './dm.v1.types';\nexport * from './list.v1.types';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/types/v2/streaming.v2.types.js"], "sourcesContent": ["// ---------------\n// -- Streaming --\n// ---------------\nexport {};\n"], "names": [], "mappings": "AAAA,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/types/v2/index.js"], "sourcesContent": ["export * from './streaming.v2.types';\nexport * from './tweet.v2.types';\nexport * from './tweet.definition.v2';\nexport * from './user.v2.types';\nexport * from './spaces.v2.types';\nexport * from './list.v2.types';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/types/errors.types.js"], "sourcesContent": ["export var ETwitterApiError;\n(function (ETwitterApiError) {\n    ETwitterApiError[\"Request\"] = \"request\";\n    ETwitterApiError[\"PartialResponse\"] = \"partial-response\";\n    ETwitterApiError[\"Response\"] = \"response\";\n})(ETwitterApiError || (ETwitterApiError = {}));\n/* ERRORS INSTANCES */\nclass ApiError extends Error {\n    constructor() {\n        super(...arguments);\n        this.error = true;\n    }\n}\nexport class ApiRequestError extends ApiError {\n    constructor(message, options) {\n        super(message);\n        this.type = ETwitterApiError.Request;\n        Error.captureStackTrace(this, this.constructor);\n        // Do not show on Node stack trace\n        Object.defineProperty(this, '_options', { value: options });\n    }\n    get request() {\n        return this._options.request;\n    }\n    get requestError() {\n        return this._options.requestError;\n    }\n    toJSON() {\n        return {\n            type: this.type,\n            error: this.requestError,\n        };\n    }\n}\nexport class ApiPartialResponseError extends ApiError {\n    constructor(message, options) {\n        super(message);\n        this.type = ETwitterApiError.PartialResponse;\n        Error.captureStackTrace(this, this.constructor);\n        // Do not show on Node stack trace\n        Object.defineProperty(this, '_options', { value: options });\n    }\n    get request() {\n        return this._options.request;\n    }\n    get response() {\n        return this._options.response;\n    }\n    get responseError() {\n        return this._options.responseError;\n    }\n    get rawContent() {\n        return this._options.rawContent;\n    }\n    toJSON() {\n        return {\n            type: this.type,\n            error: this.responseError,\n        };\n    }\n}\nexport class ApiResponseError extends ApiError {\n    constructor(message, options) {\n        super(message);\n        this.type = ETwitterApiError.Response;\n        Error.captureStackTrace(this, this.constructor);\n        // Do not show on Node stack trace\n        Object.defineProperty(this, '_options', { value: options });\n        this.code = options.code;\n        this.headers = options.headers;\n        this.rateLimit = options.rateLimit;\n        // Fix bad error data payload on some v1 endpoints (see https://github.com/PLhery/node-twitter-api-v2/issues/342)\n        if (options.data && typeof options.data === 'object' && 'error' in options.data && !options.data.errors) {\n            const data = { ...options.data };\n            data.errors = [{\n                    code: EApiV1ErrorCode.InternalError,\n                    message: data.error,\n                }];\n            this.data = data;\n        }\n        else {\n            this.data = options.data;\n        }\n    }\n    get request() {\n        return this._options.request;\n    }\n    get response() {\n        return this._options.response;\n    }\n    /** Check for presence of one of given v1/v2 error codes. */\n    hasErrorCode(...codes) {\n        const errors = this.errors;\n        // No errors\n        if (!(errors === null || errors === void 0 ? void 0 : errors.length)) {\n            return false;\n        }\n        // v1 errors\n        if ('code' in errors[0]) {\n            const v1errors = errors;\n            return v1errors.some(error => codes.includes(error.code));\n        }\n        // v2 error\n        const v2error = this.data;\n        return codes.includes(v2error.type);\n    }\n    get errors() {\n        var _a;\n        return (_a = this.data) === null || _a === void 0 ? void 0 : _a.errors;\n    }\n    get rateLimitError() {\n        return this.code === 420 || this.code === 429;\n    }\n    get isAuthError() {\n        if (this.code === 401) {\n            return true;\n        }\n        return this.hasErrorCode(EApiV1ErrorCode.AuthTimestampInvalid, EApiV1ErrorCode.AuthenticationFail, EApiV1ErrorCode.BadAuthenticationData, EApiV1ErrorCode.InvalidOrExpiredToken);\n    }\n    toJSON() {\n        return {\n            type: this.type,\n            code: this.code,\n            error: this.data,\n            rateLimit: this.rateLimit,\n            headers: this.headers,\n        };\n    }\n}\nexport var EApiV1ErrorCode;\n(function (EApiV1ErrorCode) {\n    // Location errors\n    EApiV1ErrorCode[EApiV1ErrorCode[\"InvalidCoordinates\"] = 3] = \"InvalidCoordinates\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"NoLocationFound\"] = 13] = \"NoLocationFound\";\n    // Authentication failures\n    EApiV1ErrorCode[EApiV1ErrorCode[\"AuthenticationFail\"] = 32] = \"AuthenticationFail\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"InvalidOrExpiredToken\"] = 89] = \"InvalidOrExpiredToken\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"UnableToVerifyCredentials\"] = 99] = \"UnableToVerifyCredentials\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"AuthTimestampInvalid\"] = 135] = \"AuthTimestampInvalid\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"BadAuthenticationData\"] = 215] = \"BadAuthenticationData\";\n    // Resources not found or visible\n    EApiV1ErrorCode[EApiV1ErrorCode[\"NoUserMatch\"] = 17] = \"NoUserMatch\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"UserNotFound\"] = 50] = \"UserNotFound\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"ResourceNotFound\"] = 34] = \"ResourceNotFound\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"TweetNotFound\"] = 144] = \"TweetNotFound\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"TweetNotVisible\"] = 179] = \"TweetNotVisible\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"NotAllowedResource\"] = 220] = \"NotAllowedResource\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"MediaIdNotFound\"] = 325] = \"MediaIdNotFound\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"TweetNoLongerAvailable\"] = 421] = \"TweetNoLongerAvailable\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"TweetViolatedRules\"] = 422] = \"TweetViolatedRules\";\n    // Account errors\n    EApiV1ErrorCode[EApiV1ErrorCode[\"TargetUserSuspended\"] = 63] = \"TargetUserSuspended\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"YouAreSuspended\"] = 64] = \"YouAreSuspended\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"AccountUpdateFailed\"] = 120] = \"AccountUpdateFailed\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"NoSelfSpamReport\"] = 36] = \"NoSelfSpamReport\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"NoSelfMute\"] = 271] = \"NoSelfMute\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"AccountLocked\"] = 326] = \"AccountLocked\";\n    // Application live errors / Twitter errors\n    EApiV1ErrorCode[EApiV1ErrorCode[\"RateLimitExceeded\"] = 88] = \"RateLimitExceeded\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"NoDMRightForApp\"] = 93] = \"NoDMRightForApp\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"OverCapacity\"] = 130] = \"OverCapacity\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"InternalError\"] = 131] = \"InternalError\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"TooManyFollowings\"] = 161] = \"TooManyFollowings\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"TweetLimitExceeded\"] = 185] = \"TweetLimitExceeded\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"DuplicatedTweet\"] = 187] = \"DuplicatedTweet\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"TooManySpamReports\"] = 205] = \"TooManySpamReports\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"RequestLooksLikeSpam\"] = 226] = \"RequestLooksLikeSpam\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"NoWriteRightForApp\"] = 261] = \"NoWriteRightForApp\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"TweetActionsDisabled\"] = 425] = \"TweetActionsDisabled\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"TweetRepliesRestricted\"] = 433] = \"TweetRepliesRestricted\";\n    // Invalid request parameters\n    EApiV1ErrorCode[EApiV1ErrorCode[\"NamedParameterMissing\"] = 38] = \"NamedParameterMissing\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"InvalidAttachmentUrl\"] = 44] = \"InvalidAttachmentUrl\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"TweetTextTooLong\"] = 186] = \"TweetTextTooLong\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"MissingUrlParameter\"] = 195] = \"MissingUrlParameter\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"NoMultipleGifs\"] = 323] = \"NoMultipleGifs\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"InvalidMediaIds\"] = 324] = \"InvalidMediaIds\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"InvalidUrl\"] = 407] = \"InvalidUrl\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"TooManyTweetAttachments\"] = 386] = \"TooManyTweetAttachments\";\n    // Already sent/deleted item\n    EApiV1ErrorCode[EApiV1ErrorCode[\"StatusAlreadyFavorited\"] = 139] = \"StatusAlreadyFavorited\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"FollowRequestAlreadySent\"] = 160] = \"FollowRequestAlreadySent\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"CannotUnmuteANonMutedAccount\"] = 272] = \"CannotUnmuteANonMutedAccount\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"TweetAlreadyRetweeted\"] = 327] = \"TweetAlreadyRetweeted\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"ReplyToDeletedTweet\"] = 385] = \"ReplyToDeletedTweet\";\n    // DM Errors\n    EApiV1ErrorCode[EApiV1ErrorCode[\"DMReceiverNotFollowingYou\"] = 150] = \"DMReceiverNotFollowingYou\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"UnableToSendDM\"] = 151] = \"UnableToSendDM\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"MustAllowDMFromAnyone\"] = 214] = \"MustAllowDMFromAnyone\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"CannotSendDMToThisUser\"] = 349] = \"CannotSendDMToThisUser\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"DMTextTooLong\"] = 354] = \"DMTextTooLong\";\n    // Application misconfiguration\n    EApiV1ErrorCode[EApiV1ErrorCode[\"SubscriptionAlreadyExists\"] = 355] = \"SubscriptionAlreadyExists\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"CallbackUrlNotApproved\"] = 415] = \"CallbackUrlNotApproved\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"SuspendedApplication\"] = 416] = \"SuspendedApplication\";\n    EApiV1ErrorCode[EApiV1ErrorCode[\"OobOauthIsNotAllowed\"] = 417] = \"OobOauthIsNotAllowed\";\n})(EApiV1ErrorCode || (EApiV1ErrorCode = {}));\nexport var EApiV2ErrorCode;\n(function (EApiV2ErrorCode) {\n    // Request errors\n    EApiV2ErrorCode[\"InvalidRequest\"] = \"https://api.twitter.com/2/problems/invalid-request\";\n    EApiV2ErrorCode[\"ClientForbidden\"] = \"https://api.twitter.com/2/problems/client-forbidden\";\n    EApiV2ErrorCode[\"UnsupportedAuthentication\"] = \"https://api.twitter.com/2/problems/unsupported-authentication\";\n    // Stream rules errors\n    EApiV2ErrorCode[\"InvalidRules\"] = \"https://api.twitter.com/2/problems/invalid-rules\";\n    EApiV2ErrorCode[\"TooManyRules\"] = \"https://api.twitter.com/2/problems/rule-cap\";\n    EApiV2ErrorCode[\"DuplicatedRules\"] = \"https://api.twitter.com/2/problems/duplicate-rules\";\n    // Twitter errors\n    EApiV2ErrorCode[\"RateLimitExceeded\"] = \"https://api.twitter.com/2/problems/usage-capped\";\n    EApiV2ErrorCode[\"ConnectionError\"] = \"https://api.twitter.com/2/problems/streaming-connection\";\n    EApiV2ErrorCode[\"ClientDisconnected\"] = \"https://api.twitter.com/2/problems/client-disconnected\";\n    EApiV2ErrorCode[\"TwitterDisconnectedYou\"] = \"https://api.twitter.com/2/problems/operational-disconnect\";\n    // Resource errors\n    EApiV2ErrorCode[\"ResourceNotFound\"] = \"https://api.twitter.com/2/problems/resource-not-found\";\n    EApiV2ErrorCode[\"ResourceUnauthorized\"] = \"https://api.twitter.com/2/problems/not-authorized-for-resource\";\n    EApiV2ErrorCode[\"DisallowedResource\"] = \"https://api.twitter.com/2/problems/disallowed-resource\";\n})(EApiV2ErrorCode || (EApiV2ErrorCode = {}));\n"], "names": [], "mappings": ";;;;;;;;AAAO,IAAI;AACX,CAAC,SAAU,gBAAgB;IACvB,gBAAgB,CAAC,UAAU,GAAG;IAC9B,gBAAgB,CAAC,kBAAkB,GAAG;IACtC,gBAAgB,CAAC,WAAW,GAAG;AACnC,CAAC,EAAE,oBAAoB,CAAC,mBAAmB,CAAC,CAAC;AAC7C,oBAAoB,GACpB,MAAM,iBAAiB;IACnB,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,KAAK,GAAG;IACjB;AACJ;AACO,MAAM,wBAAwB;IACjC,YAAY,OAAO,EAAE,OAAO,CAAE;QAC1B,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG,iBAAiB,OAAO;QACpC,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;QAC9C,kCAAkC;QAClC,OAAO,cAAc,CAAC,IAAI,EAAE,YAAY;YAAE,OAAO;QAAQ;IAC7D;IACA,IAAI,UAAU;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO;IAChC;IACA,IAAI,eAAe;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY;IACrC;IACA,SAAS;QACL,OAAO;YACH,MAAM,IAAI,CAAC,IAAI;YACf,OAAO,IAAI,CAAC,YAAY;QAC5B;IACJ;AACJ;AACO,MAAM,gCAAgC;IACzC,YAAY,OAAO,EAAE,OAAO,CAAE;QAC1B,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG,iBAAiB,eAAe;QAC5C,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;QAC9C,kCAAkC;QAClC,OAAO,cAAc,CAAC,IAAI,EAAE,YAAY;YAAE,OAAO;QAAQ;IAC7D;IACA,IAAI,UAAU;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO;IAChC;IACA,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ;IACjC;IACA,IAAI,gBAAgB;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa;IACtC;IACA,IAAI,aAAa;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU;IACnC;IACA,SAAS;QACL,OAAO;YACH,MAAM,IAAI,CAAC,IAAI;YACf,OAAO,IAAI,CAAC,aAAa;QAC7B;IACJ;AACJ;AACO,MAAM,yBAAyB;IAClC,YAAY,OAAO,EAAE,OAAO,CAAE;QAC1B,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG,iBAAiB,QAAQ;QACrC,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;QAC9C,kCAAkC;QAClC,OAAO,cAAc,CAAC,IAAI,EAAE,YAAY;YAAE,OAAO;QAAQ;QACzD,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI;QACxB,IAAI,CAAC,OAAO,GAAG,QAAQ,OAAO;QAC9B,IAAI,CAAC,SAAS,GAAG,QAAQ,SAAS;QAClC,iHAAiH;QACjH,IAAI,QAAQ,IAAI,IAAI,OAAO,QAAQ,IAAI,KAAK,YAAY,WAAW,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,EAAE;YACrG,MAAM,OAAO;gBAAE,GAAG,QAAQ,IAAI;YAAC;YAC/B,KAAK,MAAM,GAAG;gBAAC;oBACP,MAAM,gBAAgB,aAAa;oBACnC,SAAS,KAAK,KAAK;gBACvB;aAAE;YACN,IAAI,CAAC,IAAI,GAAG;QAChB,OACK;YACD,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI;QAC5B;IACJ;IACA,IAAI,UAAU;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO;IAChC;IACA,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ;IACjC;IACA,0DAA0D,GAC1D,aAAa,GAAG,KAAK,EAAE;QACnB,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,YAAY;QACZ,IAAI,CAAC,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,MAAM,GAAG;YAClE,OAAO;QACX;QACA,YAAY;QACZ,IAAI,UAAU,MAAM,CAAC,EAAE,EAAE;YACrB,MAAM,WAAW;YACjB,OAAO,SAAS,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,CAAC,MAAM,IAAI;QAC3D;QACA,WAAW;QACX,MAAM,UAAU,IAAI,CAAC,IAAI;QACzB,OAAO,MAAM,QAAQ,CAAC,QAAQ,IAAI;IACtC;IACA,IAAI,SAAS;QACT,IAAI;QACJ,OAAO,CAAC,KAAK,IAAI,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM;IAC1E;IACA,IAAI,iBAAiB;QACjB,OAAO,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,IAAI,KAAK;IAC9C;IACA,IAAI,cAAc;QACd,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK;YACnB,OAAO;QACX;QACA,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,oBAAoB,EAAE,gBAAgB,kBAAkB,EAAE,gBAAgB,qBAAqB,EAAE,gBAAgB,qBAAqB;IACnL;IACA,SAAS;QACL,OAAO;YACH,MAAM,IAAI,CAAC,IAAI;YACf,MAAM,IAAI,CAAC,IAAI;YACf,OAAO,IAAI,CAAC,IAAI;YAChB,WAAW,IAAI,CAAC,SAAS;YACzB,SAAS,IAAI,CAAC,OAAO;QACzB;IACJ;AACJ;AACO,IAAI;AACX,CAAC,SAAU,eAAe;IACtB,kBAAkB;IAClB,eAAe,CAAC,eAAe,CAAC,qBAAqB,GAAG,EAAE,GAAG;IAC7D,eAAe,CAAC,eAAe,CAAC,kBAAkB,GAAG,GAAG,GAAG;IAC3D,0BAA0B;IAC1B,eAAe,CAAC,eAAe,CAAC,qBAAqB,GAAG,GAAG,GAAG;IAC9D,eAAe,CAAC,eAAe,CAAC,wBAAwB,GAAG,GAAG,GAAG;IACjE,eAAe,CAAC,eAAe,CAAC,4BAA4B,GAAG,GAAG,GAAG;IACrE,eAAe,CAAC,eAAe,CAAC,uBAAuB,GAAG,IAAI,GAAG;IACjE,eAAe,CAAC,eAAe,CAAC,wBAAwB,GAAG,IAAI,GAAG;IAClE,iCAAiC;IACjC,eAAe,CAAC,eAAe,CAAC,cAAc,GAAG,GAAG,GAAG;IACvD,eAAe,CAAC,eAAe,CAAC,eAAe,GAAG,GAAG,GAAG;IACxD,eAAe,CAAC,eAAe,CAAC,mBAAmB,GAAG,GAAG,GAAG;IAC5D,eAAe,CAAC,eAAe,CAAC,gBAAgB,GAAG,IAAI,GAAG;IAC1D,eAAe,CAAC,eAAe,CAAC,kBAAkB,GAAG,IAAI,GAAG;IAC5D,eAAe,CAAC,eAAe,CAAC,qBAAqB,GAAG,IAAI,GAAG;IAC/D,eAAe,CAAC,eAAe,CAAC,kBAAkB,GAAG,IAAI,GAAG;IAC5D,eAAe,CAAC,eAAe,CAAC,yBAAyB,GAAG,IAAI,GAAG;IACnE,eAAe,CAAC,eAAe,CAAC,qBAAqB,GAAG,IAAI,GAAG;IAC/D,iBAAiB;IACjB,eAAe,CAAC,eAAe,CAAC,sBAAsB,GAAG,GAAG,GAAG;IAC/D,eAAe,CAAC,eAAe,CAAC,kBAAkB,GAAG,GAAG,GAAG;IAC3D,eAAe,CAAC,eAAe,CAAC,sBAAsB,GAAG,IAAI,GAAG;IAChE,eAAe,CAAC,eAAe,CAAC,mBAAmB,GAAG,GAAG,GAAG;IAC5D,eAAe,CAAC,eAAe,CAAC,aAAa,GAAG,IAAI,GAAG;IACvD,eAAe,CAAC,eAAe,CAAC,gBAAgB,GAAG,IAAI,GAAG;IAC1D,2CAA2C;IAC3C,eAAe,CAAC,eAAe,CAAC,oBAAoB,GAAG,GAAG,GAAG;IAC7D,eAAe,CAAC,eAAe,CAAC,kBAAkB,GAAG,GAAG,GAAG;IAC3D,eAAe,CAAC,eAAe,CAAC,eAAe,GAAG,IAAI,GAAG;IACzD,eAAe,CAAC,eAAe,CAAC,gBAAgB,GAAG,IAAI,GAAG;IAC1D,eAAe,CAAC,eAAe,CAAC,oBAAoB,GAAG,IAAI,GAAG;IAC9D,eAAe,CAAC,eAAe,CAAC,qBAAqB,GAAG,IAAI,GAAG;IAC/D,eAAe,CAAC,eAAe,CAAC,kBAAkB,GAAG,IAAI,GAAG;IAC5D,eAAe,CAAC,eAAe,CAAC,qBAAqB,GAAG,IAAI,GAAG;IAC/D,eAAe,CAAC,eAAe,CAAC,uBAAuB,GAAG,IAAI,GAAG;IACjE,eAAe,CAAC,eAAe,CAAC,qBAAqB,GAAG,IAAI,GAAG;IAC/D,eAAe,CAAC,eAAe,CAAC,uBAAuB,GAAG,IAAI,GAAG;IACjE,eAAe,CAAC,eAAe,CAAC,yBAAyB,GAAG,IAAI,GAAG;IACnE,6BAA6B;IAC7B,eAAe,CAAC,eAAe,CAAC,wBAAwB,GAAG,GAAG,GAAG;IACjE,eAAe,CAAC,eAAe,CAAC,uBAAuB,GAAG,GAAG,GAAG;IAChE,eAAe,CAAC,eAAe,CAAC,mBAAmB,GAAG,IAAI,GAAG;IAC7D,eAAe,CAAC,eAAe,CAAC,sBAAsB,GAAG,IAAI,GAAG;IAChE,eAAe,CAAC,eAAe,CAAC,iBAAiB,GAAG,IAAI,GAAG;IAC3D,eAAe,CAAC,eAAe,CAAC,kBAAkB,GAAG,IAAI,GAAG;IAC5D,eAAe,CAAC,eAAe,CAAC,aAAa,GAAG,IAAI,GAAG;IACvD,eAAe,CAAC,eAAe,CAAC,0BAA0B,GAAG,IAAI,GAAG;IACpE,4BAA4B;IAC5B,eAAe,CAAC,eAAe,CAAC,yBAAyB,GAAG,IAAI,GAAG;IACnE,eAAe,CAAC,eAAe,CAAC,2BAA2B,GAAG,IAAI,GAAG;IACrE,eAAe,CAAC,eAAe,CAAC,+BAA+B,GAAG,IAAI,GAAG;IACzE,eAAe,CAAC,eAAe,CAAC,wBAAwB,GAAG,IAAI,GAAG;IAClE,eAAe,CAAC,eAAe,CAAC,sBAAsB,GAAG,IAAI,GAAG;IAChE,YAAY;IACZ,eAAe,CAAC,eAAe,CAAC,4BAA4B,GAAG,IAAI,GAAG;IACtE,eAAe,CAAC,eAAe,CAAC,iBAAiB,GAAG,IAAI,GAAG;IAC3D,eAAe,CAAC,eAAe,CAAC,wBAAwB,GAAG,IAAI,GAAG;IAClE,eAAe,CAAC,eAAe,CAAC,yBAAyB,GAAG,IAAI,GAAG;IACnE,eAAe,CAAC,eAAe,CAAC,gBAAgB,GAAG,IAAI,GAAG;IAC1D,+BAA+B;IAC/B,eAAe,CAAC,eAAe,CAAC,4BAA4B,GAAG,IAAI,GAAG;IACtE,eAAe,CAAC,eAAe,CAAC,yBAAyB,GAAG,IAAI,GAAG;IACnE,eAAe,CAAC,eAAe,CAAC,uBAAuB,GAAG,IAAI,GAAG;IACjE,eAAe,CAAC,eAAe,CAAC,uBAAuB,GAAG,IAAI,GAAG;AACrE,CAAC,EAAE,mBAAmB,CAAC,kBAAkB,CAAC,CAAC;AACpC,IAAI;AACX,CAAC,SAAU,eAAe;IACtB,iBAAiB;IACjB,eAAe,CAAC,iBAAiB,GAAG;IACpC,eAAe,CAAC,kBAAkB,GAAG;IACrC,eAAe,CAAC,4BAA4B,GAAG;IAC/C,sBAAsB;IACtB,eAAe,CAAC,eAAe,GAAG;IAClC,eAAe,CAAC,eAAe,GAAG;IAClC,eAAe,CAAC,kBAAkB,GAAG;IACrC,iBAAiB;IACjB,eAAe,CAAC,oBAAoB,GAAG;IACvC,eAAe,CAAC,kBAAkB,GAAG;IACrC,eAAe,CAAC,qBAAqB,GAAG;IACxC,eAAe,CAAC,yBAAyB,GAAG;IAC5C,kBAAkB;IAClB,eAAe,CAAC,mBAAmB,GAAG;IACtC,eAAe,CAAC,uBAAuB,GAAG;IAC1C,eAAe,CAAC,qBAAqB,GAAG;AAC5C,CAAC,EAAE,mBAAmB,CAAC,kBAAkB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 759, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 767, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/types/client.types.js"], "sourcesContent": ["export var ETwitterStreamEvent;\n(function (ETwitterStreamEvent) {\n    ETwitterStreamEvent[\"Connected\"] = \"connected\";\n    ETwitterStreamEvent[\"ConnectError\"] = \"connect error\";\n    ETwitterStreamEvent[\"ConnectionError\"] = \"connection error\";\n    ETwitterStreamEvent[\"ConnectionClosed\"] = \"connection closed\";\n    ETwitterStreamEvent[\"ConnectionLost\"] = \"connection lost\";\n    ETwitterStreamEvent[\"ReconnectAttempt\"] = \"reconnect attempt\";\n    ETwitterStreamEvent[\"Reconnected\"] = \"reconnected\";\n    ETwitterStreamEvent[\"ReconnectError\"] = \"reconnect error\";\n    ETwitterStreamEvent[\"ReconnectLimitExceeded\"] = \"reconnect limit exceeded\";\n    ETwitterStreamEvent[\"DataKeepAlive\"] = \"data keep-alive\";\n    ETwitterStreamEvent[\"Data\"] = \"data event content\";\n    ETwitterStreamEvent[\"DataError\"] = \"data twitter error\";\n    ETwitterStreamEvent[\"TweetParseError\"] = \"data tweet parse error\";\n    ETwitterStreamEvent[\"Error\"] = \"stream error\";\n})(ETwitterStreamEvent || (ETwitterStreamEvent = {}));\n"], "names": [], "mappings": ";;;AAAO,IAAI;AACX,CAAC,SAAU,mBAAmB;IAC1B,mBAAmB,CAAC,YAAY,GAAG;IACnC,mBAAmB,CAAC,eAAe,GAAG;IACtC,mBAAmB,CAAC,kBAAkB,GAAG;IACzC,mBAAmB,CAAC,mBAAmB,GAAG;IAC1C,mBAAmB,CAAC,iBAAiB,GAAG;IACxC,mBAAmB,CAAC,mBAAmB,GAAG;IAC1C,mBAAmB,CAAC,cAAc,GAAG;IACrC,mBAAmB,CAAC,iBAAiB,GAAG;IACxC,mBAAmB,CAAC,yBAAyB,GAAG;IAChD,mBAAmB,CAAC,gBAAgB,GAAG;IACvC,mBAAmB,CAAC,OAAO,GAAG;IAC9B,mBAAmB,CAAC,YAAY,GAAG;IACnC,mBAAmB,CAAC,kBAAkB,GAAG;IACzC,mBAAmB,CAAC,QAAQ,GAAG;AACnC,CAAC,EAAE,uBAAuB,CAAC,sBAAsB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 793, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/types/plugins/client.plugins.types.js"], "sourcesContent": ["export class TwitterApiPluginResponseOverride {\n    constructor(value) {\n        this.value = value;\n    }\n}\n"], "names": [], "mappings": ";;;AAAO,MAAM;IACT,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,KAAK,GAAG;IACjB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 815, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/types/plugins/index.js"], "sourcesContent": ["export * from './client.plugins.types';\n"], "names": [], "mappings": ";AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 833, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/types/index.js"], "sourcesContent": ["export * from './v1';\nexport * from './v2';\nexport * from './errors.types';\nexport * from './responses.types';\nexport * from './client.types';\nexport * from './auth.types';\nexport * from './plugins';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 869, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/settings.js"], "sourcesContent": ["export const TwitterApiV2Settings = {\n    debug: false,\n    deprecationWarnings: true,\n    logger: { log: console.log.bind(console) },\n};\n"], "names": [], "mappings": ";;;AAAO,MAAM,uBAAuB;IAChC,OAAO;IACP,qBAAqB;IACrB,QAAQ;QAAE,KAAK,QAAQ,GAAG,CAAC,IAAI,CAAC;IAAS;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 885, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/helpers.js"], "sourcesContent": ["import { TwitterApiV2Settings } from './settings';\nexport function sharedPromise(getter) {\n    const sharedPromise = {\n        value: undefined,\n        promise: getter().then(val => {\n            sharedPromise.value = val;\n            return val;\n        }),\n    };\n    return sharedPromise;\n}\nexport function arrayWrap(value) {\n    if (Array.isArray(value)) {\n        return value;\n    }\n    return [value];\n}\nexport function trimUndefinedProperties(object) {\n    // Delete undefined parameters\n    for (const parameter in object) {\n        if (object[parameter] === undefined)\n            delete object[parameter];\n    }\n}\nexport function isTweetStreamV2ErrorPayload(payload) {\n    // Is error only if 'errors' is present and 'data' does not exists\n    return typeof payload === 'object'\n        && 'errors' in payload\n        && !('data' in payload);\n}\nexport function hasMultipleItems(item) {\n    if (Array.isArray(item) && item.length > 1) {\n        return true;\n    }\n    return item.toString().includes(',');\n}\nconst deprecationWarningsCache = new Set();\nexport function safeDeprecationWarning(message) {\n    if (typeof console === 'undefined' || !console.warn || !TwitterApiV2Settings.deprecationWarnings) {\n        return;\n    }\n    const hash = `${message.instance}-${message.method}-${message.problem}`;\n    if (deprecationWarningsCache.has(hash)) {\n        return;\n    }\n    const formattedMsg = `[twitter-api-v2] Deprecation warning: In ${message.instance}.${message.method}() call` +\n        `, ${message.problem}.\\n${message.resolution}.`;\n    console.warn(formattedMsg);\n    console.warn('To disable this message, import variable TwitterApiV2Settings from twitter-api-v2 and set TwitterApiV2Settings.deprecationWarnings to false.');\n    deprecationWarningsCache.add(hash);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,SAAS,cAAc,MAAM;IAChC,MAAM,gBAAgB;QAClB,OAAO;QACP,SAAS,SAAS,IAAI,CAAC,CAAA;YACnB,cAAc,KAAK,GAAG;YACtB,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACO,SAAS,UAAU,KAAK;IAC3B,IAAI,MAAM,OAAO,CAAC,QAAQ;QACtB,OAAO;IACX;IACA,OAAO;QAAC;KAAM;AAClB;AACO,SAAS,wBAAwB,MAAM;IAC1C,8BAA8B;IAC9B,IAAK,MAAM,aAAa,OAAQ;QAC5B,IAAI,MAAM,CAAC,UAAU,KAAK,WACtB,OAAO,MAAM,CAAC,UAAU;IAChC;AACJ;AACO,SAAS,4BAA4B,OAAO;IAC/C,kEAAkE;IAClE,OAAO,OAAO,YAAY,YACnB,YAAY,WACZ,CAAC,CAAC,UAAU,OAAO;AAC9B;AACO,SAAS,iBAAiB,IAAI;IACjC,IAAI,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,GAAG,GAAG;QACxC,OAAO;IACX;IACA,OAAO,KAAK,QAAQ,GAAG,QAAQ,CAAC;AACpC;AACA,MAAM,2BAA2B,IAAI;AAC9B,SAAS,uBAAuB,OAAO;IAC1C,IAAI,OAAO,YAAY,eAAe,CAAC,QAAQ,IAAI,IAAI,CAAC,iKAAA,CAAA,uBAAoB,CAAC,mBAAmB,EAAE;QAC9F;IACJ;IACA,MAAM,OAAO,GAAG,QAAQ,QAAQ,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,OAAO,EAAE;IACvE,IAAI,yBAAyB,GAAG,CAAC,OAAO;QACpC;IACJ;IACA,MAAM,eAAe,CAAC,yCAAyC,EAAE,QAAQ,QAAQ,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC,GACxG,CAAC,EAAE,EAAE,QAAQ,OAAO,CAAC,GAAG,EAAE,QAAQ,UAAU,CAAC,CAAC,CAAC;IACnD,QAAQ,IAAI,CAAC;IACb,QAAQ,IAAI,CAAC;IACb,yBAAyB,GAAG,CAAC;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/client-mixins/request-handler.helper.js"], "sourcesContent": ["import { request } from 'https';\nimport { TwitterApiV2Settings } from '../settings';\nimport TweetStream from '../stream/TweetStream';\nimport { ApiPartialResponseError, ApiRequestError, ApiResponseError } from '../types';\nimport * as zlib from 'zlib';\nimport { EventEmitter } from 'events';\nexport class RequestHandlerHelper {\n    constructor(requestData) {\n        this.requestData = requestData;\n        this.requestErrorHandled = false;\n        this.responseData = [];\n    }\n    /* Request helpers */\n    get hrefPathname() {\n        const url = this.requestData.url;\n        return url.hostname + url.pathname;\n    }\n    isCompressionDisabled() {\n        return !this.requestData.compression || this.requestData.compression === 'identity';\n    }\n    isFormEncodedEndpoint() {\n        return this.requestData.url.href.startsWith('https://api.twitter.com/oauth/');\n    }\n    /* Error helpers */\n    createRequestError(error) {\n        if (TwitterApiV2Settings.debug) {\n            TwitterApiV2Settings.logger.log('Request error:', error);\n        }\n        return new ApiRequestError('Request failed.', {\n            request: this.req,\n            error,\n        });\n    }\n    createPartialResponseError(error, abortClose) {\n        const res = this.res;\n        let message = `Request failed with partial response with HTTP code ${res.statusCode}`;\n        if (abortClose) {\n            message += ' (connection abruptly closed)';\n        }\n        else {\n            message += ' (parse error)';\n        }\n        return new ApiPartialResponseError(message, {\n            request: this.req,\n            response: this.res,\n            responseError: error,\n            rawContent: Buffer.concat(this.responseData).toString(),\n        });\n    }\n    formatV1Errors(errors) {\n        return errors\n            .map(({ code, message }) => `${message} (Twitter code ${code})`)\n            .join(', ');\n    }\n    formatV2Error(error) {\n        return `${error.title}: ${error.detail} (see ${error.type})`;\n    }\n    createResponseError({ res, data, rateLimit, code }) {\n        var _a;\n        if (TwitterApiV2Settings.debug) {\n            TwitterApiV2Settings.logger.log(`Request failed with code ${code}, data:`, data);\n            TwitterApiV2Settings.logger.log('Response headers:', res.headers);\n        }\n        // Errors formatting.\n        let errorString = `Request failed with code ${code}`;\n        if ((_a = data === null || data === void 0 ? void 0 : data.errors) === null || _a === void 0 ? void 0 : _a.length) {\n            const errors = data.errors;\n            if ('code' in errors[0]) {\n                errorString += ' - ' + this.formatV1Errors(errors);\n            }\n            else {\n                errorString += ' - ' + this.formatV2Error(data);\n            }\n        }\n        return new ApiResponseError(errorString, {\n            code,\n            data,\n            headers: res.headers,\n            request: this.req,\n            response: res,\n            rateLimit,\n        });\n    }\n    /* Response helpers */\n    getResponseDataStream(res) {\n        if (this.isCompressionDisabled()) {\n            return res;\n        }\n        const contentEncoding = (res.headers['content-encoding'] || 'identity').trim().toLowerCase();\n        if (contentEncoding === 'br') {\n            const brotli = zlib.createBrotliDecompress({\n                flush: zlib.constants.BROTLI_OPERATION_FLUSH,\n                finishFlush: zlib.constants.BROTLI_OPERATION_FLUSH,\n            });\n            res.pipe(brotli);\n            return brotli;\n        }\n        if (contentEncoding === 'gzip') {\n            const gunzip = zlib.createGunzip({\n                flush: zlib.constants.Z_SYNC_FLUSH,\n                finishFlush: zlib.constants.Z_SYNC_FLUSH,\n            });\n            res.pipe(gunzip);\n            return gunzip;\n        }\n        if (contentEncoding === 'deflate') {\n            const inflate = zlib.createInflate({\n                flush: zlib.constants.Z_SYNC_FLUSH,\n                finishFlush: zlib.constants.Z_SYNC_FLUSH,\n            });\n            res.pipe(inflate);\n            return inflate;\n        }\n        return res;\n    }\n    detectResponseType(res) {\n        var _a, _b;\n        // Auto parse if server responds with JSON body\n        if (((_a = res.headers['content-type']) === null || _a === void 0 ? void 0 : _a.includes('application/json')) || ((_b = res.headers['content-type']) === null || _b === void 0 ? void 0 : _b.includes('application/problem+json'))) {\n            return 'json';\n        }\n        // f-e oauth token endpoints\n        else if (this.isFormEncodedEndpoint()) {\n            return 'url';\n        }\n        return 'text';\n    }\n    getParsedResponse(res) {\n        const data = this.responseData;\n        const mode = this.requestData.forceParseMode || this.detectResponseType(res);\n        if (mode === 'buffer') {\n            return Buffer.concat(data);\n        }\n        else if (mode === 'text') {\n            return Buffer.concat(data).toString();\n        }\n        else if (mode === 'json') {\n            const asText = Buffer.concat(data).toString();\n            return asText.length ? JSON.parse(asText) : undefined;\n        }\n        else if (mode === 'url') {\n            const asText = Buffer.concat(data).toString();\n            const formEntries = {};\n            for (const [item, value] of new URLSearchParams(asText)) {\n                formEntries[item] = value;\n            }\n            return formEntries;\n        }\n        else {\n            // mode === 'none'\n            return undefined;\n        }\n    }\n    getRateLimitFromResponse(res) {\n        let rateLimit = undefined;\n        if (res.headers['x-rate-limit-limit']) {\n            rateLimit = {\n                limit: Number(res.headers['x-rate-limit-limit']),\n                remaining: Number(res.headers['x-rate-limit-remaining']),\n                reset: Number(res.headers['x-rate-limit-reset']),\n            };\n            if (res.headers['x-app-limit-24hour-limit']) {\n                rateLimit.day = {\n                    limit: Number(res.headers['x-app-limit-24hour-limit']),\n                    remaining: Number(res.headers['x-app-limit-24hour-remaining']),\n                    reset: Number(res.headers['x-app-limit-24hour-reset']),\n                };\n            }\n            if (this.requestData.rateLimitSaver) {\n                this.requestData.rateLimitSaver(rateLimit);\n            }\n        }\n        return rateLimit;\n    }\n    /* Request event handlers */\n    onSocketEventHandler(reject, cleanupListener, socket) {\n        const onClose = this.onSocketCloseHandler.bind(this, reject);\n        socket.on('close', onClose);\n        cleanupListener.on('complete', () => socket.off('close', onClose));\n    }\n    onSocketCloseHandler(reject) {\n        this.req.removeAllListeners('timeout');\n        const res = this.res;\n        if (res) {\n            // Response ok, res.close/res.end can handle request ending\n            return;\n        }\n        if (!this.requestErrorHandled) {\n            return reject(this.createRequestError(new Error('Socket closed without any information.')));\n        }\n        // else: other situation\n    }\n    requestErrorHandler(reject, requestError) {\n        var _a, _b;\n        (_b = (_a = this.requestData).requestEventDebugHandler) === null || _b === void 0 ? void 0 : _b.call(_a, 'request-error', { requestError });\n        this.requestErrorHandled = true;\n        reject(this.createRequestError(requestError));\n    }\n    timeoutErrorHandler() {\n        this.requestErrorHandled = true;\n        this.req.destroy(new Error('Request timeout.'));\n    }\n    /* Response event handlers */\n    classicResponseHandler(resolve, reject, res) {\n        this.res = res;\n        const dataStream = this.getResponseDataStream(res);\n        // Register the response data\n        dataStream.on('data', chunk => this.responseData.push(chunk));\n        dataStream.on('end', this.onResponseEndHandler.bind(this, resolve, reject));\n        dataStream.on('close', this.onResponseCloseHandler.bind(this, resolve, reject));\n        // Debug handlers\n        if (this.requestData.requestEventDebugHandler) {\n            this.requestData.requestEventDebugHandler('response', { res });\n            res.on('aborted', error => this.requestData.requestEventDebugHandler('response-aborted', { error }));\n            res.on('error', error => this.requestData.requestEventDebugHandler('response-error', { error }));\n            res.on('close', () => this.requestData.requestEventDebugHandler('response-close', { data: this.responseData }));\n            res.on('end', () => this.requestData.requestEventDebugHandler('response-end'));\n        }\n    }\n    onResponseEndHandler(resolve, reject) {\n        const rateLimit = this.getRateLimitFromResponse(this.res);\n        let data;\n        try {\n            data = this.getParsedResponse(this.res);\n        }\n        catch (e) {\n            reject(this.createPartialResponseError(e, false));\n            return;\n        }\n        // Handle bad error codes\n        const code = this.res.statusCode;\n        if (code >= 400) {\n            reject(this.createResponseError({ data, res: this.res, rateLimit, code }));\n            return;\n        }\n        if (TwitterApiV2Settings.debug) {\n            TwitterApiV2Settings.logger.log(`[${this.requestData.options.method} ${this.hrefPathname}]: Request succeeds with code ${this.res.statusCode}`);\n            TwitterApiV2Settings.logger.log('Response body:', data);\n        }\n        resolve({\n            data,\n            headers: this.res.headers,\n            rateLimit,\n        });\n    }\n    onResponseCloseHandler(resolve, reject) {\n        const res = this.res;\n        if (res.aborted) {\n            // Try to parse the request (?)\n            try {\n                this.getParsedResponse(this.res);\n                // Ok, try to resolve normally the request\n                return this.onResponseEndHandler(resolve, reject);\n            }\n            catch (e) {\n                // Parse error, just drop with content\n                return reject(this.createPartialResponseError(e, true));\n            }\n        }\n        if (!res.complete) {\n            return reject(this.createPartialResponseError(new Error('Response has been interrupted before response could be parsed.'), true));\n        }\n        // else: end has been called\n    }\n    streamResponseHandler(resolve, reject, res) {\n        const code = res.statusCode;\n        if (code < 400) {\n            if (TwitterApiV2Settings.debug) {\n                TwitterApiV2Settings.logger.log(`[${this.requestData.options.method} ${this.hrefPathname}]: Request succeeds with code ${res.statusCode} (starting stream)`);\n            }\n            const dataStream = this.getResponseDataStream(res);\n            // HTTP code ok, consume stream\n            resolve({ req: this.req, res: dataStream, originalResponse: res, requestData: this.requestData });\n        }\n        else {\n            // Handle response normally, can only rejects\n            this.classicResponseHandler(() => undefined, reject, res);\n        }\n    }\n    /* Wrappers for request lifecycle */\n    debugRequest() {\n        const url = this.requestData.url;\n        TwitterApiV2Settings.logger.log(`[${this.requestData.options.method} ${this.hrefPathname}]`, this.requestData.options);\n        if (url.search) {\n            TwitterApiV2Settings.logger.log('Request parameters:', [...url.searchParams.entries()].map(([key, value]) => `${key}: ${value}`));\n        }\n        if (this.requestData.body) {\n            TwitterApiV2Settings.logger.log('Request body:', this.requestData.body);\n        }\n    }\n    buildRequest() {\n        var _a;\n        const url = this.requestData.url;\n        const auth = url.username ? `${url.username}:${url.password}` : undefined;\n        const headers = (_a = this.requestData.options.headers) !== null && _a !== void 0 ? _a : {};\n        if (this.requestData.compression === true || this.requestData.compression === 'brotli') {\n            headers['accept-encoding'] = 'br;q=1.0, gzip;q=0.8, deflate;q=0.5, *;q=0.1';\n        }\n        else if (this.requestData.compression === 'gzip') {\n            headers['accept-encoding'] = 'gzip;q=1, deflate;q=0.5, *;q=0.1';\n        }\n        else if (this.requestData.compression === 'deflate') {\n            headers['accept-encoding'] = 'deflate;q=1, *;q=0.1';\n        }\n        if (TwitterApiV2Settings.debug) {\n            this.debugRequest();\n        }\n        this.req = request({\n            ...this.requestData.options,\n            // Define URL params manually, addresses dependencies error https://github.com/PLhery/node-twitter-api-v2/issues/94\n            host: url.hostname,\n            port: url.port || undefined,\n            path: url.pathname + url.search,\n            protocol: url.protocol,\n            auth,\n            headers,\n        });\n    }\n    registerRequestEventDebugHandlers(req) {\n        req.on('close', () => this.requestData.requestEventDebugHandler('close'));\n        req.on('abort', () => this.requestData.requestEventDebugHandler('abort'));\n        req.on('socket', socket => {\n            this.requestData.requestEventDebugHandler('socket', { socket });\n            socket.on('error', error => this.requestData.requestEventDebugHandler('socket-error', { socket, error }));\n            socket.on('connect', () => this.requestData.requestEventDebugHandler('socket-connect', { socket }));\n            socket.on('close', withError => this.requestData.requestEventDebugHandler('socket-close', { socket, withError }));\n            socket.on('end', () => this.requestData.requestEventDebugHandler('socket-end', { socket }));\n            socket.on('lookup', (...data) => this.requestData.requestEventDebugHandler('socket-lookup', { socket, data }));\n            socket.on('timeout', () => this.requestData.requestEventDebugHandler('socket-timeout', { socket }));\n        });\n    }\n    makeRequest() {\n        this.buildRequest();\n        return new Promise((_resolve, _reject) => {\n            // Hooks to call when promise is fulfulled to cleanup the socket (shared between requests)\n            const resolve = value => {\n                cleanupListener.emit('complete');\n                _resolve(value);\n            };\n            const reject = value => {\n                cleanupListener.emit('complete');\n                _reject(value);\n            };\n            const cleanupListener = new EventEmitter();\n            const req = this.req;\n            // Handle request errors\n            req.on('error', this.requestErrorHandler.bind(this, reject));\n            req.on('socket', this.onSocketEventHandler.bind(this, reject, cleanupListener));\n            req.on('response', this.classicResponseHandler.bind(this, resolve, reject));\n            if (this.requestData.options.timeout) {\n                req.on('timeout', this.timeoutErrorHandler.bind(this));\n            }\n            // Debug handlers\n            if (this.requestData.requestEventDebugHandler) {\n                this.registerRequestEventDebugHandlers(req);\n            }\n            if (this.requestData.body) {\n                req.write(this.requestData.body);\n            }\n            req.end();\n        });\n    }\n    async makeRequestAsStream() {\n        const { req, res, requestData, originalResponse } = await this.makeRequestAndResolveWhenReady();\n        return new TweetStream(requestData, { req, res, originalResponse });\n    }\n    makeRequestAndResolveWhenReady() {\n        this.buildRequest();\n        return new Promise((resolve, reject) => {\n            const req = this.req;\n            // Handle request errors\n            req.on('error', this.requestErrorHandler.bind(this, reject));\n            req.on('response', this.streamResponseHandler.bind(this, resolve, reject));\n            if (this.requestData.body) {\n                req.write(this.requestData.body);\n            }\n            req.end();\n        });\n    }\n}\nexport default RequestHandlerHelper;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;;;;;;;AACO,MAAM;IACT,YAAY,WAAW,CAAE;QACrB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,YAAY,GAAG,EAAE;IAC1B;IACA,mBAAmB,GACnB,IAAI,eAAe;QACf,MAAM,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG;QAChC,OAAO,IAAI,QAAQ,GAAG,IAAI,QAAQ;IACtC;IACA,wBAAwB;QACpB,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,KAAK;IAC7E;IACA,wBAAwB;QACpB,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;IAChD;IACA,iBAAiB,GACjB,mBAAmB,KAAK,EAAE;QACtB,IAAI,iKAAA,CAAA,uBAAoB,CAAC,KAAK,EAAE;YAC5B,iKAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB;QACtD;QACA,OAAO,IAAI,iLAAA,CAAA,kBAAe,CAAC,mBAAmB;YAC1C,SAAS,IAAI,CAAC,GAAG;YACjB;QACJ;IACJ;IACA,2BAA2B,KAAK,EAAE,UAAU,EAAE;QAC1C,MAAM,MAAM,IAAI,CAAC,GAAG;QACpB,IAAI,UAAU,CAAC,oDAAoD,EAAE,IAAI,UAAU,EAAE;QACrF,IAAI,YAAY;YACZ,WAAW;QACf,OACK;YACD,WAAW;QACf;QACA,OAAO,IAAI,iLAAA,CAAA,0BAAuB,CAAC,SAAS;YACxC,SAAS,IAAI,CAAC,GAAG;YACjB,UAAU,IAAI,CAAC,GAAG;YAClB,eAAe;YACf,YAAY,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ;QACzD;IACJ;IACA,eAAe,MAAM,EAAE;QACnB,OAAO,OACF,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAK,GAAG,QAAQ,eAAe,EAAE,KAAK,CAAC,CAAC,EAC9D,IAAI,CAAC;IACd;IACA,cAAc,KAAK,EAAE;QACjB,OAAO,GAAG,MAAM,KAAK,CAAC,EAAE,EAAE,MAAM,MAAM,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;IAChE;IACA,oBAAoB,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE;QAChD,IAAI;QACJ,IAAI,iKAAA,CAAA,uBAAoB,CAAC,KAAK,EAAE;YAC5B,iKAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,yBAAyB,EAAE,KAAK,OAAO,CAAC,EAAE;YAC3E,iKAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,IAAI,OAAO;QACpE;QACA,qBAAqB;QACrB,IAAI,cAAc,CAAC,yBAAyB,EAAE,MAAM;QACpD,IAAI,CAAC,KAAK,SAAS,QAAQ,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,EAAE;YAC/G,MAAM,SAAS,KAAK,MAAM;YAC1B,IAAI,UAAU,MAAM,CAAC,EAAE,EAAE;gBACrB,eAAe,QAAQ,IAAI,CAAC,cAAc,CAAC;YAC/C,OACK;gBACD,eAAe,QAAQ,IAAI,CAAC,aAAa,CAAC;YAC9C;QACJ;QACA,OAAO,IAAI,iLAAA,CAAA,mBAAgB,CAAC,aAAa;YACrC;YACA;YACA,SAAS,IAAI,OAAO;YACpB,SAAS,IAAI,CAAC,GAAG;YACjB,UAAU;YACV;QACJ;IACJ;IACA,oBAAoB,GACpB,sBAAsB,GAAG,EAAE;QACvB,IAAI,IAAI,CAAC,qBAAqB,IAAI;YAC9B,OAAO;QACX;QACA,MAAM,kBAAkB,CAAC,IAAI,OAAO,CAAC,mBAAmB,IAAI,UAAU,EAAE,IAAI,GAAG,WAAW;QAC1F,IAAI,oBAAoB,MAAM;YAC1B,MAAM,SAAS,CAAA,GAAA,iGAAA,CAAA,yBAA2B,AAAD,EAAE;gBACvC,OAAO,iGAAA,CAAA,YAAc,CAAC,sBAAsB;gBAC5C,aAAa,iGAAA,CAAA,YAAc,CAAC,sBAAsB;YACtD;YACA,IAAI,IAAI,CAAC;YACT,OAAO;QACX;QACA,IAAI,oBAAoB,QAAQ;YAC5B,MAAM,SAAS,CAAA,GAAA,iGAAA,CAAA,eAAiB,AAAD,EAAE;gBAC7B,OAAO,iGAAA,CAAA,YAAc,CAAC,YAAY;gBAClC,aAAa,iGAAA,CAAA,YAAc,CAAC,YAAY;YAC5C;YACA,IAAI,IAAI,CAAC;YACT,OAAO;QACX;QACA,IAAI,oBAAoB,WAAW;YAC/B,MAAM,UAAU,CAAA,GAAA,iGAAA,CAAA,gBAAkB,AAAD,EAAE;gBAC/B,OAAO,iGAAA,CAAA,YAAc,CAAC,YAAY;gBAClC,aAAa,iGAAA,CAAA,YAAc,CAAC,YAAY;YAC5C;YACA,IAAI,IAAI,CAAC;YACT,OAAO;QACX;QACA,OAAO;IACX;IACA,mBAAmB,GAAG,EAAE;QACpB,IAAI,IAAI;QACR,+CAA+C;QAC/C,IAAI,CAAC,CAAC,KAAK,IAAI,OAAO,CAAC,eAAe,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,CAAC,mBAAmB,KAAK,CAAC,CAAC,KAAK,IAAI,OAAO,CAAC,eAAe,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,CAAC,2BAA2B,GAAG;YAChO,OAAO;QACX,OAEK,IAAI,IAAI,CAAC,qBAAqB,IAAI;YACnC,OAAO;QACX;QACA,OAAO;IACX;IACA,kBAAkB,GAAG,EAAE;QACnB,MAAM,OAAO,IAAI,CAAC,YAAY;QAC9B,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc,IAAI,IAAI,CAAC,kBAAkB,CAAC;QACxE,IAAI,SAAS,UAAU;YACnB,OAAO,OAAO,MAAM,CAAC;QACzB,OACK,IAAI,SAAS,QAAQ;YACtB,OAAO,OAAO,MAAM,CAAC,MAAM,QAAQ;QACvC,OACK,IAAI,SAAS,QAAQ;YACtB,MAAM,SAAS,OAAO,MAAM,CAAC,MAAM,QAAQ;YAC3C,OAAO,OAAO,MAAM,GAAG,KAAK,KAAK,CAAC,UAAU;QAChD,OACK,IAAI,SAAS,OAAO;YACrB,MAAM,SAAS,OAAO,MAAM,CAAC,MAAM,QAAQ;YAC3C,MAAM,cAAc,CAAC;YACrB,KAAK,MAAM,CAAC,MAAM,MAAM,IAAI,IAAI,gBAAgB,QAAS;gBACrD,WAAW,CAAC,KAAK,GAAG;YACxB;YACA,OAAO;QACX,OACK;YACD,kBAAkB;YAClB,OAAO;QACX;IACJ;IACA,yBAAyB,GAAG,EAAE;QAC1B,IAAI,YAAY;QAChB,IAAI,IAAI,OAAO,CAAC,qBAAqB,EAAE;YACnC,YAAY;gBACR,OAAO,OAAO,IAAI,OAAO,CAAC,qBAAqB;gBAC/C,WAAW,OAAO,IAAI,OAAO,CAAC,yBAAyB;gBACvD,OAAO,OAAO,IAAI,OAAO,CAAC,qBAAqB;YACnD;YACA,IAAI,IAAI,OAAO,CAAC,2BAA2B,EAAE;gBACzC,UAAU,GAAG,GAAG;oBACZ,OAAO,OAAO,IAAI,OAAO,CAAC,2BAA2B;oBACrD,WAAW,OAAO,IAAI,OAAO,CAAC,+BAA+B;oBAC7D,OAAO,OAAO,IAAI,OAAO,CAAC,2BAA2B;gBACzD;YACJ;YACA,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE;gBACjC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;YACpC;QACJ;QACA,OAAO;IACX;IACA,0BAA0B,GAC1B,qBAAqB,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE;QAClD,MAAM,UAAU,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE;QACrD,OAAO,EAAE,CAAC,SAAS;QACnB,gBAAgB,EAAE,CAAC,YAAY,IAAM,OAAO,GAAG,CAAC,SAAS;IAC7D;IACA,qBAAqB,MAAM,EAAE;QACzB,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC;QAC5B,MAAM,MAAM,IAAI,CAAC,GAAG;QACpB,IAAI,KAAK;YACL,2DAA2D;YAC3D;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,OAAO,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,MAAM;QACpD;IACA,wBAAwB;IAC5B;IACA,oBAAoB,MAAM,EAAE,YAAY,EAAE;QACtC,IAAI,IAAI;QACR,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,WAAW,EAAE,wBAAwB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,iBAAiB;YAAE;QAAa;QACzI,IAAI,CAAC,mBAAmB,GAAG;QAC3B,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC;IACA,sBAAsB;QAClB,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,MAAM;IAC/B;IACA,2BAA2B,GAC3B,uBAAuB,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE;QACzC,IAAI,CAAC,GAAG,GAAG;QACX,MAAM,aAAa,IAAI,CAAC,qBAAqB,CAAC;QAC9C,6BAA6B;QAC7B,WAAW,EAAE,CAAC,QAAQ,CAAA,QAAS,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;QACtD,WAAW,EAAE,CAAC,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS;QACnE,WAAW,EAAE,CAAC,SAAS,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS;QACvE,iBAAiB;QACjB,IAAI,IAAI,CAAC,WAAW,CAAC,wBAAwB,EAAE;YAC3C,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,YAAY;gBAAE;YAAI;YAC5D,IAAI,EAAE,CAAC,WAAW,CAAA,QAAS,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,oBAAoB;oBAAE;gBAAM;YACjG,IAAI,EAAE,CAAC,SAAS,CAAA,QAAS,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,kBAAkB;oBAAE;gBAAM;YAC7F,IAAI,EAAE,CAAC,SAAS,IAAM,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,kBAAkB;oBAAE,MAAM,IAAI,CAAC,YAAY;gBAAC;YAC5G,IAAI,EAAE,CAAC,OAAO,IAAM,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC;QAClE;IACJ;IACA,qBAAqB,OAAO,EAAE,MAAM,EAAE;QAClC,MAAM,YAAY,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,GAAG;QACxD,IAAI;QACJ,IAAI;YACA,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG;QAC1C,EACA,OAAO,GAAG;YACN,OAAO,IAAI,CAAC,0BAA0B,CAAC,GAAG;YAC1C;QACJ;QACA,yBAAyB;QACzB,MAAM,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU;QAChC,IAAI,QAAQ,KAAK;YACb,OAAO,IAAI,CAAC,mBAAmB,CAAC;gBAAE;gBAAM,KAAK,IAAI,CAAC,GAAG;gBAAE;gBAAW;YAAK;YACvE;QACJ;QACA,IAAI,iKAAA,CAAA,uBAAoB,CAAC,KAAK,EAAE;YAC5B,iKAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,8BAA8B,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;YAC9I,iKAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB;QACtD;QACA,QAAQ;YACJ;YACA,SAAS,IAAI,CAAC,GAAG,CAAC,OAAO;YACzB;QACJ;IACJ;IACA,uBAAuB,OAAO,EAAE,MAAM,EAAE;QACpC,MAAM,MAAM,IAAI,CAAC,GAAG;QACpB,IAAI,IAAI,OAAO,EAAE;YACb,+BAA+B;YAC/B,IAAI;gBACA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG;gBAC/B,0CAA0C;gBAC1C,OAAO,IAAI,CAAC,oBAAoB,CAAC,SAAS;YAC9C,EACA,OAAO,GAAG;gBACN,sCAAsC;gBACtC,OAAO,OAAO,IAAI,CAAC,0BAA0B,CAAC,GAAG;YACrD;QACJ;QACA,IAAI,CAAC,IAAI,QAAQ,EAAE;YACf,OAAO,OAAO,IAAI,CAAC,0BAA0B,CAAC,IAAI,MAAM,mEAAmE;QAC/H;IACA,4BAA4B;IAChC;IACA,sBAAsB,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE;QACxC,MAAM,OAAO,IAAI,UAAU;QAC3B,IAAI,OAAO,KAAK;YACZ,IAAI,iKAAA,CAAA,uBAAoB,CAAC,KAAK,EAAE;gBAC5B,iKAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,8BAA8B,EAAE,IAAI,UAAU,CAAC,kBAAkB,CAAC;YAC/J;YACA,MAAM,aAAa,IAAI,CAAC,qBAAqB,CAAC;YAC9C,+BAA+B;YAC/B,QAAQ;gBAAE,KAAK,IAAI,CAAC,GAAG;gBAAE,KAAK;gBAAY,kBAAkB;gBAAK,aAAa,IAAI,CAAC,WAAW;YAAC;QACnG,OACK;YACD,6CAA6C;YAC7C,IAAI,CAAC,sBAAsB,CAAC,IAAM,WAAW,QAAQ;QACzD;IACJ;IACA,kCAAkC,GAClC,eAAe;QACX,MAAM,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG;QAChC,iKAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;QACrH,IAAI,IAAI,MAAM,EAAE;YACZ,iKAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB;mBAAI,IAAI,YAAY,CAAC,OAAO;aAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,GAAG,IAAI,EAAE,EAAE,OAAO;QACnI;QACA,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;YACvB,iKAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,WAAW,CAAC,IAAI;QAC1E;IACJ;IACA,eAAe;QACX,IAAI;QACJ,MAAM,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG;QAChC,MAAM,OAAO,IAAI,QAAQ,GAAG,GAAG,IAAI,QAAQ,CAAC,CAAC,EAAE,IAAI,QAAQ,EAAE,GAAG;QAChE,MAAM,UAAU,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,CAAC;QAC1F,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,KAAK,QAAQ,IAAI,CAAC,WAAW,CAAC,WAAW,KAAK,UAAU;YACpF,OAAO,CAAC,kBAAkB,GAAG;QACjC,OACK,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,KAAK,QAAQ;YAC9C,OAAO,CAAC,kBAAkB,GAAG;QACjC,OACK,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,KAAK,WAAW;YACjD,OAAO,CAAC,kBAAkB,GAAG;QACjC;QACA,IAAI,iKAAA,CAAA,uBAAoB,CAAC,KAAK,EAAE;YAC5B,IAAI,CAAC,YAAY;QACrB;QACA,IAAI,CAAC,GAAG,GAAG,CAAA,GAAA,mGAAA,CAAA,UAAO,AAAD,EAAE;YACf,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO;YAC3B,mHAAmH;YACnH,MAAM,IAAI,QAAQ;YAClB,MAAM,IAAI,IAAI,IAAI;YAClB,MAAM,IAAI,QAAQ,GAAG,IAAI,MAAM;YAC/B,UAAU,IAAI,QAAQ;YACtB;YACA;QACJ;IACJ;IACA,kCAAkC,GAAG,EAAE;QACnC,IAAI,EAAE,CAAC,SAAS,IAAM,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC;QAChE,IAAI,EAAE,CAAC,SAAS,IAAM,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC;QAChE,IAAI,EAAE,CAAC,UAAU,CAAA;YACb,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,UAAU;gBAAE;YAAO;YAC7D,OAAO,EAAE,CAAC,SAAS,CAAA,QAAS,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,gBAAgB;oBAAE;oBAAQ;gBAAM;YACtG,OAAO,EAAE,CAAC,WAAW,IAAM,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,kBAAkB;oBAAE;gBAAO;YAChG,OAAO,EAAE,CAAC,SAAS,CAAA,YAAa,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,gBAAgB;oBAAE;oBAAQ;gBAAU;YAC9G,OAAO,EAAE,CAAC,OAAO,IAAM,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,cAAc;oBAAE;gBAAO;YACxF,OAAO,EAAE,CAAC,UAAU,CAAC,GAAG,OAAS,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,iBAAiB;oBAAE;oBAAQ;gBAAK;YAC3G,OAAO,EAAE,CAAC,WAAW,IAAM,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,kBAAkB;oBAAE;gBAAO;QACpG;IACJ;IACA,cAAc;QACV,IAAI,CAAC,YAAY;QACjB,OAAO,IAAI,QAAQ,CAAC,UAAU;YAC1B,0FAA0F;YAC1F,MAAM,UAAU,CAAA;gBACZ,gBAAgB,IAAI,CAAC;gBACrB,SAAS;YACb;YACA,MAAM,SAAS,CAAA;gBACX,gBAAgB,IAAI,CAAC;gBACrB,QAAQ;YACZ;YACA,MAAM,kBAAkB,IAAI,qGAAA,CAAA,eAAY;YACxC,MAAM,MAAM,IAAI,CAAC,GAAG;YACpB,wBAAwB;YACxB,IAAI,EAAE,CAAC,SAAS,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE;YACpD,IAAI,EAAE,CAAC,UAAU,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ;YAC9D,IAAI,EAAE,CAAC,YAAY,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS;YACnE,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE;gBAClC,IAAI,EAAE,CAAC,WAAW,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI;YACxD;YACA,iBAAiB;YACjB,IAAI,IAAI,CAAC,WAAW,CAAC,wBAAwB,EAAE;gBAC3C,IAAI,CAAC,iCAAiC,CAAC;YAC3C;YACA,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;gBACvB,IAAI,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI;YACnC;YACA,IAAI,GAAG;QACX;IACJ;IACA,MAAM,sBAAsB;QACxB,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,WAAW,EAAE,gBAAgB,EAAE,GAAG,MAAM,IAAI,CAAC,8BAA8B;QAC7F,OAAO,IAAI,8KAAA,CAAA,UAAW,CAAC,aAAa;YAAE;YAAK;YAAK;QAAiB;IACrE;IACA,iCAAiC;QAC7B,IAAI,CAAC,YAAY;QACjB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,MAAM,MAAM,IAAI,CAAC,GAAG;YACpB,wBAAwB;YACxB,IAAI,EAAE,CAAC,SAAS,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE;YACpD,IAAI,EAAE,CAAC,YAAY,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS;YAClE,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;gBACvB,IAAI,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI;YACnC;YACA,IAAI,GAAG;QACX;IACJ;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1369, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/stream/TweetStreamEventCombiner.js"], "sourcesContent": ["import { EventEmitter } from 'events';\nimport { ETwitterStreamEvent } from '../types';\nexport class TweetStreamEventCombiner extends EventEmitter {\n    constructor(stream) {\n        super();\n        this.stream = stream;\n        this.stack = [];\n        this.onStreamData = this.onStreamData.bind(this);\n        this.onStreamError = this.onStreamError.bind(this);\n        this.onceNewEvent = this.once.bind(this, 'event');\n        // Init events from stream\n        stream.on(ETwitterStreamEvent.Data, this.onStreamData);\n        // Ignore reconnect errors: Don't close event combiner until connection error/closed\n        stream.on(ETwitterStreamEvent.ConnectionError, this.onStreamError);\n        stream.on(ETwitterStreamEvent.TweetParseError, this.onStreamError);\n        stream.on(ETwitterStreamEvent.ConnectionClosed, this.onStreamError);\n    }\n    /** Returns a new `Promise` that will `resolve` on next event (`data` or any sort of error). */\n    nextEvent() {\n        return new Promise(this.onceNewEvent);\n    }\n    /** Returns `true` if there's something in the stack. */\n    hasStack() {\n        return this.stack.length > 0;\n    }\n    /** Returns stacked data events, and clean the stack. */\n    popStack() {\n        const stack = this.stack;\n        this.stack = [];\n        return stack;\n    }\n    /** Cleanup all the listeners attached on stream. */\n    destroy() {\n        this.removeAllListeners();\n        this.stream.off(ETwitterStreamEvent.Data, this.onStreamData);\n        this.stream.off(ETwitterStreamEvent.ConnectionError, this.onStreamError);\n        this.stream.off(ETwitterStreamEvent.TweetParseError, this.onStreamError);\n        this.stream.off(ETwitterStreamEvent.ConnectionClosed, this.onStreamError);\n    }\n    emitEvent(type, payload) {\n        this.emit('event', { type, payload });\n    }\n    onStreamError(payload) {\n        this.emitEvent('error', payload);\n    }\n    onStreamData(payload) {\n        this.stack.push(payload);\n        this.emitEvent('data', payload);\n    }\n}\nexport default TweetStreamEventCombiner;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AACO,MAAM,iCAAiC,qGAAA,CAAA,eAAY;IACtD,YAAY,MAAM,CAAE;QAChB,KAAK;QACL,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI;QAC/C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI;QACjD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;QACzC,0BAA0B;QAC1B,OAAO,EAAE,CAAC,iLAAA,CAAA,sBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY;QACrD,oFAAoF;QACpF,OAAO,EAAE,CAAC,iLAAA,CAAA,sBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa;QACjE,OAAO,EAAE,CAAC,iLAAA,CAAA,sBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa;QACjE,OAAO,EAAE,CAAC,iLAAA,CAAA,sBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAAC,aAAa;IACtE;IACA,6FAA6F,GAC7F,YAAY;QACR,OAAO,IAAI,QAAQ,IAAI,CAAC,YAAY;IACxC;IACA,sDAAsD,GACtD,WAAW;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IACA,sDAAsD,GACtD,WAAW;QACP,MAAM,QAAQ,IAAI,CAAC,KAAK;QACxB,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,OAAO;IACX;IACA,kDAAkD,GAClD,UAAU;QACN,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iLAAA,CAAA,sBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY;QAC3D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iLAAA,CAAA,sBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa;QACvE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iLAAA,CAAA,sBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa;QACvE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iLAAA,CAAA,sBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAAC,aAAa;IAC5E;IACA,UAAU,IAAI,EAAE,OAAO,EAAE;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE;YAAM;QAAQ;IACvC;IACA,cAAc,OAAO,EAAE;QACnB,IAAI,CAAC,SAAS,CAAC,SAAS;IAC5B;IACA,aAAa,OAAO,EAAE;QAClB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAChB,IAAI,CAAC,SAAS,CAAC,QAAQ;IAC3B;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1432, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/stream/TweetStreamParser.js"], "sourcesContent": ["import { EventEmitter } from 'events';\nexport default class TweetStreamParser extends EventEmitter {\n    constructor() {\n        super(...arguments);\n        this.currentMessage = '';\n    }\n    // Code partially belongs to twitter-stream-api for this\n    // https://github.com/trygve-lie/twitter-stream-api/blob/master/lib/parser.js\n    push(chunk) {\n        this.currentMessage += chunk;\n        chunk = this.currentMessage;\n        const size = chunk.length;\n        let start = 0;\n        let offset = 0;\n        while (offset < size) {\n            // Take [offset, offset+1] inside a new string\n            if (chunk.slice(offset, offset + 2) === '\\r\\n') {\n                // If chunk contains \\r\\n after current offset,\n                // parse [start, ..., offset] as a tweet\n                const piece = chunk.slice(start, offset);\n                start = offset += 2;\n                // If empty object\n                if (!piece.length) {\n                    continue;\n                }\n                try {\n                    const payload = JSON.parse(piece);\n                    if (payload) {\n                        this.emit(EStreamParserEvent.ParsedData, payload);\n                        continue;\n                    }\n                }\n                catch (error) {\n                    this.emit(EStreamParserEvent.ParseError, error);\n                }\n            }\n            offset++;\n        }\n        this.currentMessage = chunk.slice(start, size);\n    }\n    /** Reset the currently stored message (f.e. on connection reset) */\n    reset() {\n        this.currentMessage = '';\n    }\n}\nexport var EStreamParserEvent;\n(function (EStreamParserEvent) {\n    EStreamParserEvent[\"ParsedData\"] = \"parsed data\";\n    EStreamParserEvent[\"ParseError\"] = \"parse error\";\n})(EStreamParserEvent || (EStreamParserEvent = {}));\n"], "names": [], "mappings": ";;;;AAAA;;AACe,MAAM,0BAA0B,qGAAA,CAAA,eAAY;IACvD,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,cAAc,GAAG;IAC1B;IACA,wDAAwD;IACxD,6EAA6E;IAC7E,KAAK,KAAK,EAAE;QACR,IAAI,CAAC,cAAc,IAAI;QACvB,QAAQ,IAAI,CAAC,cAAc;QAC3B,MAAM,OAAO,MAAM,MAAM;QACzB,IAAI,QAAQ;QACZ,IAAI,SAAS;QACb,MAAO,SAAS,KAAM;YAClB,8CAA8C;YAC9C,IAAI,MAAM,KAAK,CAAC,QAAQ,SAAS,OAAO,QAAQ;gBAC5C,+CAA+C;gBAC/C,wCAAwC;gBACxC,MAAM,QAAQ,MAAM,KAAK,CAAC,OAAO;gBACjC,QAAQ,UAAU;gBAClB,kBAAkB;gBAClB,IAAI,CAAC,MAAM,MAAM,EAAE;oBACf;gBACJ;gBACA,IAAI;oBACA,MAAM,UAAU,KAAK,KAAK,CAAC;oBAC3B,IAAI,SAAS;wBACT,IAAI,CAAC,IAAI,CAAC,mBAAmB,UAAU,EAAE;wBACzC;oBACJ;gBACJ,EACA,OAAO,OAAO;oBACV,IAAI,CAAC,IAAI,CAAC,mBAAmB,UAAU,EAAE;gBAC7C;YACJ;YACA;QACJ;QACA,IAAI,CAAC,cAAc,GAAG,MAAM,KAAK,CAAC,OAAO;IAC7C;IACA,kEAAkE,GAClE,QAAQ;QACJ,IAAI,CAAC,cAAc,GAAG;IAC1B;AACJ;AACO,IAAI;AACX,CAAC,SAAU,kBAAkB;IACzB,kBAAkB,CAAC,aAAa,GAAG;IACnC,kBAAkB,CAAC,aAAa,GAAG;AACvC,CAAC,EAAE,sBAAsB,CAAC,qBAAqB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1491, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/stream/TweetStream.js"], "sourcesContent": ["import { EventEmitter } from 'events';\nimport RequestHandlerHelper from '../client-mixins/request-handler.helper';\nimport { ETwitterStreamEvent } from '../types';\nimport TweetStreamEventCombiner from './TweetStreamEventCombiner';\nimport TweetStreamParser, { EStreamParserEvent } from './TweetStreamParser';\n// In seconds\nconst basicRetriesAttempt = [5, 15, 30, 60, 90, 120, 180, 300, 600, 900];\n// Default retry function\nconst basicReconnectRetry = tryOccurrence => tryOccurrence > basicRetriesAttempt.length\n    ? 901000\n    : basicRetriesAttempt[tryOccurrence - 1] * 1000;\nexport class TweetStream extends EventEmitter {\n    constructor(requestData, connection) {\n        super();\n        this.requestData = requestData;\n        this.autoReconnect = false;\n        this.autoReconnectRetries = 5;\n        // 2 minutes without any Twitter signal\n        this.keepAliveTimeoutMs = 1000 * 120;\n        this.nextRetryTimeout = basicReconnectRetry;\n        this.parser = new TweetStreamParser();\n        this.connectionProcessRunning = false;\n        this.onKeepAliveTimeout = this.onKeepAliveTimeout.bind(this);\n        this.initEventsFromParser();\n        if (connection) {\n            this.req = connection.req;\n            this.res = connection.res;\n            this.originalResponse = connection.originalResponse;\n            this.initEventsFromRequest();\n        }\n    }\n    on(event, handler) {\n        return super.on(event, handler);\n    }\n    initEventsFromRequest() {\n        if (!this.req || !this.res) {\n            throw new Error('TweetStream error: You cannot init TweetStream without a request and response object.');\n        }\n        const errorHandler = (err) => {\n            this.emit(ETwitterStreamEvent.ConnectionError, err);\n            this.emit(ETwitterStreamEvent.Error, {\n                type: ETwitterStreamEvent.ConnectionError,\n                error: err,\n                message: 'Connection lost or closed by Twitter.',\n            });\n            this.onConnectionError();\n        };\n        this.req.on('error', errorHandler);\n        this.res.on('error', errorHandler);\n        // Usually, connection should not be closed by Twitter!\n        this.res.on('close', () => errorHandler(new Error('Connection closed by Twitter.')));\n        this.res.on('data', (chunk) => {\n            this.resetKeepAliveTimeout();\n            if (chunk.toString() === '\\r\\n') {\n                return this.emit(ETwitterStreamEvent.DataKeepAlive);\n            }\n            this.parser.push(chunk.toString());\n        });\n        // Starts the keep alive timeout\n        this.resetKeepAliveTimeout();\n    }\n    initEventsFromParser() {\n        const payloadIsError = this.requestData.payloadIsError;\n        this.parser.on(EStreamParserEvent.ParsedData, (eventData) => {\n            if (payloadIsError && payloadIsError(eventData)) {\n                this.emit(ETwitterStreamEvent.DataError, eventData);\n                this.emit(ETwitterStreamEvent.Error, {\n                    type: ETwitterStreamEvent.DataError,\n                    error: eventData,\n                    message: 'Twitter sent a payload that is detected as an error payload.',\n                });\n            }\n            else {\n                this.emit(ETwitterStreamEvent.Data, eventData);\n            }\n        });\n        this.parser.on(EStreamParserEvent.ParseError, (error) => {\n            this.emit(ETwitterStreamEvent.TweetParseError, error);\n            this.emit(ETwitterStreamEvent.Error, {\n                type: ETwitterStreamEvent.TweetParseError,\n                error,\n                message: 'Failed to parse stream data.',\n            });\n        });\n    }\n    resetKeepAliveTimeout() {\n        this.unbindKeepAliveTimeout();\n        if (this.keepAliveTimeoutMs !== Infinity) {\n            this.keepAliveTimeout = setTimeout(this.onKeepAliveTimeout, this.keepAliveTimeoutMs);\n        }\n    }\n    onKeepAliveTimeout() {\n        this.emit(ETwitterStreamEvent.ConnectionLost);\n        this.onConnectionError();\n    }\n    unbindTimeouts() {\n        this.unbindRetryTimeout();\n        this.unbindKeepAliveTimeout();\n    }\n    unbindKeepAliveTimeout() {\n        if (this.keepAliveTimeout) {\n            clearTimeout(this.keepAliveTimeout);\n            this.keepAliveTimeout = undefined;\n        }\n    }\n    unbindRetryTimeout() {\n        if (this.retryTimeout) {\n            clearTimeout(this.retryTimeout);\n            this.retryTimeout = undefined;\n        }\n    }\n    closeWithoutEmit() {\n        this.unbindTimeouts();\n        if (this.res) {\n            this.res.removeAllListeners();\n            // Close response silently\n            this.res.destroy();\n        }\n        if (this.req) {\n            this.req.removeAllListeners();\n            // Close connection silently\n            this.req.destroy();\n        }\n    }\n    /** Terminate connection to Twitter. */\n    close() {\n        this.emit(ETwitterStreamEvent.ConnectionClosed);\n        this.closeWithoutEmit();\n    }\n    /** Unbind all listeners, and close connection. */\n    destroy() {\n        this.removeAllListeners();\n        this.close();\n    }\n    /**\n     * Make a new request that creates a new `TweetStream` instance with\n     * the same parameters, and bind current listeners to new stream.\n     */\n    async clone() {\n        const newRequest = new RequestHandlerHelper(this.requestData);\n        const newStream = await newRequest.makeRequestAsStream();\n        // Clone attached listeners\n        const listenerNames = this.eventNames();\n        for (const listener of listenerNames) {\n            const callbacks = this.listeners(listener);\n            for (const callback of callbacks) {\n                newStream.on(listener, callback);\n            }\n        }\n        return newStream;\n    }\n    /** Start initial stream connection, setup options on current instance and returns itself. */\n    async connect(options = {}) {\n        if (typeof options.autoReconnect !== 'undefined') {\n            this.autoReconnect = options.autoReconnect;\n        }\n        if (typeof options.autoReconnectRetries !== 'undefined') {\n            this.autoReconnectRetries = options.autoReconnectRetries === 'unlimited'\n                ? Infinity\n                : options.autoReconnectRetries;\n        }\n        if (typeof options.keepAliveTimeout !== 'undefined') {\n            this.keepAliveTimeoutMs = options.keepAliveTimeout === 'disable'\n                ? Infinity\n                : options.keepAliveTimeout;\n        }\n        if (typeof options.nextRetryTimeout !== 'undefined') {\n            this.nextRetryTimeout = options.nextRetryTimeout;\n        }\n        // Make the connection\n        this.unbindTimeouts();\n        try {\n            await this.reconnect();\n        }\n        catch (e) {\n            this.emit(ETwitterStreamEvent.ConnectError, 0);\n            this.emit(ETwitterStreamEvent.Error, {\n                type: ETwitterStreamEvent.ConnectError,\n                error: e,\n                message: 'Connect error - Initial connection just failed.',\n            });\n            // Only make a reconnection attempt if autoReconnect is true!\n            // Otherwise, let error be propagated\n            if (this.autoReconnect) {\n                this.makeAutoReconnectRetry(0, e);\n            }\n            else {\n                throw e;\n            }\n        }\n        return this;\n    }\n    /** Make a new request to (re)connect to Twitter. */\n    async reconnect() {\n        if (this.connectionProcessRunning) {\n            throw new Error('Connection process is already running.');\n        }\n        this.connectionProcessRunning = true;\n        try {\n            let initialConnection = true;\n            if (this.req) {\n                initialConnection = false;\n                this.closeWithoutEmit();\n            }\n            const { req, res, originalResponse } = await new RequestHandlerHelper(this.requestData).makeRequestAndResolveWhenReady();\n            this.req = req;\n            this.res = res;\n            this.originalResponse = originalResponse;\n            this.emit(initialConnection ? ETwitterStreamEvent.Connected : ETwitterStreamEvent.Reconnected);\n            this.parser.reset();\n            this.initEventsFromRequest();\n        }\n        finally {\n            this.connectionProcessRunning = false;\n        }\n    }\n    async onConnectionError(retryOccurrence = 0) {\n        this.unbindTimeouts();\n        // Close the request if necessary\n        this.closeWithoutEmit();\n        // Terminate stream by events if necessary (no auto-reconnect or retries exceeded)\n        if (!this.autoReconnect) {\n            this.emit(ETwitterStreamEvent.ConnectionClosed);\n            return;\n        }\n        if (retryOccurrence >= this.autoReconnectRetries) {\n            this.emit(ETwitterStreamEvent.ReconnectLimitExceeded);\n            this.emit(ETwitterStreamEvent.ConnectionClosed);\n            return;\n        }\n        // If all other conditions fails, do a reconnect attempt\n        try {\n            this.emit(ETwitterStreamEvent.ReconnectAttempt, retryOccurrence);\n            await this.reconnect();\n        }\n        catch (e) {\n            this.emit(ETwitterStreamEvent.ReconnectError, retryOccurrence);\n            this.emit(ETwitterStreamEvent.Error, {\n                type: ETwitterStreamEvent.ReconnectError,\n                error: e,\n                message: `Reconnect error - ${retryOccurrence + 1} attempts made yet.`,\n            });\n            this.makeAutoReconnectRetry(retryOccurrence, e);\n        }\n    }\n    makeAutoReconnectRetry(retryOccurrence, error) {\n        const nextRetry = this.nextRetryTimeout(retryOccurrence + 1, error);\n        this.retryTimeout = setTimeout(() => {\n            this.onConnectionError(retryOccurrence + 1);\n        }, nextRetry);\n    }\n    async *[Symbol.asyncIterator]() {\n        const eventCombiner = new TweetStreamEventCombiner(this);\n        try {\n            while (true) {\n                if (!this.req || this.req.aborted) {\n                    throw new Error('Connection closed');\n                }\n                if (eventCombiner.hasStack()) {\n                    yield* eventCombiner.popStack();\n                }\n                const { type, payload } = await eventCombiner.nextEvent();\n                if (type === 'error') {\n                    throw payload;\n                }\n            }\n        }\n        finally {\n            eventCombiner.destroy();\n        }\n    }\n}\nexport default TweetStream;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;AACA;;;;;;AACA,aAAa;AACb,MAAM,sBAAsB;IAAC;IAAG;IAAI;IAAI;IAAI;IAAI;IAAK;IAAK;IAAK;IAAK;CAAI;AACxE,yBAAyB;AACzB,MAAM,sBAAsB,CAAA,gBAAiB,gBAAgB,oBAAoB,MAAM,GACjF,SACA,mBAAmB,CAAC,gBAAgB,EAAE,GAAG;AACxC,MAAM,oBAAoB,qGAAA,CAAA,eAAY;IACzC,YAAY,WAAW,EAAE,UAAU,CAAE;QACjC,KAAK;QACL,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,oBAAoB,GAAG;QAC5B,uCAAuC;QACvC,IAAI,CAAC,kBAAkB,GAAG,OAAO;QACjC,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,MAAM,GAAG,IAAI,oLAAA,CAAA,UAAiB;QACnC,IAAI,CAAC,wBAAwB,GAAG;QAChC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI;QAC3D,IAAI,CAAC,oBAAoB;QACzB,IAAI,YAAY;YACZ,IAAI,CAAC,GAAG,GAAG,WAAW,GAAG;YACzB,IAAI,CAAC,GAAG,GAAG,WAAW,GAAG;YACzB,IAAI,CAAC,gBAAgB,GAAG,WAAW,gBAAgB;YACnD,IAAI,CAAC,qBAAqB;QAC9B;IACJ;IACA,GAAG,KAAK,EAAE,OAAO,EAAE;QACf,OAAO,KAAK,CAAC,GAAG,OAAO;IAC3B;IACA,wBAAwB;QACpB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACxB,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,eAAe,CAAC;YAClB,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,sBAAmB,CAAC,eAAe,EAAE;YAC/C,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,sBAAmB,CAAC,KAAK,EAAE;gBACjC,MAAM,iLAAA,CAAA,sBAAmB,CAAC,eAAe;gBACzC,OAAO;gBACP,SAAS;YACb;YACA,IAAI,CAAC,iBAAiB;QAC1B;QACA,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS;QACrB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS;QACrB,uDAAuD;QACvD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,IAAM,aAAa,IAAI,MAAM;QAClD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC;YACjB,IAAI,CAAC,qBAAqB;YAC1B,IAAI,MAAM,QAAQ,OAAO,QAAQ;gBAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,sBAAmB,CAAC,aAAa;YACtD;YACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,QAAQ;QACnC;QACA,gCAAgC;QAChC,IAAI,CAAC,qBAAqB;IAC9B;IACA,uBAAuB;QACnB,MAAM,iBAAiB,IAAI,CAAC,WAAW,CAAC,cAAc;QACtD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,oLAAA,CAAA,qBAAkB,CAAC,UAAU,EAAE,CAAC;YAC3C,IAAI,kBAAkB,eAAe,YAAY;gBAC7C,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,sBAAmB,CAAC,SAAS,EAAE;gBACzC,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,sBAAmB,CAAC,KAAK,EAAE;oBACjC,MAAM,iLAAA,CAAA,sBAAmB,CAAC,SAAS;oBACnC,OAAO;oBACP,SAAS;gBACb;YACJ,OACK;gBACD,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,sBAAmB,CAAC,IAAI,EAAE;YACxC;QACJ;QACA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,oLAAA,CAAA,qBAAkB,CAAC,UAAU,EAAE,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,sBAAmB,CAAC,eAAe,EAAE;YAC/C,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,sBAAmB,CAAC,KAAK,EAAE;gBACjC,MAAM,iLAAA,CAAA,sBAAmB,CAAC,eAAe;gBACzC;gBACA,SAAS;YACb;QACJ;IACJ;IACA,wBAAwB;QACpB,IAAI,CAAC,sBAAsB;QAC3B,IAAI,IAAI,CAAC,kBAAkB,KAAK,UAAU;YACtC,IAAI,CAAC,gBAAgB,GAAG,WAAW,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;QACvF;IACJ;IACA,qBAAqB;QACjB,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,sBAAmB,CAAC,cAAc;QAC5C,IAAI,CAAC,iBAAiB;IAC1B;IACA,iBAAiB;QACb,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,sBAAsB;IAC/B;IACA,yBAAyB;QACrB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,aAAa,IAAI,CAAC,gBAAgB;YAClC,IAAI,CAAC,gBAAgB,GAAG;QAC5B;IACJ;IACA,qBAAqB;QACjB,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,aAAa,IAAI,CAAC,YAAY;YAC9B,IAAI,CAAC,YAAY,GAAG;QACxB;IACJ;IACA,mBAAmB;QACf,IAAI,CAAC,cAAc;QACnB,IAAI,IAAI,CAAC,GAAG,EAAE;YACV,IAAI,CAAC,GAAG,CAAC,kBAAkB;YAC3B,0BAA0B;YAC1B,IAAI,CAAC,GAAG,CAAC,OAAO;QACpB;QACA,IAAI,IAAI,CAAC,GAAG,EAAE;YACV,IAAI,CAAC,GAAG,CAAC,kBAAkB;YAC3B,4BAA4B;YAC5B,IAAI,CAAC,GAAG,CAAC,OAAO;QACpB;IACJ;IACA,qCAAqC,GACrC,QAAQ;QACJ,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,sBAAmB,CAAC,gBAAgB;QAC9C,IAAI,CAAC,gBAAgB;IACzB;IACA,gDAAgD,GAChD,UAAU;QACN,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,KAAK;IACd;IACA;;;KAGC,GACD,MAAM,QAAQ;QACV,MAAM,aAAa,IAAI,yMAAA,CAAA,UAAoB,CAAC,IAAI,CAAC,WAAW;QAC5D,MAAM,YAAY,MAAM,WAAW,mBAAmB;QACtD,2BAA2B;QAC3B,MAAM,gBAAgB,IAAI,CAAC,UAAU;QACrC,KAAK,MAAM,YAAY,cAAe;YAClC,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC;YACjC,KAAK,MAAM,YAAY,UAAW;gBAC9B,UAAU,EAAE,CAAC,UAAU;YAC3B;QACJ;QACA,OAAO;IACX;IACA,2FAA2F,GAC3F,MAAM,QAAQ,UAAU,CAAC,CAAC,EAAE;QACxB,IAAI,OAAO,QAAQ,aAAa,KAAK,aAAa;YAC9C,IAAI,CAAC,aAAa,GAAG,QAAQ,aAAa;QAC9C;QACA,IAAI,OAAO,QAAQ,oBAAoB,KAAK,aAAa;YACrD,IAAI,CAAC,oBAAoB,GAAG,QAAQ,oBAAoB,KAAK,cACvD,WACA,QAAQ,oBAAoB;QACtC;QACA,IAAI,OAAO,QAAQ,gBAAgB,KAAK,aAAa;YACjD,IAAI,CAAC,kBAAkB,GAAG,QAAQ,gBAAgB,KAAK,YACjD,WACA,QAAQ,gBAAgB;QAClC;QACA,IAAI,OAAO,QAAQ,gBAAgB,KAAK,aAAa;YACjD,IAAI,CAAC,gBAAgB,GAAG,QAAQ,gBAAgB;QACpD;QACA,sBAAsB;QACtB,IAAI,CAAC,cAAc;QACnB,IAAI;YACA,MAAM,IAAI,CAAC,SAAS;QACxB,EACA,OAAO,GAAG;YACN,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,sBAAmB,CAAC,YAAY,EAAE;YAC5C,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,sBAAmB,CAAC,KAAK,EAAE;gBACjC,MAAM,iLAAA,CAAA,sBAAmB,CAAC,YAAY;gBACtC,OAAO;gBACP,SAAS;YACb;YACA,6DAA6D;YAC7D,qCAAqC;YACrC,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,sBAAsB,CAAC,GAAG;YACnC,OACK;gBACD,MAAM;YACV;QACJ;QACA,OAAO,IAAI;IACf;IACA,kDAAkD,GAClD,MAAM,YAAY;QACd,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,wBAAwB,GAAG;QAChC,IAAI;YACA,IAAI,oBAAoB;YACxB,IAAI,IAAI,CAAC,GAAG,EAAE;gBACV,oBAAoB;gBACpB,IAAI,CAAC,gBAAgB;YACzB;YACA,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,gBAAgB,EAAE,GAAG,MAAM,IAAI,yMAAA,CAAA,UAAoB,CAAC,IAAI,CAAC,WAAW,EAAE,8BAA8B;YACtH,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,IAAI,CAAC,oBAAoB,iLAAA,CAAA,sBAAmB,CAAC,SAAS,GAAG,iLAAA,CAAA,sBAAmB,CAAC,WAAW;YAC7F,IAAI,CAAC,MAAM,CAAC,KAAK;YACjB,IAAI,CAAC,qBAAqB;QAC9B,SACQ;YACJ,IAAI,CAAC,wBAAwB,GAAG;QACpC;IACJ;IACA,MAAM,kBAAkB,kBAAkB,CAAC,EAAE;QACzC,IAAI,CAAC,cAAc;QACnB,iCAAiC;QACjC,IAAI,CAAC,gBAAgB;QACrB,kFAAkF;QAClF,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,sBAAmB,CAAC,gBAAgB;YAC9C;QACJ;QACA,IAAI,mBAAmB,IAAI,CAAC,oBAAoB,EAAE;YAC9C,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,sBAAmB,CAAC,sBAAsB;YACpD,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,sBAAmB,CAAC,gBAAgB;YAC9C;QACJ;QACA,wDAAwD;QACxD,IAAI;YACA,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,sBAAmB,CAAC,gBAAgB,EAAE;YAChD,MAAM,IAAI,CAAC,SAAS;QACxB,EACA,OAAO,GAAG;YACN,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,sBAAmB,CAAC,cAAc,EAAE;YAC9C,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,sBAAmB,CAAC,KAAK,EAAE;gBACjC,MAAM,iLAAA,CAAA,sBAAmB,CAAC,cAAc;gBACxC,OAAO;gBACP,SAAS,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,mBAAmB,CAAC;YAC1E;YACA,IAAI,CAAC,sBAAsB,CAAC,iBAAiB;QACjD;IACJ;IACA,uBAAuB,eAAe,EAAE,KAAK,EAAE;QAC3C,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,GAAG;QAC7D,IAAI,CAAC,YAAY,GAAG,WAAW;YAC3B,IAAI,CAAC,iBAAiB,CAAC,kBAAkB;QAC7C,GAAG;IACP;IACA,OAAO,CAAC,OAAO,aAAa,CAAC,GAAG;QAC5B,MAAM,gBAAgB,IAAI,2LAAA,CAAA,UAAwB,CAAC,IAAI;QACvD,IAAI;YACA,MAAO,KAAM;gBACT,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;oBAC/B,MAAM,IAAI,MAAM;gBACpB;gBACA,IAAI,cAAc,QAAQ,IAAI;oBAC1B,OAAO,cAAc,QAAQ;gBACjC;gBACA,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,MAAM,cAAc,SAAS;gBACvD,IAAI,SAAS,SAAS;oBAClB,MAAM;gBACV;YACJ;QACJ,SACQ;YACJ,cAAc,OAAO;QACzB;IACJ;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1774, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/plugins/helpers.js"], "sourcesContent": ["import { ApiPartialResponseError, ApiRequestError, ApiResponseError, TwitterApiPluginResponseOverride } from '../types';\n/* Plugin helpers */\nexport function hasRequestErrorPlugins(client) {\n    var _a;\n    if (!((_a = client.clientSettings.plugins) === null || _a === void 0 ? void 0 : _a.length)) {\n        return false;\n    }\n    for (const plugin of client.clientSettings.plugins) {\n        if (plugin.onRequestError || plugin.onResponseError) {\n            return true;\n        }\n    }\n    return false;\n}\nexport async function applyResponseHooks(requestParams, computedParams, requestOptions, error) {\n    let override;\n    if (error instanceof ApiRequestError || error instanceof ApiPartialResponseError) {\n        override = await this.applyPluginMethod('onRequestError', {\n            client: this,\n            url: this.getUrlObjectFromUrlString(requestParams.url),\n            params: requestParams,\n            computedParams,\n            requestOptions,\n            error,\n        });\n    }\n    else if (error instanceof ApiResponseError) {\n        override = await this.applyPluginMethod('onResponseError', {\n            client: this,\n            url: this.getUrlObjectFromUrlString(requestParams.url),\n            params: requestParams,\n            computedParams,\n            requestOptions,\n            error,\n        });\n    }\n    if (override && override instanceof TwitterApiPluginResponseOverride) {\n        return override.value;\n    }\n    return Promise.reject(error);\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;;AAEO,SAAS,uBAAuB,MAAM;IACzC,IAAI;IACJ,IAAI,CAAC,CAAC,CAAC,KAAK,OAAO,cAAc,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG;QACxF,OAAO;IACX;IACA,KAAK,MAAM,UAAU,OAAO,cAAc,CAAC,OAAO,CAAE;QAChD,IAAI,OAAO,cAAc,IAAI,OAAO,eAAe,EAAE;YACjD,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACO,eAAe,mBAAmB,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,KAAK;IACzF,IAAI;IACJ,IAAI,iBAAiB,iLAAA,CAAA,kBAAe,IAAI,iBAAiB,iLAAA,CAAA,0BAAuB,EAAE;QAC9E,WAAW,MAAM,IAAI,CAAC,iBAAiB,CAAC,kBAAkB;YACtD,QAAQ,IAAI;YACZ,KAAK,IAAI,CAAC,yBAAyB,CAAC,cAAc,GAAG;YACrD,QAAQ;YACR;YACA;YACA;QACJ;IACJ,OACK,IAAI,iBAAiB,iLAAA,CAAA,mBAAgB,EAAE;QACxC,WAAW,MAAM,IAAI,CAAC,iBAAiB,CAAC,mBAAmB;YACvD,QAAQ,IAAI;YACZ,KAAK,IAAI,CAAC,yBAAyB,CAAC,cAAc,GAAG;YACrD,QAAQ;YACR;YACA;YACA;QACJ;IACJ;IACA,IAAI,YAAY,oBAAoB,uMAAA,CAAA,mCAAgC,EAAE;QAClE,OAAO,SAAS,KAAK;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1826, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/client-mixins/oauth1.helper.js"], "sourcesContent": ["import * as crypto from 'crypto';\nexport class OAuth1Helper {\n    constructor(options) {\n        this.nonceLength = 32;\n        this.consumerKeys = options.consumerKeys;\n    }\n    static percentEncode(str) {\n        return encodeURIComponent(str)\n            .replace(/!/g, '%21')\n            .replace(/\\*/g, '%2A')\n            .replace(/'/g, '%27')\n            .replace(/\\(/g, '%28')\n            .replace(/\\)/g, '%29');\n    }\n    hash(base, key) {\n        return crypto\n            .createHmac('sha1', key)\n            .update(base)\n            .digest('base64');\n    }\n    authorize(request, accessTokens = {}) {\n        const oauthInfo = {\n            oauth_consumer_key: this.consumerKeys.key,\n            oauth_nonce: this.getNonce(),\n            oauth_signature_method: 'HMAC-SHA1',\n            oauth_timestamp: this.getTimestamp(),\n            oauth_version: '1.0',\n        };\n        if (accessTokens.key !== undefined) {\n            oauthInfo.oauth_token = accessTokens.key;\n        }\n        if (!request.data) {\n            request.data = {};\n        }\n        oauthInfo.oauth_signature = this.getSignature(request, accessTokens.secret, oauthInfo);\n        return oauthInfo;\n    }\n    toHeader(oauthInfo) {\n        const sorted = sortObject(oauthInfo);\n        let header_value = 'OAuth ';\n        for (const element of sorted) {\n            if (element.key.indexOf('oauth_') !== 0) {\n                continue;\n            }\n            header_value += OAuth1Helper.percentEncode(element.key) + '=\"' + OAuth1Helper.percentEncode(element.value) + '\",';\n        }\n        return {\n            // Remove the last ,\n            Authorization: header_value.slice(0, header_value.length - 1),\n        };\n    }\n    getNonce() {\n        const wordCharacters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n        let result = '';\n        for (let i = 0; i < this.nonceLength; i++) {\n            result += wordCharacters[Math.trunc(Math.random() * wordCharacters.length)];\n        }\n        return result;\n    }\n    getTimestamp() {\n        return Math.trunc(new Date().getTime() / 1000);\n    }\n    getSignature(request, tokenSecret, oauthInfo) {\n        return this.hash(this.getBaseString(request, oauthInfo), this.getSigningKey(tokenSecret));\n    }\n    getSigningKey(tokenSecret) {\n        return OAuth1Helper.percentEncode(this.consumerKeys.secret) + '&' + OAuth1Helper.percentEncode(tokenSecret || '');\n    }\n    getBaseString(request, oauthInfo) {\n        return request.method.toUpperCase() + '&'\n            + OAuth1Helper.percentEncode(this.getBaseUrl(request.url)) + '&'\n            + OAuth1Helper.percentEncode(this.getParameterString(request, oauthInfo));\n    }\n    getParameterString(request, oauthInfo) {\n        const baseStringData = sortObject(percentEncodeData(mergeObject(oauthInfo, mergeObject(request.data, deParamUrl(request.url)))));\n        let dataStr = '';\n        for (const { key, value } of baseStringData) {\n            // check if the value is an array\n            // this means that this key has multiple values\n            if (value && Array.isArray(value)) {\n                // sort the array first\n                value.sort();\n                let valString = '';\n                // serialize all values for this key: e.g. formkey=formvalue1&formkey=formvalue2\n                value.forEach((item, i) => {\n                    valString += key + '=' + item;\n                    if (i < value.length) {\n                        valString += '&';\n                    }\n                });\n                dataStr += valString;\n            }\n            else {\n                dataStr += key + '=' + value + '&';\n            }\n        }\n        // Remove the last character\n        return dataStr.slice(0, dataStr.length - 1);\n    }\n    getBaseUrl(url) {\n        return url.split('?')[0];\n    }\n}\nexport default OAuth1Helper;\n// Helper functions //\nfunction mergeObject(obj1, obj2) {\n    return {\n        ...obj1 || {},\n        ...obj2 || {},\n    };\n}\nfunction sortObject(data) {\n    return Object.keys(data)\n        .sort()\n        .map(key => ({ key, value: data[key] }));\n}\nfunction deParam(string) {\n    const split = string.split('&');\n    const data = {};\n    for (const coupleKeyValue of split) {\n        const [key, value = ''] = coupleKeyValue.split('=');\n        // check if the key already exists\n        // this can occur if the QS part of the url contains duplicate keys like this: ?formkey=formvalue1&formkey=formvalue2\n        if (data[key]) {\n            // the key exists already\n            if (!Array.isArray(data[key])) {\n                // replace the value with an array containing the already present value\n                data[key] = [data[key]];\n            }\n            // and add the new found value to it\n            data[key].push(decodeURIComponent(value));\n        }\n        else {\n            // it doesn't exist, just put the found value in the data object\n            data[key] = decodeURIComponent(value);\n        }\n    }\n    return data;\n}\nfunction deParamUrl(url) {\n    const tmp = url.split('?');\n    if (tmp.length === 1)\n        return {};\n    return deParam(tmp[1]);\n}\nfunction percentEncodeData(data) {\n    const result = {};\n    for (const key in data) {\n        let value = data[key];\n        // check if the value is an array\n        if (value && Array.isArray(value)) {\n            value = value.map(v => OAuth1Helper.percentEncode(v));\n        }\n        else {\n            value = OAuth1Helper.percentEncode(value);\n        }\n        result[OAuth1Helper.percentEncode(key)] = value;\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM;IACT,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,GAAG,QAAQ,YAAY;IAC5C;IACA,OAAO,cAAc,GAAG,EAAE;QACtB,OAAO,mBAAmB,KACrB,OAAO,CAAC,MAAM,OACd,OAAO,CAAC,OAAO,OACf,OAAO,CAAC,MAAM,OACd,OAAO,CAAC,OAAO,OACf,OAAO,CAAC,OAAO;IACxB;IACA,KAAK,IAAI,EAAE,GAAG,EAAE;QACZ,OAAO,CAAA,GAAA,qGAAA,CAAA,aACQ,AAAD,EAAE,QAAQ,KACnB,MAAM,CAAC,MACP,MAAM,CAAC;IAChB;IACA,UAAU,OAAO,EAAE,eAAe,CAAC,CAAC,EAAE;QAClC,MAAM,YAAY;YACd,oBAAoB,IAAI,CAAC,YAAY,CAAC,GAAG;YACzC,aAAa,IAAI,CAAC,QAAQ;YAC1B,wBAAwB;YACxB,iBAAiB,IAAI,CAAC,YAAY;YAClC,eAAe;QACnB;QACA,IAAI,aAAa,GAAG,KAAK,WAAW;YAChC,UAAU,WAAW,GAAG,aAAa,GAAG;QAC5C;QACA,IAAI,CAAC,QAAQ,IAAI,EAAE;YACf,QAAQ,IAAI,GAAG,CAAC;QACpB;QACA,UAAU,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,aAAa,MAAM,EAAE;QAC5E,OAAO;IACX;IACA,SAAS,SAAS,EAAE;QAChB,MAAM,SAAS,WAAW;QAC1B,IAAI,eAAe;QACnB,KAAK,MAAM,WAAW,OAAQ;YAC1B,IAAI,QAAQ,GAAG,CAAC,OAAO,CAAC,cAAc,GAAG;gBACrC;YACJ;YACA,gBAAgB,aAAa,aAAa,CAAC,QAAQ,GAAG,IAAI,OAAO,aAAa,aAAa,CAAC,QAAQ,KAAK,IAAI;QACjH;QACA,OAAO;YACH,oBAAoB;YACpB,eAAe,aAAa,KAAK,CAAC,GAAG,aAAa,MAAM,GAAG;QAC/D;IACJ;IACA,WAAW;QACP,MAAM,iBAAiB;QACvB,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,EAAE,IAAK;YACvC,UAAU,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,eAAe,MAAM,EAAE;QAC/E;QACA,OAAO;IACX;IACA,eAAe;QACX,OAAO,KAAK,KAAK,CAAC,IAAI,OAAO,OAAO,KAAK;IAC7C;IACA,aAAa,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE;QAC1C,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,YAAY,IAAI,CAAC,aAAa,CAAC;IAChF;IACA,cAAc,WAAW,EAAE;QACvB,OAAO,aAAa,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,MAAM,aAAa,aAAa,CAAC,eAAe;IAClH;IACA,cAAc,OAAO,EAAE,SAAS,EAAE;QAC9B,OAAO,QAAQ,MAAM,CAAC,WAAW,KAAK,MAChC,aAAa,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK,MAC3D,aAAa,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,SAAS;IACtE;IACA,mBAAmB,OAAO,EAAE,SAAS,EAAE;QACnC,MAAM,iBAAiB,WAAW,kBAAkB,YAAY,WAAW,YAAY,QAAQ,IAAI,EAAE,WAAW,QAAQ,GAAG;QAC3H,IAAI,UAAU;QACd,KAAK,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,eAAgB;YACzC,iCAAiC;YACjC,+CAA+C;YAC/C,IAAI,SAAS,MAAM,OAAO,CAAC,QAAQ;gBAC/B,uBAAuB;gBACvB,MAAM,IAAI;gBACV,IAAI,YAAY;gBAChB,gFAAgF;gBAChF,MAAM,OAAO,CAAC,CAAC,MAAM;oBACjB,aAAa,MAAM,MAAM;oBACzB,IAAI,IAAI,MAAM,MAAM,EAAE;wBAClB,aAAa;oBACjB;gBACJ;gBACA,WAAW;YACf,OACK;gBACD,WAAW,MAAM,MAAM,QAAQ;YACnC;QACJ;QACA,4BAA4B;QAC5B,OAAO,QAAQ,KAAK,CAAC,GAAG,QAAQ,MAAM,GAAG;IAC7C;IACA,WAAW,GAAG,EAAE;QACZ,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;IAC5B;AACJ;uCACe;AACf,sBAAsB;AACtB,SAAS,YAAY,IAAI,EAAE,IAAI;IAC3B,OAAO;QACH,GAAG,QAAQ,CAAC,CAAC;QACb,GAAG,QAAQ,CAAC,CAAC;IACjB;AACJ;AACA,SAAS,WAAW,IAAI;IACpB,OAAO,OAAO,IAAI,CAAC,MACd,IAAI,GACJ,GAAG,CAAC,CAAA,MAAO,CAAC;YAAE;YAAK,OAAO,IAAI,CAAC,IAAI;QAAC,CAAC;AAC9C;AACA,SAAS,QAAQ,MAAM;IACnB,MAAM,QAAQ,OAAO,KAAK,CAAC;IAC3B,MAAM,OAAO,CAAC;IACd,KAAK,MAAM,kBAAkB,MAAO;QAChC,MAAM,CAAC,KAAK,QAAQ,EAAE,CAAC,GAAG,eAAe,KAAK,CAAC;QAC/C,kCAAkC;QAClC,qHAAqH;QACrH,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,yBAAyB;YACzB,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG;gBAC3B,uEAAuE;gBACvE,IAAI,CAAC,IAAI,GAAG;oBAAC,IAAI,CAAC,IAAI;iBAAC;YAC3B;YACA,oCAAoC;YACpC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB;QACtC,OACK;YACD,gEAAgE;YAChE,IAAI,CAAC,IAAI,GAAG,mBAAmB;QACnC;IACJ;IACA,OAAO;AACX;AACA,SAAS,WAAW,GAAG;IACnB,MAAM,MAAM,IAAI,KAAK,CAAC;IACtB,IAAI,IAAI,MAAM,KAAK,GACf,OAAO,CAAC;IACZ,OAAO,QAAQ,GAAG,CAAC,EAAE;AACzB;AACA,SAAS,kBAAkB,IAAI;IAC3B,MAAM,SAAS,CAAC;IAChB,IAAK,MAAM,OAAO,KAAM;QACpB,IAAI,QAAQ,IAAI,CAAC,IAAI;QACrB,iCAAiC;QACjC,IAAI,SAAS,MAAM,OAAO,CAAC,QAAQ;YAC/B,QAAQ,MAAM,GAAG,CAAC,CAAA,IAAK,aAAa,aAAa,CAAC;QACtD,OACK;YACD,QAAQ,aAAa,aAAa,CAAC;QACvC;QACA,MAAM,CAAC,aAAa,aAAa,CAAC,KAAK,GAAG;IAC9C;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1986, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/client-mixins/form-data.helper.js"], "sourcesContent": ["import { arrayWrap } from '../helpers';\n// This class is partially inspired by https://github.com/form-data/form-data/blob/master/lib/form_data.js\n// All credits to their authors.\nexport class FormDataHelper {\n    constructor() {\n        this._boundary = '';\n        this._chunks = [];\n    }\n    bodyAppend(...values) {\n        const allAsBuffer = values.map(val => val instanceof Buffer ? val : Buffer.from(val));\n        this._chunks.push(...allAsBuffer);\n    }\n    append(field, value, contentType) {\n        const convertedValue = value instanceof Buffer ? value : value.toString();\n        const header = this.getMultipartHeader(field, convertedValue, contentType);\n        this.bodyAppend(header, convertedValue, FormDataHelper.LINE_BREAK);\n    }\n    getHeaders() {\n        return {\n            'content-type': 'multipart/form-data; boundary=' + this.getBoundary(),\n        };\n    }\n    /** Length of form-data (including footer length). */\n    getLength() {\n        return this._chunks.reduce((acc, cur) => acc + cur.length, this.getMultipartFooter().length);\n    }\n    getBuffer() {\n        const allChunks = [...this._chunks, this.getMultipartFooter()];\n        const totalBuffer = Buffer.alloc(this.getLength());\n        let i = 0;\n        for (const chunk of allChunks) {\n            for (let j = 0; j < chunk.length; i++, j++) {\n                totalBuffer[i] = chunk[j];\n            }\n        }\n        return totalBuffer;\n    }\n    getBoundary() {\n        if (!this._boundary) {\n            this.generateBoundary();\n        }\n        return this._boundary;\n    }\n    generateBoundary() {\n        // This generates a 50 character boundary similar to those used by Firefox.\n        let boundary = '--------------------------';\n        for (let i = 0; i < 24; i++) {\n            boundary += Math.floor(Math.random() * 10).toString(16);\n        }\n        this._boundary = boundary;\n    }\n    getMultipartHeader(field, value, contentType) {\n        // In this lib no need to guess more the content type, octet stream is ok of buffers\n        if (!contentType) {\n            contentType = value instanceof Buffer ? FormDataHelper.DEFAULT_CONTENT_TYPE : '';\n        }\n        const headers = {\n            'Content-Disposition': ['form-data', `name=\"${field}\"`],\n            'Content-Type': contentType,\n        };\n        let contents = '';\n        for (const [prop, header] of Object.entries(headers)) {\n            // skip nullish headers.\n            if (!header.length) {\n                continue;\n            }\n            contents += prop + ': ' + arrayWrap(header).join('; ') + FormDataHelper.LINE_BREAK;\n        }\n        return '--' + this.getBoundary() + FormDataHelper.LINE_BREAK + contents + FormDataHelper.LINE_BREAK;\n    }\n    getMultipartFooter() {\n        if (this._footerChunk) {\n            return this._footerChunk;\n        }\n        return this._footerChunk = Buffer.from('--' + this.getBoundary() + '--' + FormDataHelper.LINE_BREAK);\n    }\n}\nFormDataHelper.LINE_BREAK = '\\r\\n';\nFormDataHelper.DEFAULT_CONTENT_TYPE = 'application/octet-stream';\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM;IACT,aAAc;QACV,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG,EAAE;IACrB;IACA,WAAW,GAAG,MAAM,EAAE;QAClB,MAAM,cAAc,OAAO,GAAG,CAAC,CAAA,MAAO,eAAe,SAAS,MAAM,OAAO,IAAI,CAAC;QAChF,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI;IACzB;IACA,OAAO,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE;QAC9B,MAAM,iBAAiB,iBAAiB,SAAS,QAAQ,MAAM,QAAQ;QACvE,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC,OAAO,gBAAgB;QAC9D,IAAI,CAAC,UAAU,CAAC,QAAQ,gBAAgB,eAAe,UAAU;IACrE;IACA,aAAa;QACT,OAAO;YACH,gBAAgB,mCAAmC,IAAI,CAAC,WAAW;QACvE;IACJ;IACA,mDAAmD,GACnD,YAAY;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,MAAM,EAAE,IAAI,CAAC,kBAAkB,GAAG,MAAM;IAC/F;IACA,YAAY;QACR,MAAM,YAAY;eAAI,IAAI,CAAC,OAAO;YAAE,IAAI,CAAC,kBAAkB;SAAG;QAC9D,MAAM,cAAc,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS;QAC/C,IAAI,IAAI;QACR,KAAK,MAAM,SAAS,UAAW;YAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,IAAK;gBACxC,WAAW,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;YAC7B;QACJ;QACA,OAAO;IACX;IACA,cAAc;QACV,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,gBAAgB;QACzB;QACA,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,mBAAmB;QACf,2EAA2E;QAC3E,IAAI,WAAW;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;YACzB,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,IAAI,QAAQ,CAAC;QACxD;QACA,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,mBAAmB,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE;QAC1C,oFAAoF;QACpF,IAAI,CAAC,aAAa;YACd,cAAc,iBAAiB,SAAS,eAAe,oBAAoB,GAAG;QAClF;QACA,MAAM,UAAU;YACZ,uBAAuB;gBAAC;gBAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;aAAC;YACvD,gBAAgB;QACpB;QACA,IAAI,WAAW;QACf,KAAK,MAAM,CAAC,MAAM,OAAO,IAAI,OAAO,OAAO,CAAC,SAAU;YAClD,wBAAwB;YACxB,IAAI,CAAC,OAAO,MAAM,EAAE;gBAChB;YACJ;YACA,YAAY,OAAO,OAAO,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,IAAI,CAAC,QAAQ,eAAe,UAAU;QACtF;QACA,OAAO,OAAO,IAAI,CAAC,WAAW,KAAK,eAAe,UAAU,GAAG,WAAW,eAAe,UAAU;IACvG;IACA,qBAAqB;QACjB,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,OAAO,IAAI,CAAC,YAAY;QAC5B;QACA,OAAO,IAAI,CAAC,YAAY,GAAG,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,WAAW,KAAK,OAAO,eAAe,UAAU;IACvG;AACJ;AACA,eAAe,UAAU,GAAG;AAC5B,eAAe,oBAAoB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2078, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/client-mixins/request-param.helper.js"], "sourcesContent": ["import { FormDataHelper } from './form-data.helper';\nimport OAuth1Helper from './oauth1.helper';\n/* Helpers functions that are specific to this class but do not depends on instance */\nexport class RequestParamHelpers {\n    static formatQueryToString(query) {\n        const formattedQuery = {};\n        for (const prop in query) {\n            if (typeof query[prop] === 'string') {\n                formattedQuery[prop] = query[prop];\n            }\n            else if (typeof query[prop] !== 'undefined') {\n                formattedQuery[prop] = String(query[prop]);\n            }\n        }\n        return formattedQuery;\n    }\n    static autoDetectBodyType(url) {\n        if (url.pathname.startsWith('/2/') || url.pathname.startsWith('/labs/2/')) {\n            // oauth2 takes url encoded\n            if (url.password.startsWith('/2/oauth2')) {\n                return 'url';\n            }\n            // Twitter API v2 has JSON-encoded requests for everything else\n            return 'json';\n        }\n        if (url.hostname === 'upload.twitter.com') {\n            if (url.pathname === '/1.1/media/upload.json') {\n                return 'form-data';\n            }\n            // json except for media/upload command, that is form-data.\n            return 'json';\n        }\n        const endpoint = url.pathname.split('/1.1/', 2)[1];\n        if (this.JSON_1_1_ENDPOINTS.has(endpoint)) {\n            return 'json';\n        }\n        return 'url';\n    }\n    static addQueryParamsToUrl(url, query) {\n        const queryEntries = Object.entries(query);\n        if (queryEntries.length) {\n            let search = '';\n            for (const [key, value] of queryEntries) {\n                search += (search.length ? '&' : '?') + `${OAuth1Helper.percentEncode(key)}=${OAuth1Helper.percentEncode(value)}`;\n            }\n            url.search = search;\n        }\n    }\n    static constructBodyParams(body, headers, mode) {\n        if (body instanceof Buffer) {\n            return body;\n        }\n        if (mode === 'json') {\n            if (!headers['content-type']) {\n                headers['content-type'] = 'application/json;charset=UTF-8';\n            }\n            return JSON.stringify(body);\n        }\n        else if (mode === 'url') {\n            if (!headers['content-type']) {\n                headers['content-type'] = 'application/x-www-form-urlencoded;charset=UTF-8';\n            }\n            if (Object.keys(body).length) {\n                return new URLSearchParams(body)\n                    .toString()\n                    .replace(/\\*/g, '%2A'); // URLSearchParams doesnt encode '*', but Twitter wants it encoded.\n            }\n            return '';\n        }\n        else if (mode === 'raw') {\n            throw new Error('You can only use raw body mode with Buffers. To give a string, use Buffer.from(str).');\n        }\n        else {\n            const form = new FormDataHelper();\n            for (const parameter in body) {\n                form.append(parameter, body[parameter]);\n            }\n            if (!headers['content-type']) {\n                const formHeaders = form.getHeaders();\n                headers['content-type'] = formHeaders['content-type'];\n            }\n            return form.getBuffer();\n        }\n    }\n    static setBodyLengthHeader(options, body) {\n        var _a;\n        options.headers = (_a = options.headers) !== null && _a !== void 0 ? _a : {};\n        if (typeof body === 'string') {\n            options.headers['content-length'] = Buffer.byteLength(body);\n        }\n        else {\n            options.headers['content-length'] = body.length;\n        }\n    }\n    static isOAuthSerializable(item) {\n        return !(item instanceof Buffer);\n    }\n    static mergeQueryAndBodyForOAuth(query, body) {\n        const parameters = {};\n        for (const prop in query) {\n            parameters[prop] = query[prop];\n        }\n        if (this.isOAuthSerializable(body)) {\n            for (const prop in body) {\n                const bodyProp = body[prop];\n                if (this.isOAuthSerializable(bodyProp)) {\n                    parameters[prop] = typeof bodyProp === 'object' && bodyProp !== null && 'toString' in bodyProp\n                        ? bodyProp.toString()\n                        : bodyProp;\n                }\n            }\n        }\n        return parameters;\n    }\n    static moveUrlQueryParamsIntoObject(url, query) {\n        for (const [param, value] of url.searchParams) {\n            query[param] = value;\n        }\n        // Remove the query string\n        url.search = '';\n        return url;\n    }\n    /**\n     * Replace URL parameters available in pathname, like `:id`, with data given in `parameters`:\n     * `https://twitter.com/:id.json` + `{ id: '20' }` => `https://twitter.com/20.json`\n     */\n    static applyRequestParametersToUrl(url, parameters) {\n        url.pathname = url.pathname.replace(/:([A-Z_-]+)/ig, (fullMatch, paramName) => {\n            if (parameters[paramName] !== undefined) {\n                return String(parameters[paramName]);\n            }\n            return fullMatch;\n        });\n        return url;\n    }\n}\nRequestParamHelpers.JSON_1_1_ENDPOINTS = new Set([\n    'direct_messages/events/new.json',\n    'direct_messages/welcome_messages/new.json',\n    'direct_messages/welcome_messages/rules/new.json',\n    'media/metadata/create.json',\n    'collections/entries/curate.json',\n]);\nexport default RequestParamHelpers;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,MAAM;IACT,OAAO,oBAAoB,KAAK,EAAE;QAC9B,MAAM,iBAAiB,CAAC;QACxB,IAAK,MAAM,QAAQ,MAAO;YACtB,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,UAAU;gBACjC,cAAc,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;YACtC,OACK,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,aAAa;gBACzC,cAAc,CAAC,KAAK,GAAG,OAAO,KAAK,CAAC,KAAK;YAC7C;QACJ;QACA,OAAO;IACX;IACA,OAAO,mBAAmB,GAAG,EAAE;QAC3B,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,aAAa;YACvE,2BAA2B;YAC3B,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,cAAc;gBACtC,OAAO;YACX;YACA,+DAA+D;YAC/D,OAAO;QACX;QACA,IAAI,IAAI,QAAQ,KAAK,sBAAsB;YACvC,IAAI,IAAI,QAAQ,KAAK,0BAA0B;gBAC3C,OAAO;YACX;YACA,2DAA2D;YAC3D,OAAO;QACX;QACA,MAAM,WAAW,IAAI,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE;QAClD,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW;YACvC,OAAO;QACX;QACA,OAAO;IACX;IACA,OAAO,oBAAoB,GAAG,EAAE,KAAK,EAAE;QACnC,MAAM,eAAe,OAAO,OAAO,CAAC;QACpC,IAAI,aAAa,MAAM,EAAE;YACrB,IAAI,SAAS;YACb,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,aAAc;gBACrC,UAAU,CAAC,OAAO,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,6LAAA,CAAA,UAAY,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,6LAAA,CAAA,UAAY,CAAC,aAAa,CAAC,QAAQ;YACrH;YACA,IAAI,MAAM,GAAG;QACjB;IACJ;IACA,OAAO,oBAAoB,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;QAC5C,IAAI,gBAAgB,QAAQ;YACxB,OAAO;QACX;QACA,IAAI,SAAS,QAAQ;YACjB,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;gBAC1B,OAAO,CAAC,eAAe,GAAG;YAC9B;YACA,OAAO,KAAK,SAAS,CAAC;QAC1B,OACK,IAAI,SAAS,OAAO;YACrB,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;gBAC1B,OAAO,CAAC,eAAe,GAAG;YAC9B;YACA,IAAI,OAAO,IAAI,CAAC,MAAM,MAAM,EAAE;gBAC1B,OAAO,IAAI,gBAAgB,MACtB,QAAQ,GACR,OAAO,CAAC,OAAO,QAAQ,mEAAmE;YACnG;YACA,OAAO;QACX,OACK,IAAI,SAAS,OAAO;YACrB,MAAM,IAAI,MAAM;QACpB,OACK;YACD,MAAM,OAAO,IAAI,mMAAA,CAAA,iBAAc;YAC/B,IAAK,MAAM,aAAa,KAAM;gBAC1B,KAAK,MAAM,CAAC,WAAW,IAAI,CAAC,UAAU;YAC1C;YACA,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;gBAC1B,MAAM,cAAc,KAAK,UAAU;gBACnC,OAAO,CAAC,eAAe,GAAG,WAAW,CAAC,eAAe;YACzD;YACA,OAAO,KAAK,SAAS;QACzB;IACJ;IACA,OAAO,oBAAoB,OAAO,EAAE,IAAI,EAAE;QACtC,IAAI;QACJ,QAAQ,OAAO,GAAG,CAAC,KAAK,QAAQ,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,CAAC;QAC3E,IAAI,OAAO,SAAS,UAAU;YAC1B,QAAQ,OAAO,CAAC,iBAAiB,GAAG,OAAO,UAAU,CAAC;QAC1D,OACK;YACD,QAAQ,OAAO,CAAC,iBAAiB,GAAG,KAAK,MAAM;QACnD;IACJ;IACA,OAAO,oBAAoB,IAAI,EAAE;QAC7B,OAAO,CAAC,CAAC,gBAAgB,MAAM;IACnC;IACA,OAAO,0BAA0B,KAAK,EAAE,IAAI,EAAE;QAC1C,MAAM,aAAa,CAAC;QACpB,IAAK,MAAM,QAAQ,MAAO;YACtB,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;QAClC;QACA,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO;YAChC,IAAK,MAAM,QAAQ,KAAM;gBACrB,MAAM,WAAW,IAAI,CAAC,KAAK;gBAC3B,IAAI,IAAI,CAAC,mBAAmB,CAAC,WAAW;oBACpC,UAAU,CAAC,KAAK,GAAG,OAAO,aAAa,YAAY,aAAa,QAAQ,cAAc,WAChF,SAAS,QAAQ,KACjB;gBACV;YACJ;QACJ;QACA,OAAO;IACX;IACA,OAAO,6BAA6B,GAAG,EAAE,KAAK,EAAE;QAC5C,KAAK,MAAM,CAAC,OAAO,MAAM,IAAI,IAAI,YAAY,CAAE;YAC3C,KAAK,CAAC,MAAM,GAAG;QACnB;QACA,0BAA0B;QAC1B,IAAI,MAAM,GAAG;QACb,OAAO;IACX;IACA;;;KAGC,GACD,OAAO,4BAA4B,GAAG,EAAE,UAAU,EAAE;QAChD,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,WAAW;YAC7D,IAAI,UAAU,CAAC,UAAU,KAAK,WAAW;gBACrC,OAAO,OAAO,UAAU,CAAC,UAAU;YACvC;YACA,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACA,oBAAoB,kBAAkB,GAAG,IAAI,IAAI;IAC7C;IACA;IACA;IACA;IACA;CACH;uCACc", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2223, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/client-mixins/oauth2.helper.js"], "sourcesContent": ["import * as crypto from 'crypto';\nexport class OAuth2Helper {\n    static getCodeVerifier() {\n        return this.generateRandomString(128);\n    }\n    static getCodeChallengeFromVerifier(verifier) {\n        return this.escapeBase64Url(crypto\n            .createHash('sha256')\n            .update(verifier)\n            .digest('base64'));\n    }\n    static getAuthHeader(clientId, clientSecret) {\n        const key = encodeURIComponent(clientId) + ':' + encodeURIComponent(clientSecret);\n        return Buffer.from(key).toString('base64');\n    }\n    static generateRandomString(length) {\n        let text = '';\n        const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';\n        for (let i = 0; i < length; i++) {\n            text += possible[Math.floor(Math.random() * possible.length)];\n        }\n        return text;\n    }\n    static escapeBase64Url(string) {\n        return string.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM;IACT,OAAO,kBAAkB;QACrB,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC;IACA,OAAO,6BAA6B,QAAQ,EAAE;QAC1C,OAAO,IAAI,CAAC,eAAe,CAAC,CAAA,GAAA,qGAAA,CAAA,aACb,AAAD,EAAE,UACX,MAAM,CAAC,UACP,MAAM,CAAC;IAChB;IACA,OAAO,cAAc,QAAQ,EAAE,YAAY,EAAE;QACzC,MAAM,MAAM,mBAAmB,YAAY,MAAM,mBAAmB;QACpE,OAAO,OAAO,IAAI,CAAC,KAAK,QAAQ,CAAC;IACrC;IACA,OAAO,qBAAqB,MAAM,EAAE;QAChC,IAAI,OAAO;QACX,MAAM,WAAW;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC7B,QAAQ,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;QACjE;QACA,OAAO;IACX;IACA,OAAO,gBAAgB,MAAM,EAAE;QAC3B,OAAO,OAAO,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO;IACvE;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2257, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/client-mixins/request-maker.mixin.js"], "sourcesContent": ["import { TwitterApiPluginResponseOverride } from '../types';\nimport TweetStream from '../stream/TweetStream';\nimport { applyResponseHooks, hasRequestErrorPlugins } from '../plugins/helpers';\nimport { trimUndefinedProperties } from '../helpers';\nimport OAuth1<PERSON><PERSON>per from './oauth1.helper';\nimport <PERSON>quest<PERSON><PERSON>lerHelper from './request-handler.helper';\nimport RequestParamHelpers from './request-param.helper';\nimport { OAuth2Helper } from './oauth2.helper';\nexport class ClientRequestMaker {\n    constructor(settings) {\n        this.rateLimits = {};\n        this.clientSettings = {};\n        if (settings) {\n            this.clientSettings = settings;\n        }\n    }\n    /** @deprecated - Switch to `@twitter-api-v2/plugin-rate-limit` */\n    getRateLimits() {\n        return this.rateLimits;\n    }\n    saveRateLimit(originalUrl, rateLimit) {\n        this.rateLimits[originalUrl] = rateLimit;\n    }\n    /** Send a new request and returns a wrapped `Promise<TwitterResponse<T>`. */\n    async send(requestParams) {\n        var _a, _b, _c, _d, _e;\n        // Pre-request config hooks\n        if ((_a = this.clientSettings.plugins) === null || _a === void 0 ? void 0 : _a.length) {\n            const possibleResponse = await this.applyPreRequestConfigHooks(requestParams);\n            if (possibleResponse) {\n                return possibleResponse;\n            }\n        }\n        const args = this.getHttpRequestArgs(requestParams);\n        const options = {\n            method: args.method,\n            headers: args.headers,\n            timeout: requestParams.timeout,\n            agent: this.clientSettings.httpAgent,\n        };\n        const enableRateLimitSave = requestParams.enableRateLimitSave !== false;\n        if (args.body) {\n            RequestParamHelpers.setBodyLengthHeader(options, args.body);\n        }\n        // Pre-request hooks\n        if ((_b = this.clientSettings.plugins) === null || _b === void 0 ? void 0 : _b.length) {\n            await this.applyPreRequestHooks(requestParams, args, options);\n        }\n        let request = new RequestHandlerHelper({\n            url: args.url,\n            options,\n            body: args.body,\n            rateLimitSaver: enableRateLimitSave ? this.saveRateLimit.bind(this, args.rawUrl) : undefined,\n            requestEventDebugHandler: requestParams.requestEventDebugHandler,\n            compression: (_d = (_c = requestParams.compression) !== null && _c !== void 0 ? _c : this.clientSettings.compression) !== null && _d !== void 0 ? _d : true,\n            forceParseMode: requestParams.forceParseMode,\n        })\n            .makeRequest();\n        if (hasRequestErrorPlugins(this)) {\n            request = this.applyResponseErrorHooks(requestParams, args, options, request);\n        }\n        const response = await request;\n        // Post-request hooks\n        if ((_e = this.clientSettings.plugins) === null || _e === void 0 ? void 0 : _e.length) {\n            const responseOverride = await this.applyPostRequestHooks(requestParams, args, options, response);\n            if (responseOverride) {\n                return responseOverride.value;\n            }\n        }\n        return response;\n    }\n    sendStream(requestParams) {\n        var _a, _b;\n        // Pre-request hooks\n        if (this.clientSettings.plugins) {\n            this.applyPreStreamRequestConfigHooks(requestParams);\n        }\n        const args = this.getHttpRequestArgs(requestParams);\n        const options = {\n            method: args.method,\n            headers: args.headers,\n            agent: this.clientSettings.httpAgent,\n        };\n        const enableRateLimitSave = requestParams.enableRateLimitSave !== false;\n        const enableAutoConnect = requestParams.autoConnect !== false;\n        if (args.body) {\n            RequestParamHelpers.setBodyLengthHeader(options, args.body);\n        }\n        const requestData = {\n            url: args.url,\n            options,\n            body: args.body,\n            rateLimitSaver: enableRateLimitSave ? this.saveRateLimit.bind(this, args.rawUrl) : undefined,\n            payloadIsError: requestParams.payloadIsError,\n            compression: (_b = (_a = requestParams.compression) !== null && _a !== void 0 ? _a : this.clientSettings.compression) !== null && _b !== void 0 ? _b : true,\n        };\n        const stream = new TweetStream(requestData);\n        if (!enableAutoConnect) {\n            return stream;\n        }\n        return stream.connect();\n    }\n    /* Token helpers */\n    initializeToken(token) {\n        if (typeof token === 'string') {\n            this.bearerToken = token;\n        }\n        else if (typeof token === 'object' && 'appKey' in token) {\n            this.consumerToken = token.appKey;\n            this.consumerSecret = token.appSecret;\n            if (token.accessToken && token.accessSecret) {\n                this.accessToken = token.accessToken;\n                this.accessSecret = token.accessSecret;\n            }\n            this._oauth = this.buildOAuth();\n        }\n        else if (typeof token === 'object' && 'username' in token) {\n            const key = encodeURIComponent(token.username) + ':' + encodeURIComponent(token.password);\n            this.basicToken = Buffer.from(key).toString('base64');\n        }\n        else if (typeof token === 'object' && 'clientId' in token) {\n            this.clientId = token.clientId;\n            this.clientSecret = token.clientSecret;\n        }\n    }\n    getActiveTokens() {\n        if (this.bearerToken) {\n            return {\n                type: 'oauth2',\n                bearerToken: this.bearerToken,\n            };\n        }\n        else if (this.basicToken) {\n            return {\n                type: 'basic',\n                token: this.basicToken,\n            };\n        }\n        else if (this.consumerSecret && this._oauth) {\n            return {\n                type: 'oauth-1.0a',\n                appKey: this.consumerToken,\n                appSecret: this.consumerSecret,\n                accessToken: this.accessToken,\n                accessSecret: this.accessSecret,\n            };\n        }\n        else if (this.clientId) {\n            return {\n                type: 'oauth2-user',\n                clientId: this.clientId,\n            };\n        }\n        return { type: 'none' };\n    }\n    buildOAuth() {\n        if (!this.consumerSecret || !this.consumerToken)\n            throw new Error('Invalid consumer tokens');\n        return new OAuth1Helper({\n            consumerKeys: { key: this.consumerToken, secret: this.consumerSecret },\n        });\n    }\n    getOAuthAccessTokens() {\n        if (!this.accessSecret || !this.accessToken)\n            return;\n        return {\n            key: this.accessToken,\n            secret: this.accessSecret,\n        };\n    }\n    /* Plugin helpers */\n    getPlugins() {\n        var _a;\n        return (_a = this.clientSettings.plugins) !== null && _a !== void 0 ? _a : [];\n    }\n    hasPlugins() {\n        var _a;\n        return !!((_a = this.clientSettings.plugins) === null || _a === void 0 ? void 0 : _a.length);\n    }\n    async applyPluginMethod(method, args) {\n        var _a;\n        let returnValue;\n        for (const plugin of this.getPlugins()) {\n            const value = await ((_a = plugin[method]) === null || _a === void 0 ? void 0 : _a.call(plugin, args));\n            if (value && value instanceof TwitterApiPluginResponseOverride) {\n                returnValue = value;\n            }\n        }\n        return returnValue;\n    }\n    /* Request helpers */\n    writeAuthHeaders({ headers, bodyInSignature, url, method, query, body }) {\n        headers = { ...headers };\n        if (this.bearerToken) {\n            headers.Authorization = 'Bearer ' + this.bearerToken;\n        }\n        else if (this.basicToken) {\n            // Basic auth, to request a bearer token\n            headers.Authorization = 'Basic ' + this.basicToken;\n        }\n        else if (this.clientId && this.clientSecret) {\n            // Basic auth with clientId + clientSecret\n            headers.Authorization = 'Basic ' + OAuth2Helper.getAuthHeader(this.clientId, this.clientSecret);\n        }\n        else if (this.consumerSecret && this._oauth) {\n            // Merge query and body\n            const data = bodyInSignature ? RequestParamHelpers.mergeQueryAndBodyForOAuth(query, body) : query;\n            const auth = this._oauth.authorize({\n                url: url.toString(),\n                method,\n                data,\n            }, this.getOAuthAccessTokens());\n            headers = { ...headers, ...this._oauth.toHeader(auth) };\n        }\n        return headers;\n    }\n    getUrlObjectFromUrlString(url) {\n        // Add protocol to URL if needed\n        if (!url.startsWith('http')) {\n            url = 'https://' + url;\n        }\n        // Convert URL to object that will receive all URL modifications\n        return new URL(url);\n    }\n    getHttpRequestArgs({ url: stringUrl, method, query: rawQuery = {}, body: rawBody = {}, headers, forceBodyMode, enableAuth, params, }) {\n        let body = undefined;\n        method = method.toUpperCase();\n        headers = headers !== null && headers !== void 0 ? headers : {};\n        // Add user agent header (Twitter recommends it)\n        if (!headers['x-user-agent']) {\n            headers['x-user-agent'] = 'Node.twitter-api-v2';\n        }\n        const url = this.getUrlObjectFromUrlString(stringUrl);\n        // URL without query string to save as endpoint name\n        const rawUrl = url.origin + url.pathname;\n        // Apply URL parameters\n        if (params) {\n            RequestParamHelpers.applyRequestParametersToUrl(url, params);\n        }\n        // Build a URL without anything in QS, and QSP in query\n        const query = RequestParamHelpers.formatQueryToString(rawQuery);\n        RequestParamHelpers.moveUrlQueryParamsIntoObject(url, query);\n        // Delete undefined parameters\n        if (!(rawBody instanceof Buffer)) {\n            trimUndefinedProperties(rawBody);\n        }\n        // OAuth signature should not include parameters when using multipart.\n        const bodyType = forceBodyMode !== null && forceBodyMode !== void 0 ? forceBodyMode : RequestParamHelpers.autoDetectBodyType(url);\n        // If undefined or true, enable auth by headers\n        if (enableAuth !== false) {\n            // OAuth needs body signature only if body is URL encoded.\n            const bodyInSignature = ClientRequestMaker.BODY_METHODS.has(method) && bodyType === 'url';\n            headers = this.writeAuthHeaders({ headers, bodyInSignature, method, query, url, body: rawBody });\n        }\n        if (ClientRequestMaker.BODY_METHODS.has(method)) {\n            body = RequestParamHelpers.constructBodyParams(rawBody, headers, bodyType) || undefined;\n        }\n        RequestParamHelpers.addQueryParamsToUrl(url, query);\n        return {\n            rawUrl,\n            url,\n            method,\n            headers,\n            body,\n        };\n    }\n    /* Plugin helpers */\n    async applyPreRequestConfigHooks(requestParams) {\n        var _a;\n        const url = this.getUrlObjectFromUrlString(requestParams.url);\n        for (const plugin of this.getPlugins()) {\n            const result = await ((_a = plugin.onBeforeRequestConfig) === null || _a === void 0 ? void 0 : _a.call(plugin, {\n                client: this,\n                url,\n                params: requestParams,\n            }));\n            if (result) {\n                return result;\n            }\n        }\n    }\n    applyPreStreamRequestConfigHooks(requestParams) {\n        var _a;\n        const url = this.getUrlObjectFromUrlString(requestParams.url);\n        for (const plugin of this.getPlugins()) {\n            (_a = plugin.onBeforeStreamRequestConfig) === null || _a === void 0 ? void 0 : _a.call(plugin, {\n                client: this,\n                url,\n                params: requestParams,\n            });\n        }\n    }\n    async applyPreRequestHooks(requestParams, computedParams, requestOptions) {\n        await this.applyPluginMethod('onBeforeRequest', {\n            client: this,\n            url: this.getUrlObjectFromUrlString(requestParams.url),\n            params: requestParams,\n            computedParams,\n            requestOptions,\n        });\n    }\n    async applyPostRequestHooks(requestParams, computedParams, requestOptions, response) {\n        return await this.applyPluginMethod('onAfterRequest', {\n            client: this,\n            url: this.getUrlObjectFromUrlString(requestParams.url),\n            params: requestParams,\n            computedParams,\n            requestOptions,\n            response,\n        });\n    }\n    applyResponseErrorHooks(requestParams, computedParams, requestOptions, promise) {\n        return promise.catch(applyResponseHooks.bind(this, requestParams, computedParams, requestOptions));\n    }\n}\nClientRequestMaker.BODY_METHODS = new Set(['POST', 'PUT', 'PATCH']);\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACO,MAAM;IACT,YAAY,QAAQ,CAAE;QAClB,IAAI,CAAC,UAAU,GAAG,CAAC;QACnB,IAAI,CAAC,cAAc,GAAG,CAAC;QACvB,IAAI,UAAU;YACV,IAAI,CAAC,cAAc,GAAG;QAC1B;IACJ;IACA,gEAAgE,GAChE,gBAAgB;QACZ,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA,cAAc,WAAW,EAAE,SAAS,EAAE;QAClC,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG;IACnC;IACA,2EAA2E,GAC3E,MAAM,KAAK,aAAa,EAAE;QACtB,IAAI,IAAI,IAAI,IAAI,IAAI;QACpB,2BAA2B;QAC3B,IAAI,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,EAAE;YACnF,MAAM,mBAAmB,MAAM,IAAI,CAAC,0BAA0B,CAAC;YAC/D,IAAI,kBAAkB;gBAClB,OAAO;YACX;QACJ;QACA,MAAM,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACrC,MAAM,UAAU;YACZ,QAAQ,KAAK,MAAM;YACnB,SAAS,KAAK,OAAO;YACrB,SAAS,cAAc,OAAO;YAC9B,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS;QACxC;QACA,MAAM,sBAAsB,cAAc,mBAAmB,KAAK;QAClE,IAAI,KAAK,IAAI,EAAE;YACX,uMAAA,CAAA,UAAmB,CAAC,mBAAmB,CAAC,SAAS,KAAK,IAAI;QAC9D;QACA,oBAAoB;QACpB,IAAI,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,EAAE;YACnF,MAAM,IAAI,CAAC,oBAAoB,CAAC,eAAe,MAAM;QACzD;QACA,IAAI,UAAU,IAAI,yMAAA,CAAA,UAAoB,CAAC;YACnC,KAAK,KAAK,GAAG;YACb;YACA,MAAM,KAAK,IAAI;YACf,gBAAgB,sBAAsB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,MAAM,IAAI;YACnF,0BAA0B,cAAc,wBAAwB;YAChE,aAAa,CAAC,KAAK,CAAC,KAAK,cAAc,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,CAAC,cAAc,CAAC,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;YACvJ,gBAAgB,cAAc,cAAc;QAChD,GACK,WAAW;QAChB,IAAI,CAAA,GAAA,2KAAA,CAAA,yBAAsB,AAAD,EAAE,IAAI,GAAG;YAC9B,UAAU,IAAI,CAAC,uBAAuB,CAAC,eAAe,MAAM,SAAS;QACzE;QACA,MAAM,WAAW,MAAM;QACvB,qBAAqB;QACrB,IAAI,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,EAAE;YACnF,MAAM,mBAAmB,MAAM,IAAI,CAAC,qBAAqB,CAAC,eAAe,MAAM,SAAS;YACxF,IAAI,kBAAkB;gBAClB,OAAO,iBAAiB,KAAK;YACjC;QACJ;QACA,OAAO;IACX;IACA,WAAW,aAAa,EAAE;QACtB,IAAI,IAAI;QACR,oBAAoB;QACpB,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;YAC7B,IAAI,CAAC,gCAAgC,CAAC;QAC1C;QACA,MAAM,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACrC,MAAM,UAAU;YACZ,QAAQ,KAAK,MAAM;YACnB,SAAS,KAAK,OAAO;YACrB,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS;QACxC;QACA,MAAM,sBAAsB,cAAc,mBAAmB,KAAK;QAClE,MAAM,oBAAoB,cAAc,WAAW,KAAK;QACxD,IAAI,KAAK,IAAI,EAAE;YACX,uMAAA,CAAA,UAAmB,CAAC,mBAAmB,CAAC,SAAS,KAAK,IAAI;QAC9D;QACA,MAAM,cAAc;YAChB,KAAK,KAAK,GAAG;YACb;YACA,MAAM,KAAK,IAAI;YACf,gBAAgB,sBAAsB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,MAAM,IAAI;YACnF,gBAAgB,cAAc,cAAc;YAC5C,aAAa,CAAC,KAAK,CAAC,KAAK,cAAc,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,CAAC,cAAc,CAAC,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAC3J;QACA,MAAM,SAAS,IAAI,8KAAA,CAAA,UAAW,CAAC;QAC/B,IAAI,CAAC,mBAAmB;YACpB,OAAO;QACX;QACA,OAAO,OAAO,OAAO;IACzB;IACA,iBAAiB,GACjB,gBAAgB,KAAK,EAAE;QACnB,IAAI,OAAO,UAAU,UAAU;YAC3B,IAAI,CAAC,WAAW,GAAG;QACvB,OACK,IAAI,OAAO,UAAU,YAAY,YAAY,OAAO;YACrD,IAAI,CAAC,aAAa,GAAG,MAAM,MAAM;YACjC,IAAI,CAAC,cAAc,GAAG,MAAM,SAAS;YACrC,IAAI,MAAM,WAAW,IAAI,MAAM,YAAY,EAAE;gBACzC,IAAI,CAAC,WAAW,GAAG,MAAM,WAAW;gBACpC,IAAI,CAAC,YAAY,GAAG,MAAM,YAAY;YAC1C;YACA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU;QACjC,OACK,IAAI,OAAO,UAAU,YAAY,cAAc,OAAO;YACvD,MAAM,MAAM,mBAAmB,MAAM,QAAQ,IAAI,MAAM,mBAAmB,MAAM,QAAQ;YACxF,IAAI,CAAC,UAAU,GAAG,OAAO,IAAI,CAAC,KAAK,QAAQ,CAAC;QAChD,OACK,IAAI,OAAO,UAAU,YAAY,cAAc,OAAO;YACvD,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YAC9B,IAAI,CAAC,YAAY,GAAG,MAAM,YAAY;QAC1C;IACJ;IACA,kBAAkB;QACd,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,OAAO;gBACH,MAAM;gBACN,aAAa,IAAI,CAAC,WAAW;YACjC;QACJ,OACK,IAAI,IAAI,CAAC,UAAU,EAAE;YACtB,OAAO;gBACH,MAAM;gBACN,OAAO,IAAI,CAAC,UAAU;YAC1B;QACJ,OACK,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM,EAAE;YACzC,OAAO;gBACH,MAAM;gBACN,QAAQ,IAAI,CAAC,aAAa;gBAC1B,WAAW,IAAI,CAAC,cAAc;gBAC9B,aAAa,IAAI,CAAC,WAAW;gBAC7B,cAAc,IAAI,CAAC,YAAY;YACnC;QACJ,OACK,IAAI,IAAI,CAAC,QAAQ,EAAE;YACpB,OAAO;gBACH,MAAM;gBACN,UAAU,IAAI,CAAC,QAAQ;YAC3B;QACJ;QACA,OAAO;YAAE,MAAM;QAAO;IAC1B;IACA,aAAa;QACT,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,aAAa,EAC3C,MAAM,IAAI,MAAM;QACpB,OAAO,IAAI,6LAAA,CAAA,UAAY,CAAC;YACpB,cAAc;gBAAE,KAAK,IAAI,CAAC,aAAa;gBAAE,QAAQ,IAAI,CAAC,cAAc;YAAC;QACzE;IACJ;IACA,uBAAuB;QACnB,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,WAAW,EACvC;QACJ,OAAO;YACH,KAAK,IAAI,CAAC,WAAW;YACrB,QAAQ,IAAI,CAAC,YAAY;QAC7B;IACJ;IACA,kBAAkB,GAClB,aAAa;QACT,IAAI;QACJ,OAAO,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;IACjF;IACA,aAAa;QACT,IAAI;QACJ,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM;IAC/F;IACA,MAAM,kBAAkB,MAAM,EAAE,IAAI,EAAE;QAClC,IAAI;QACJ,IAAI;QACJ,KAAK,MAAM,UAAU,IAAI,CAAC,UAAU,GAAI;YACpC,MAAM,QAAQ,MAAM,CAAC,CAAC,KAAK,MAAM,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,QAAQ,KAAK;YACrG,IAAI,SAAS,iBAAiB,uMAAA,CAAA,mCAAgC,EAAE;gBAC5D,cAAc;YAClB;QACJ;QACA,OAAO;IACX;IACA,mBAAmB,GACnB,iBAAiB,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;QACrE,UAAU;YAAE,GAAG,OAAO;QAAC;QACvB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,QAAQ,aAAa,GAAG,YAAY,IAAI,CAAC,WAAW;QACxD,OACK,IAAI,IAAI,CAAC,UAAU,EAAE;YACtB,wCAAwC;YACxC,QAAQ,aAAa,GAAG,WAAW,IAAI,CAAC,UAAU;QACtD,OACK,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,EAAE;YACzC,0CAA0C;YAC1C,QAAQ,aAAa,GAAG,WAAW,6LAAA,CAAA,eAAY,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY;QAClG,OACK,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM,EAAE;YACzC,uBAAuB;YACvB,MAAM,OAAO,kBAAkB,uMAAA,CAAA,UAAmB,CAAC,yBAAyB,CAAC,OAAO,QAAQ;YAC5F,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;gBAC/B,KAAK,IAAI,QAAQ;gBACjB;gBACA;YACJ,GAAG,IAAI,CAAC,oBAAoB;YAC5B,UAAU;gBAAE,GAAG,OAAO;gBAAE,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK;YAAC;QAC1D;QACA,OAAO;IACX;IACA,0BAA0B,GAAG,EAAE;QAC3B,gCAAgC;QAChC,IAAI,CAAC,IAAI,UAAU,CAAC,SAAS;YACzB,MAAM,aAAa;QACvB;QACA,gEAAgE;QAChE,OAAO,IAAI,IAAI;IACnB;IACA,mBAAmB,EAAE,KAAK,SAAS,EAAE,MAAM,EAAE,OAAO,WAAW,CAAC,CAAC,EAAE,MAAM,UAAU,CAAC,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,EAAG,EAAE;QAClI,IAAI,OAAO;QACX,SAAS,OAAO,WAAW;QAC3B,UAAU,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU,CAAC;QAC9D,gDAAgD;QAChD,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;YAC1B,OAAO,CAAC,eAAe,GAAG;QAC9B;QACA,MAAM,MAAM,IAAI,CAAC,yBAAyB,CAAC;QAC3C,oDAAoD;QACpD,MAAM,SAAS,IAAI,MAAM,GAAG,IAAI,QAAQ;QACxC,uBAAuB;QACvB,IAAI,QAAQ;YACR,uMAAA,CAAA,UAAmB,CAAC,2BAA2B,CAAC,KAAK;QACzD;QACA,uDAAuD;QACvD,MAAM,QAAQ,uMAAA,CAAA,UAAmB,CAAC,mBAAmB,CAAC;QACtD,uMAAA,CAAA,UAAmB,CAAC,4BAA4B,CAAC,KAAK;QACtD,8BAA8B;QAC9B,IAAI,CAAC,CAAC,mBAAmB,MAAM,GAAG;YAC9B,CAAA,GAAA,gKAAA,CAAA,0BAAuB,AAAD,EAAE;QAC5B;QACA,sEAAsE;QACtE,MAAM,WAAW,kBAAkB,QAAQ,kBAAkB,KAAK,IAAI,gBAAgB,uMAAA,CAAA,UAAmB,CAAC,kBAAkB,CAAC;QAC7H,+CAA+C;QAC/C,IAAI,eAAe,OAAO;YACtB,0DAA0D;YAC1D,MAAM,kBAAkB,mBAAmB,YAAY,CAAC,GAAG,CAAC,WAAW,aAAa;YACpF,UAAU,IAAI,CAAC,gBAAgB,CAAC;gBAAE;gBAAS;gBAAiB;gBAAQ;gBAAO;gBAAK,MAAM;YAAQ;QAClG;QACA,IAAI,mBAAmB,YAAY,CAAC,GAAG,CAAC,SAAS;YAC7C,OAAO,uMAAA,CAAA,UAAmB,CAAC,mBAAmB,CAAC,SAAS,SAAS,aAAa;QAClF;QACA,uMAAA,CAAA,UAAmB,CAAC,mBAAmB,CAAC,KAAK;QAC7C,OAAO;YACH;YACA;YACA;YACA;YACA;QACJ;IACJ;IACA,kBAAkB,GAClB,MAAM,2BAA2B,aAAa,EAAE;QAC5C,IAAI;QACJ,MAAM,MAAM,IAAI,CAAC,yBAAyB,CAAC,cAAc,GAAG;QAC5D,KAAK,MAAM,UAAU,IAAI,CAAC,UAAU,GAAI;YACpC,MAAM,SAAS,MAAM,CAAC,CAAC,KAAK,OAAO,qBAAqB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,QAAQ;gBAC3G,QAAQ,IAAI;gBACZ;gBACA,QAAQ;YACZ,EAAE;YACF,IAAI,QAAQ;gBACR,OAAO;YACX;QACJ;IACJ;IACA,iCAAiC,aAAa,EAAE;QAC5C,IAAI;QACJ,MAAM,MAAM,IAAI,CAAC,yBAAyB,CAAC,cAAc,GAAG;QAC5D,KAAK,MAAM,UAAU,IAAI,CAAC,UAAU,GAAI;YACpC,CAAC,KAAK,OAAO,2BAA2B,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,QAAQ;gBAC3F,QAAQ,IAAI;gBACZ;gBACA,QAAQ;YACZ;QACJ;IACJ;IACA,MAAM,qBAAqB,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE;QACtE,MAAM,IAAI,CAAC,iBAAiB,CAAC,mBAAmB;YAC5C,QAAQ,IAAI;YACZ,KAAK,IAAI,CAAC,yBAAyB,CAAC,cAAc,GAAG;YACrD,QAAQ;YACR;YACA;QACJ;IACJ;IACA,MAAM,sBAAsB,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE;QACjF,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,kBAAkB;YAClD,QAAQ,IAAI;YACZ,KAAK,IAAI,CAAC,yBAAyB,CAAC,cAAc,GAAG;YACrD,QAAQ;YACR;YACA;YACA;QACJ;IACJ;IACA,wBAAwB,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,OAAO,EAAE;QAC5E,OAAO,QAAQ,KAAK,CAAC,2KAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,gBAAgB;IACtF;AACJ;AACA,mBAAmB,YAAY,GAAG,IAAI,IAAI;IAAC;IAAQ;IAAO;CAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2594, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/client.base.js"], "sourcesContent": ["import { ClientRequestMaker } from './client-mixins/request-maker.mixin';\nimport { sharedPromise } from './helpers';\nimport { API_V1_1_PREFIX, API_V2_PREFIX } from './globals';\n/**\n * Base class for Twitter instances\n */\nexport default class TwitterApiBase {\n    constructor(token, settings = {}) {\n        this._currentUser = null;\n        this._currentUserV2 = null;\n        if (token instanceof TwitterApiBase) {\n            this._requestMaker = token._requestMaker;\n        }\n        else {\n            this._requestMaker = new ClientRequestMaker(settings);\n            this._requestMaker.initializeToken(token);\n        }\n    }\n    /* Prefix/Token handling */\n    setPrefix(prefix) {\n        this._prefix = prefix;\n    }\n    cloneWithPrefix(prefix) {\n        const clone = this.constructor(this);\n        clone.setPrefix(prefix);\n        return clone;\n    }\n    getActiveTokens() {\n        return this._requestMaker.getActiveTokens();\n    }\n    /* Rate limit cache / Plugins */\n    getPlugins() {\n        return this._requestMaker.getPlugins();\n    }\n    getPluginOfType(type) {\n        return this.getPlugins().find(plugin => plugin instanceof type);\n    }\n    /**\n     * @deprecated - Migrate to plugin `@twitter-api-v2/plugin-rate-limit`\n     *\n     * Tells if you hit the Twitter rate limit for {endpoint}.\n     * (local data only, this should not ask anything to Twitter)\n     */\n    hasHitRateLimit(endpoint) {\n        var _a;\n        if (this.isRateLimitStatusObsolete(endpoint)) {\n            return false;\n        }\n        return ((_a = this.getLastRateLimitStatus(endpoint)) === null || _a === void 0 ? void 0 : _a.remaining) === 0;\n    }\n    /**\n     * @deprecated - Migrate to plugin `@twitter-api-v2/plugin-rate-limit`\n     *\n     * Tells if you hit the returned Twitter rate limit for {endpoint} has expired.\n     * If client has no saved rate limit data for {endpoint}, this will gives you `true`.\n     */\n    isRateLimitStatusObsolete(endpoint) {\n        const rateLimit = this.getLastRateLimitStatus(endpoint);\n        if (rateLimit === undefined) {\n            return true;\n        }\n        // Timestamps are exprimed in seconds, JS works with ms\n        return (rateLimit.reset * 1000) < Date.now();\n    }\n    /**\n     * @deprecated - Migrate to plugin `@twitter-api-v2/plugin-rate-limit`\n     *\n     * Get the last obtained Twitter rate limit information for {endpoint}.\n     * (local data only, this should not ask anything to Twitter)\n     */\n    getLastRateLimitStatus(endpoint) {\n        const endpointWithPrefix = endpoint.match(/^https?:\\/\\//) ? endpoint : (this._prefix + endpoint);\n        return this._requestMaker.getRateLimits()[endpointWithPrefix];\n    }\n    /* Current user cache */\n    /** Get cached current user. */\n    getCurrentUserObject(forceFetch = false) {\n        if (!forceFetch && this._currentUser) {\n            if (this._currentUser.value) {\n                return Promise.resolve(this._currentUser.value);\n            }\n            return this._currentUser.promise;\n        }\n        this._currentUser = sharedPromise(() => this.get('account/verify_credentials.json', { tweet_mode: 'extended' }, { prefix: API_V1_1_PREFIX }));\n        return this._currentUser.promise;\n    }\n    /**\n     * Get cached current user from v2 API.\n     * This can only be the slimest available `UserV2` object, with only `id`, `name` and `username` properties defined.\n     *\n     * To get a customized `UserV2Result`, use `.v2.me()`\n     *\n     * OAuth2 scopes: `tweet.read` & `users.read`\n     */\n    getCurrentUserV2Object(forceFetch = false) {\n        if (!forceFetch && this._currentUserV2) {\n            if (this._currentUserV2.value) {\n                return Promise.resolve(this._currentUserV2.value);\n            }\n            return this._currentUserV2.promise;\n        }\n        this._currentUserV2 = sharedPromise(() => this.get('users/me', undefined, { prefix: API_V2_PREFIX }));\n        return this._currentUserV2.promise;\n    }\n    async get(url, query = {}, { fullResponse, prefix = this._prefix, ...rest } = {}) {\n        if (prefix)\n            url = prefix + url;\n        const resp = await this._requestMaker.send({\n            url,\n            method: 'GET',\n            query,\n            ...rest,\n        });\n        return fullResponse ? resp : resp.data;\n    }\n    async delete(url, query = {}, { fullResponse, prefix = this._prefix, ...rest } = {}) {\n        if (prefix)\n            url = prefix + url;\n        const resp = await this._requestMaker.send({\n            url,\n            method: 'DELETE',\n            query,\n            ...rest,\n        });\n        return fullResponse ? resp : resp.data;\n    }\n    async post(url, body, { fullResponse, prefix = this._prefix, ...rest } = {}) {\n        if (prefix)\n            url = prefix + url;\n        const resp = await this._requestMaker.send({\n            url,\n            method: 'POST',\n            body,\n            ...rest,\n        });\n        return fullResponse ? resp : resp.data;\n    }\n    async put(url, body, { fullResponse, prefix = this._prefix, ...rest } = {}) {\n        if (prefix)\n            url = prefix + url;\n        const resp = await this._requestMaker.send({\n            url,\n            method: 'PUT',\n            body,\n            ...rest,\n        });\n        return fullResponse ? resp : resp.data;\n    }\n    async patch(url, body, { fullResponse, prefix = this._prefix, ...rest } = {}) {\n        if (prefix)\n            url = prefix + url;\n        const resp = await this._requestMaker.send({\n            url,\n            method: 'PATCH',\n            body,\n            ...rest,\n        });\n        return fullResponse ? resp : resp.data;\n    }\n    getStream(url, query, { prefix = this._prefix, ...rest } = {}) {\n        return this._requestMaker.sendStream({\n            url: prefix ? prefix + url : url,\n            method: 'GET',\n            query,\n            ...rest,\n        });\n    }\n    postStream(url, body, { prefix = this._prefix, ...rest } = {}) {\n        return this._requestMaker.sendStream({\n            url: prefix ? prefix + url : url,\n            method: 'POST',\n            body,\n            ...rest,\n        });\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAIe,MAAM;IACjB,YAAY,KAAK,EAAE,WAAW,CAAC,CAAC,CAAE;QAC9B,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,iBAAiB,gBAAgB;YACjC,IAAI,CAAC,aAAa,GAAG,MAAM,aAAa;QAC5C,OACK;YACD,IAAI,CAAC,aAAa,GAAG,IAAI,sMAAA,CAAA,qBAAkB,CAAC;YAC5C,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC;QACvC;IACJ;IACA,yBAAyB,GACzB,UAAU,MAAM,EAAE;QACd,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,gBAAgB,MAAM,EAAE;QACpB,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,IAAI;QACnC,MAAM,SAAS,CAAC;QAChB,OAAO;IACX;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,aAAa,CAAC,eAAe;IAC7C;IACA,8BAA8B,GAC9B,aAAa;QACT,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU;IACxC;IACA,gBAAgB,IAAI,EAAE;QAClB,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAA,SAAU,kBAAkB;IAC9D;IACA;;;;;KAKC,GACD,gBAAgB,QAAQ,EAAE;QACtB,IAAI;QACJ,IAAI,IAAI,CAAC,yBAAyB,CAAC,WAAW;YAC1C,OAAO;QACX;QACA,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC,sBAAsB,CAAC,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,MAAM;IAChH;IACA;;;;;KAKC,GACD,0BAA0B,QAAQ,EAAE;QAChC,MAAM,YAAY,IAAI,CAAC,sBAAsB,CAAC;QAC9C,IAAI,cAAc,WAAW;YACzB,OAAO;QACX;QACA,uDAAuD;QACvD,OAAO,AAAC,UAAU,KAAK,GAAG,OAAQ,KAAK,GAAG;IAC9C;IACA;;;;;KAKC,GACD,uBAAuB,QAAQ,EAAE;QAC7B,MAAM,qBAAqB,SAAS,KAAK,CAAC,kBAAkB,WAAY,IAAI,CAAC,OAAO,GAAG;QACvF,OAAO,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC,mBAAmB;IACjE;IACA,sBAAsB,GACtB,6BAA6B,GAC7B,qBAAqB,aAAa,KAAK,EAAE;QACrC,IAAI,CAAC,cAAc,IAAI,CAAC,YAAY,EAAE;YAClC,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;gBACzB,OAAO,QAAQ,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK;YAClD;YACA,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO;QACpC;QACA,IAAI,CAAC,YAAY,GAAG,CAAA,GAAA,gKAAA,CAAA,gBAAa,AAAD,EAAE,IAAM,IAAI,CAAC,GAAG,CAAC,mCAAmC;gBAAE,YAAY;YAAW,GAAG;gBAAE,QAAQ,gKAAA,CAAA,kBAAe;YAAC;QAC1I,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO;IACpC;IACA;;;;;;;KAOC,GACD,uBAAuB,aAAa,KAAK,EAAE;QACvC,IAAI,CAAC,cAAc,IAAI,CAAC,cAAc,EAAE;YACpC,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;gBAC3B,OAAO,QAAQ,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK;YACpD;YACA,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO;QACtC;QACA,IAAI,CAAC,cAAc,GAAG,CAAA,GAAA,gKAAA,CAAA,gBAAa,AAAD,EAAE,IAAM,IAAI,CAAC,GAAG,CAAC,YAAY,WAAW;gBAAE,QAAQ,gKAAA,CAAA,gBAAa;YAAC;QAClG,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO;IACtC;IACA,MAAM,IAAI,GAAG,EAAE,QAAQ,CAAC,CAAC,EAAE,EAAE,YAAY,EAAE,SAAS,IAAI,CAAC,OAAO,EAAE,GAAG,MAAM,GAAG,CAAC,CAAC,EAAE;QAC9E,IAAI,QACA,MAAM,SAAS;QACnB,MAAM,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACvC;YACA,QAAQ;YACR;YACA,GAAG,IAAI;QACX;QACA,OAAO,eAAe,OAAO,KAAK,IAAI;IAC1C;IACA,MAAM,OAAO,GAAG,EAAE,QAAQ,CAAC,CAAC,EAAE,EAAE,YAAY,EAAE,SAAS,IAAI,CAAC,OAAO,EAAE,GAAG,MAAM,GAAG,CAAC,CAAC,EAAE;QACjF,IAAI,QACA,MAAM,SAAS;QACnB,MAAM,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACvC;YACA,QAAQ;YACR;YACA,GAAG,IAAI;QACX;QACA,OAAO,eAAe,OAAO,KAAK,IAAI;IAC1C;IACA,MAAM,KAAK,GAAG,EAAE,IAAI,EAAE,EAAE,YAAY,EAAE,SAAS,IAAI,CAAC,OAAO,EAAE,GAAG,MAAM,GAAG,CAAC,CAAC,EAAE;QACzE,IAAI,QACA,MAAM,SAAS;QACnB,MAAM,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACvC;YACA,QAAQ;YACR;YACA,GAAG,IAAI;QACX;QACA,OAAO,eAAe,OAAO,KAAK,IAAI;IAC1C;IACA,MAAM,IAAI,GAAG,EAAE,IAAI,EAAE,EAAE,YAAY,EAAE,SAAS,IAAI,CAAC,OAAO,EAAE,GAAG,MAAM,GAAG,CAAC,CAAC,EAAE;QACxE,IAAI,QACA,MAAM,SAAS;QACnB,MAAM,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACvC;YACA,QAAQ;YACR;YACA,GAAG,IAAI;QACX;QACA,OAAO,eAAe,OAAO,KAAK,IAAI;IAC1C;IACA,MAAM,MAAM,GAAG,EAAE,IAAI,EAAE,EAAE,YAAY,EAAE,SAAS,IAAI,CAAC,OAAO,EAAE,GAAG,MAAM,GAAG,CAAC,CAAC,EAAE;QAC1E,IAAI,QACA,MAAM,SAAS;QACnB,MAAM,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACvC;YACA,QAAQ;YACR;YACA,GAAG,IAAI;QACX;QACA,OAAO,eAAe,OAAO,KAAK,IAAI;IAC1C;IACA,UAAU,GAAG,EAAE,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,OAAO,EAAE,GAAG,MAAM,GAAG,CAAC,CAAC,EAAE;QAC3D,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;YACjC,KAAK,SAAS,SAAS,MAAM;YAC7B,QAAQ;YACR;YACA,GAAG,IAAI;QACX;IACJ;IACA,WAAW,GAAG,EAAE,IAAI,EAAE,EAAE,SAAS,IAAI,CAAC,OAAO,EAAE,GAAG,MAAM,GAAG,CAAC,CAAC,EAAE;QAC3D,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;YACjC,KAAK,SAAS,SAAS,MAAM;YAC7B,QAAQ;YACR;YACA,GAAG,IAAI;QACX;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2771, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/client.subclient.js"], "sourcesContent": ["import TwitterApiBase from './client.base';\n/**\n * Base subclient for every v1 and v2 client.\n */\nexport default class Twitter<PERSON>piSubClient extends TwitterApiBase {\n    constructor(instance) {\n        if (!(instance instanceof TwitterApiBase)) {\n            throw new Error('You must instance SubTwitterApi instance from existing TwitterApi instance.');\n        }\n        super(instance);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAIe,MAAM,4BAA4B,uKAAA,CAAA,UAAc;IAC3D,YAAY,QAAQ,CAAE;QAClB,IAAI,CAAC,CAAC,oBAAoB,uKAAA,CAAA,UAAc,GAAG;YACvC,MAAM,IAAI,MAAM;QACpB;QACA,KAAK,CAAC;IACV;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2790, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/paginators/tweet.paginator.v1.js"], "sourcesContent": ["import TwitterPaginator from './TwitterPaginator';\n/** A generic TwitterPaginator able to consume TweetV1 timelines. */\nclass TweetTimelineV1Paginator extends TwitterPaginator {\n    constructor() {\n        super(...arguments);\n        this.hasFinishedFetch = false;\n    }\n    refreshInstanceFromResult(response, isNextPage) {\n        const result = response.data;\n        this._rateLimit = response.rateLimit;\n        if (isNextPage) {\n            this._realData.push(...result);\n            // HINT: This is an approximation, as \"end\" of pagination cannot be safely determined without cursors.\n            this.hasFinishedFetch = result.length === 0;\n        }\n    }\n    getNextQueryParams(maxResults) {\n        const latestId = BigInt(this._realData[this._realData.length - 1].id_str);\n        return {\n            ...this.injectQueryParams(maxResults),\n            max_id: (latestId - BigInt(1)).toString(),\n        };\n    }\n    getPageLengthFromRequest(result) {\n        return result.data.length;\n    }\n    isFetchLastOver(result) {\n        return !result.data.length;\n    }\n    canFetchNextPage(result) {\n        return result.length > 0;\n    }\n    getItemArray() {\n        return this.tweets;\n    }\n    /**\n     * Tweets returned by paginator.\n     */\n    get tweets() {\n        return this._realData;\n    }\n    get done() {\n        return super.done || this.hasFinishedFetch;\n    }\n}\n// Timelines\n// Home\nexport class HomeTimelineV1Paginator extends TweetTimelineV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'statuses/home_timeline.json';\n    }\n}\n// Mention\nexport class MentionTimelineV1Paginator extends TweetTimelineV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'statuses/mentions_timeline.json';\n    }\n}\n// User\nexport class UserTimelineV1Paginator extends TweetTimelineV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'statuses/user_timeline.json';\n    }\n}\n// Lists\nexport class ListTimelineV1Paginator extends TweetTimelineV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'lists/statuses.json';\n    }\n}\n// Favorites\nexport class UserFavoritesV1Paginator extends TweetTimelineV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'favorites/list.json';\n    }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACA,kEAAkE,GAClE,MAAM,iCAAiC,uLAAA,CAAA,UAAgB;IACnD,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,gBAAgB,GAAG;IAC5B;IACA,0BAA0B,QAAQ,EAAE,UAAU,EAAE;QAC5C,MAAM,SAAS,SAAS,IAAI;QAC5B,IAAI,CAAC,UAAU,GAAG,SAAS,SAAS;QACpC,IAAI,YAAY;YACZ,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI;YACvB,sGAAsG;YACtG,IAAI,CAAC,gBAAgB,GAAG,OAAO,MAAM,KAAK;QAC9C;IACJ;IACA,mBAAmB,UAAU,EAAE;QAC3B,MAAM,WAAW,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC,MAAM;QACxE,OAAO;YACH,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW;YACrC,QAAQ,CAAC,WAAW,OAAO,EAAE,EAAE,QAAQ;QAC3C;IACJ;IACA,yBAAyB,MAAM,EAAE;QAC7B,OAAO,OAAO,IAAI,CAAC,MAAM;IAC7B;IACA,gBAAgB,MAAM,EAAE;QACpB,OAAO,CAAC,OAAO,IAAI,CAAC,MAAM;IAC9B;IACA,iBAAiB,MAAM,EAAE;QACrB,OAAO,OAAO,MAAM,GAAG;IAC3B;IACA,eAAe;QACX,OAAO,IAAI,CAAC,MAAM;IACtB;IACA;;KAEC,GACD,IAAI,SAAS;QACT,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,IAAI,OAAO;QACP,OAAO,KAAK,CAAC,QAAQ,IAAI,CAAC,gBAAgB;IAC9C;AACJ;AAGO,MAAM,gCAAgC;IACzC,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AAEO,MAAM,mCAAmC;IAC5C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AAEO,MAAM,gCAAgC;IACzC,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AAEO,MAAM,gCAAgC;IACzC,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AAEO,MAAM,iCAAiC;IAC1C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2877, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/paginators/mutes.paginator.v1.js"], "sourcesContent": ["import { CursoredV1Paginator } from './paginator.v1';\nexport class MuteUserListV1Paginator extends CursoredV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'mutes/users/list.json';\n    }\n    refreshInstanceFromResult(response, isNextPage) {\n        const result = response.data;\n        this._rateLimit = response.rateLimit;\n        if (isNextPage) {\n            this._realData.users.push(...result.users);\n            this._realData.next_cursor = result.next_cursor;\n        }\n    }\n    getPageLengthFromRequest(result) {\n        return result.data.users.length;\n    }\n    getItemArray() {\n        return this.users;\n    }\n    /**\n     * Users returned by paginator.\n     */\n    get users() {\n        return this._realData.users;\n    }\n}\nexport class MuteUserIdsV1Paginator extends CursoredV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'mutes/users/ids.json';\n        this._maxResultsWhenFetchLast = 5000;\n    }\n    refreshInstanceFromResult(response, isNextPage) {\n        const result = response.data;\n        this._rateLimit = response.rateLimit;\n        if (isNextPage) {\n            this._realData.ids.push(...result.ids);\n            this._realData.next_cursor = result.next_cursor;\n        }\n    }\n    getPageLengthFromRequest(result) {\n        return result.data.ids.length;\n    }\n    getItemArray() {\n        return this.ids;\n    }\n    /**\n     * Users IDs returned by paginator.\n     */\n    get ids() {\n        return this._realData.ids;\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gCAAgC,sLAAA,CAAA,sBAAmB;IAC5D,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,0BAA0B,QAAQ,EAAE,UAAU,EAAE;QAC5C,MAAM,SAAS,SAAS,IAAI;QAC5B,IAAI,CAAC,UAAU,GAAG,SAAS,SAAS;QACpC,IAAI,YAAY;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,OAAO,KAAK;YACzC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,WAAW;QACnD;IACJ;IACA,yBAAyB,MAAM,EAAE;QAC7B,OAAO,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;IACnC;IACA,eAAe;QACX,OAAO,IAAI,CAAC,KAAK;IACrB;IACA;;KAEC,GACD,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK;IAC/B;AACJ;AACO,MAAM,+BAA+B,sLAAA,CAAA,sBAAmB;IAC3D,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,wBAAwB,GAAG;IACpC;IACA,0BAA0B,QAAQ,EAAE,UAAU,EAAE;QAC5C,MAAM,SAAS,SAAS,IAAI;QAC5B,IAAI,CAAC,UAAU,GAAG,SAAS,SAAS;QACpC,IAAI,YAAY;YACZ,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,IAAI,OAAO,GAAG;YACrC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,WAAW;QACnD;IACJ;IACA,yBAAyB,MAAM,EAAE;QAC7B,OAAO,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM;IACjC;IACA,eAAe;QACX,OAAO,IAAI,CAAC,GAAG;IACnB;IACA;;KAEC,GACD,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG;IAC7B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2940, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/paginators/followers.paginator.v1.js"], "sourcesContent": ["import { CursoredV1Paginator } from './paginator.v1';\nexport class UserFollowerListV1Paginator extends CursoredV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'followers/list.json';\n    }\n    refreshInstanceFromResult(response, isNextPage) {\n        const result = response.data;\n        this._rateLimit = response.rateLimit;\n        if (isNextPage) {\n            this._realData.users.push(...result.users);\n            this._realData.next_cursor = result.next_cursor;\n        }\n    }\n    getPageLengthFromRequest(result) {\n        return result.data.users.length;\n    }\n    getItemArray() {\n        return this.users;\n    }\n    /**\n     * Users returned by paginator.\n     */\n    get users() {\n        return this._realData.users;\n    }\n}\nexport class UserFollowerIdsV1Paginator extends CursoredV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'followers/ids.json';\n        this._maxResultsWhenFetchLast = 5000;\n    }\n    refreshInstanceFromResult(response, isNextPage) {\n        const result = response.data;\n        this._rateLimit = response.rateLimit;\n        if (isNextPage) {\n            this._realData.ids.push(...result.ids);\n            this._realData.next_cursor = result.next_cursor;\n        }\n    }\n    getPageLengthFromRequest(result) {\n        return result.data.ids.length;\n    }\n    getItemArray() {\n        return this.ids;\n    }\n    /**\n     * Users IDs returned by paginator.\n     */\n    get ids() {\n        return this._realData.ids;\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,oCAAoC,sLAAA,CAAA,sBAAmB;IAChE,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,0BAA0B,QAAQ,EAAE,UAAU,EAAE;QAC5C,MAAM,SAAS,SAAS,IAAI;QAC5B,IAAI,CAAC,UAAU,GAAG,SAAS,SAAS;QACpC,IAAI,YAAY;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,OAAO,KAAK;YACzC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,WAAW;QACnD;IACJ;IACA,yBAAyB,MAAM,EAAE;QAC7B,OAAO,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;IACnC;IACA,eAAe;QACX,OAAO,IAAI,CAAC,KAAK;IACrB;IACA;;KAEC,GACD,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK;IAC/B;AACJ;AACO,MAAM,mCAAmC,sLAAA,CAAA,sBAAmB;IAC/D,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,wBAAwB,GAAG;IACpC;IACA,0BAA0B,QAAQ,EAAE,UAAU,EAAE;QAC5C,MAAM,SAAS,SAAS,IAAI;QAC5B,IAAI,CAAC,UAAU,GAAG,SAAS,SAAS;QACpC,IAAI,YAAY;YACZ,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,IAAI,OAAO,GAAG;YACrC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,WAAW;QACnD;IACJ;IACA,yBAAyB,MAAM,EAAE;QAC7B,OAAO,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM;IACjC;IACA,eAAe;QACX,OAAO,IAAI,CAAC,GAAG;IACnB;IACA;;KAEC,GACD,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG;IAC7B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3003, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/paginators/friends.paginator.v1.js"], "sourcesContent": ["import { CursoredV1Paginator } from './paginator.v1';\nexport class UserFriendListV1Paginator extends CursoredV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'friends/list.json';\n    }\n    refreshInstanceFromResult(response, isNextPage) {\n        const result = response.data;\n        this._rateLimit = response.rateLimit;\n        if (isNextPage) {\n            this._realData.users.push(...result.users);\n            this._realData.next_cursor = result.next_cursor;\n        }\n    }\n    getPageLengthFromRequest(result) {\n        return result.data.users.length;\n    }\n    getItemArray() {\n        return this.users;\n    }\n    /**\n     * Users returned by paginator.\n     */\n    get users() {\n        return this._realData.users;\n    }\n}\nexport class UserFollowersIdsV1Paginator extends CursoredV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'friends/ids.json';\n        this._maxResultsWhenFetchLast = 5000;\n    }\n    refreshInstanceFromResult(response, isNextPage) {\n        const result = response.data;\n        this._rateLimit = response.rateLimit;\n        if (isNextPage) {\n            this._realData.ids.push(...result.ids);\n            this._realData.next_cursor = result.next_cursor;\n        }\n    }\n    getPageLengthFromRequest(result) {\n        return result.data.ids.length;\n    }\n    getItemArray() {\n        return this.ids;\n    }\n    /**\n     * Users IDs returned by paginator.\n     */\n    get ids() {\n        return this._realData.ids;\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,kCAAkC,sLAAA,CAAA,sBAAmB;IAC9D,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,0BAA0B,QAAQ,EAAE,UAAU,EAAE;QAC5C,MAAM,SAAS,SAAS,IAAI;QAC5B,IAAI,CAAC,UAAU,GAAG,SAAS,SAAS;QACpC,IAAI,YAAY;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,OAAO,KAAK;YACzC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,WAAW;QACnD;IACJ;IACA,yBAAyB,MAAM,EAAE;QAC7B,OAAO,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;IACnC;IACA,eAAe;QACX,OAAO,IAAI,CAAC,KAAK;IACrB;IACA;;KAEC,GACD,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK;IAC/B;AACJ;AACO,MAAM,oCAAoC,sLAAA,CAAA,sBAAmB;IAChE,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,wBAAwB,GAAG;IACpC;IACA,0BAA0B,QAAQ,EAAE,UAAU,EAAE;QAC5C,MAAM,SAAS,SAAS,IAAI;QAC5B,IAAI,CAAC,UAAU,GAAG,SAAS,SAAS;QACpC,IAAI,YAAY;YACZ,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,IAAI,OAAO,GAAG;YACrC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,WAAW;QACnD;IACJ;IACA,yBAAyB,MAAM,EAAE;QAC7B,OAAO,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM;IACjC;IACA,eAAe;QACX,OAAO,IAAI,CAAC,GAAG;IACnB;IACA;;KAEC,GACD,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG;IAC7B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3066, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/paginators/user.paginator.v1.js"], "sourcesContent": ["import TwitterPaginator from './TwitterPaginator';\nimport { CursoredV1Paginator } from './paginator.v1';\n/** A generic TwitterPaginator able to consume TweetV1 timelines. */\nexport class UserSearchV1Paginator extends TwitterPaginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'users/search.json';\n    }\n    refreshInstanceFromResult(response, isNextPage) {\n        const result = response.data;\n        this._rateLimit = response.rateLimit;\n        if (isNextPage) {\n            this._realData.push(...result);\n        }\n    }\n    getNextQueryParams(maxResults) {\n        var _a;\n        const previousPage = Number((_a = this._queryParams.page) !== null && _a !== void 0 ? _a : '1');\n        return {\n            ...this._queryParams,\n            page: previousPage + 1,\n            ...maxResults ? { count: maxResults } : {},\n        };\n    }\n    getPageLengthFromRequest(result) {\n        return result.data.length;\n    }\n    isFetchLastOver(result) {\n        return !result.data.length;\n    }\n    canFetchNextPage(result) {\n        return result.length > 0;\n    }\n    getItemArray() {\n        return this.users;\n    }\n    /**\n     * Users returned by paginator.\n     */\n    get users() {\n        return this._realData;\n    }\n}\nexport class FriendshipsIncomingV1Paginator extends CursoredV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'friendships/incoming.json';\n        this._maxResultsWhenFetchLast = 5000;\n    }\n    refreshInstanceFromResult(response, isNextPage) {\n        const result = response.data;\n        this._rateLimit = response.rateLimit;\n        if (isNextPage) {\n            this._realData.ids.push(...result.ids);\n            this._realData.next_cursor = result.next_cursor;\n        }\n    }\n    getPageLengthFromRequest(result) {\n        return result.data.ids.length;\n    }\n    getItemArray() {\n        return this.ids;\n    }\n    /**\n     * Users IDs returned by paginator.\n     */\n    get ids() {\n        return this._realData.ids;\n    }\n}\nexport class FriendshipsOutgoingV1Paginator extends FriendshipsIncomingV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'friendships/outgoing.json';\n    }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,MAAM,8BAA8B,uLAAA,CAAA,UAAgB;IACvD,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,0BAA0B,QAAQ,EAAE,UAAU,EAAE;QAC5C,MAAM,SAAS,SAAS,IAAI;QAC5B,IAAI,CAAC,UAAU,GAAG,SAAS,SAAS;QACpC,IAAI,YAAY;YACZ,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI;QAC3B;IACJ;IACA,mBAAmB,UAAU,EAAE;QAC3B,IAAI;QACJ,MAAM,eAAe,OAAO,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAC3F,OAAO;YACH,GAAG,IAAI,CAAC,YAAY;YACpB,MAAM,eAAe;YACrB,GAAG,aAAa;gBAAE,OAAO;YAAW,IAAI,CAAC,CAAC;QAC9C;IACJ;IACA,yBAAyB,MAAM,EAAE;QAC7B,OAAO,OAAO,IAAI,CAAC,MAAM;IAC7B;IACA,gBAAgB,MAAM,EAAE;QACpB,OAAO,CAAC,OAAO,IAAI,CAAC,MAAM;IAC9B;IACA,iBAAiB,MAAM,EAAE;QACrB,OAAO,OAAO,MAAM,GAAG;IAC3B;IACA,eAAe;QACX,OAAO,IAAI,CAAC,KAAK;IACrB;IACA;;KAEC,GACD,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,SAAS;IACzB;AACJ;AACO,MAAM,uCAAuC,sLAAA,CAAA,sBAAmB;IACnE,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,wBAAwB,GAAG;IACpC;IACA,0BAA0B,QAAQ,EAAE,UAAU,EAAE;QAC5C,MAAM,SAAS,SAAS,IAAI;QAC5B,IAAI,CAAC,UAAU,GAAG,SAAS,SAAS;QACpC,IAAI,YAAY;YACZ,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,IAAI,OAAO,GAAG;YACrC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,WAAW;QACnD;IACJ;IACA,yBAAyB,MAAM,EAAE;QAC7B,OAAO,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM;IACjC;IACA,eAAe;QACX,OAAO,IAAI,CAAC,GAAG;IACnB;IACA;;KAEC,GACD,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG;IAC7B;AACJ;AACO,MAAM,uCAAuC;IAChD,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/paginators/list.paginator.v1.js"], "sourcesContent": ["import { CursoredV1Paginator } from './paginator.v1';\nclass ListListsV1Paginator extends CursoredV1Paginator {\n    refreshInstanceFromResult(response, isNextPage) {\n        const result = response.data;\n        this._rateLimit = response.rateLimit;\n        if (isNextPage) {\n            this._realData.lists.push(...result.lists);\n            this._realData.next_cursor = result.next_cursor;\n        }\n    }\n    getPageLengthFromRequest(result) {\n        return result.data.lists.length;\n    }\n    getItemArray() {\n        return this.lists;\n    }\n    /**\n     * Lists returned by paginator.\n     */\n    get lists() {\n        return this._realData.lists;\n    }\n}\nexport class ListMembershipsV1Paginator extends ListListsV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'lists/memberships.json';\n    }\n}\nexport class ListOwnershipsV1Paginator extends ListListsV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'lists/ownerships.json';\n    }\n}\nexport class ListSubscriptionsV1Paginator extends ListListsV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'lists/subscriptions.json';\n    }\n}\nclass ListUsersV1Paginator extends CursoredV1Paginator {\n    refreshInstanceFromResult(response, isNextPage) {\n        const result = response.data;\n        this._rateLimit = response.rateLimit;\n        if (isNextPage) {\n            this._realData.users.push(...result.users);\n            this._realData.next_cursor = result.next_cursor;\n        }\n    }\n    getPageLengthFromRequest(result) {\n        return result.data.users.length;\n    }\n    getItemArray() {\n        return this.users;\n    }\n    /**\n     * Users returned by paginator.\n     */\n    get users() {\n        return this._realData.users;\n    }\n}\nexport class ListMembersV1Paginator extends ListUsersV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'lists/members.json';\n    }\n}\nexport class ListSubscribersV1Paginator extends ListUsersV1Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'lists/subscribers.json';\n    }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACA,MAAM,6BAA6B,sLAAA,CAAA,sBAAmB;IAClD,0BAA0B,QAAQ,EAAE,UAAU,EAAE;QAC5C,MAAM,SAAS,SAAS,IAAI;QAC5B,IAAI,CAAC,UAAU,GAAG,SAAS,SAAS;QACpC,IAAI,YAAY;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,OAAO,KAAK;YACzC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,WAAW;QACnD;IACJ;IACA,yBAAyB,MAAM,EAAE;QAC7B,OAAO,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;IACnC;IACA,eAAe;QACX,OAAO,IAAI,CAAC,KAAK;IACrB;IACA;;KAEC,GACD,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK;IAC/B;AACJ;AACO,MAAM,mCAAmC;IAC5C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,kCAAkC;IAC3C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,qCAAqC;IAC9C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACA,MAAM,6BAA6B,sLAAA,CAAA,sBAAmB;IAClD,0BAA0B,QAAQ,EAAE,UAAU,EAAE;QAC5C,MAAM,SAAS,SAAS,IAAI;QAC5B,IAAI,CAAC,UAAU,GAAG,SAAS,SAAS;QACpC,IAAI,YAAY;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,OAAO,KAAK;YACzC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,WAAW;QACnD;IACJ;IACA,yBAAyB,MAAM,EAAE;QAC7B,OAAO,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;IACnC;IACA,eAAe;QACX,OAAO,IAAI,CAAC,KAAK;IACrB;IACA;;KAEC,GACD,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK;IAC/B;AACJ;AACO,MAAM,+BAA+B;IACxC,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,mCAAmC;IAC5C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3241, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/v1/client.v1.read.js"], "sourcesContent": ["import TwitterApiSubClient from '../client.subclient';\nimport { API_V1_1_PREFIX, API_V1_1_STREAM_PREFIX, API_V1_1_UPLOAD_PREFIX } from '../globals';\nimport { arrayWrap } from '../helpers';\nimport TwitterApiv1 from '../v1/client.v1';\nimport { HomeTimelineV1Paginator, ListTimelineV1Paginator, MentionTimelineV1Paginator, UserFavoritesV1Paginator, UserTimelineV1Paginator } from '../paginators/tweet.paginator.v1';\nimport { MuteUserIdsV1Paginator, MuteUserListV1Paginator } from '../paginators/mutes.paginator.v1';\nimport { UserFollowerIdsV1Paginator, UserFollowerListV1Paginator } from '../paginators/followers.paginator.v1';\nimport { UserFollowersIdsV1Paginator, UserFriendListV1Paginator } from '../paginators/friends.paginator.v1';\nimport { FriendshipsIncomingV1Paginator, FriendshipsOutgoingV1Paginator, UserSearchV1Paginator } from '../paginators/user.paginator.v1';\nimport { ListMembershipsV1Paginator, ListMembersV1Paginator, ListOwnershipsV1Paginator, ListSubscribersV1Paginator, ListSubscriptionsV1Paginator } from '../paginators/list.paginator.v1';\n/**\n * Base Twitter v1 client with only read right.\n */\nexport default class TwitterApiv1ReadOnly extends TwitterApiSubClient {\n    constructor() {\n        super(...arguments);\n        this._prefix = API_V1_1_PREFIX;\n    }\n    /* Tweets */\n    /**\n     * Returns a single Tweet, specified by the id parameter. The Tweet's author will also be embedded within the Tweet.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/tweets/post-and-engage/api-reference/get-statuses-show-id\n     */\n    singleTweet(tweetId, options = {}) {\n        return this.get('statuses/show.json', { tweet_mode: 'extended', id: tweetId, ...options });\n    }\n    tweets(ids, options = {}) {\n        return this.post('statuses/lookup.json', { tweet_mode: 'extended', id: ids, ...options });\n    }\n    /**\n     * Returns a single Tweet, specified by either a Tweet web URL or the Tweet ID, in an oEmbed-compatible format.\n     * The returned HTML snippet will be automatically recognized as an Embedded Tweet when Twitter's widget JavaScript is included on the page.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/tweets/post-and-engage/api-reference/get-statuses-oembed\n     */\n    oembedTweet(tweetId, options = {}) {\n        return this.get('oembed', {\n            url: `https://twitter.com/i/statuses/${tweetId}`,\n            ...options,\n        }, { prefix: 'https://publish.twitter.com/' });\n    }\n    /* Tweets timelines */\n    /**\n     * Returns a collection of the most recent Tweets and Retweets posted by the authenticating user and the users they follow.\n     * The home timeline is central to how most users interact with the Twitter service.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/tweets/timelines/api-reference/get-statuses-home_timeline\n     */\n    async homeTimeline(options = {}) {\n        const queryParams = {\n            tweet_mode: 'extended',\n            ...options,\n        };\n        const initialRq = await this.get('statuses/home_timeline.json', queryParams, { fullResponse: true });\n        return new HomeTimelineV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * Returns the 20 most recent mentions (Tweets containing a users's @screen_name) for the authenticating user.\n     * The timeline returned is the equivalent of the one seen when you view your mentions on twitter.com.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/tweets/timelines/api-reference/get-statuses-mentions_timeline\n     */\n    async mentionTimeline(options = {}) {\n        const queryParams = {\n            tweet_mode: 'extended',\n            ...options,\n        };\n        const initialRq = await this.get('statuses/mentions_timeline.json', queryParams, { fullResponse: true });\n        return new MentionTimelineV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * Returns a collection of the most recent Tweets posted by the user indicated by the user_id parameters.\n     * User timelines belonging to protected users may only be requested when the authenticated user either \"owns\" the timeline or is an approved follower of the owner.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/tweets/timelines/api-reference/get-statuses-user_timeline\n     */\n    async userTimeline(userId, options = {}) {\n        const queryParams = {\n            tweet_mode: 'extended',\n            user_id: userId,\n            ...options,\n        };\n        const initialRq = await this.get('statuses/user_timeline.json', queryParams, { fullResponse: true });\n        return new UserTimelineV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * Returns a collection of the most recent Tweets posted by the user indicated by the screen_name parameters.\n     * User timelines belonging to protected users may only be requested when the authenticated user either \"owns\" the timeline or is an approved follower of the owner.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/tweets/timelines/api-reference/get-statuses-user_timeline\n     */\n    async userTimelineByUsername(username, options = {}) {\n        const queryParams = {\n            tweet_mode: 'extended',\n            screen_name: username,\n            ...options,\n        };\n        const initialRq = await this.get('statuses/user_timeline.json', queryParams, { fullResponse: true });\n        return new UserTimelineV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * Returns the most recent Tweets liked by the authenticating or specified user, 20 tweets by default.\n     * Note: favorites are now known as likes.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/tweets/post-and-engage/api-reference/get-favorites-list\n     */\n    async favoriteTimeline(userId, options = {}) {\n        const queryParams = {\n            tweet_mode: 'extended',\n            user_id: userId,\n            ...options,\n        };\n        const initialRq = await this.get('favorites/list.json', queryParams, { fullResponse: true });\n        return new UserFavoritesV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * Returns the most recent Tweets liked by the authenticating or specified user, 20 tweets by default.\n     * Note: favorites are now known as likes.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/tweets/post-and-engage/api-reference/get-favorites-list\n     */\n    async favoriteTimelineByUsername(username, options = {}) {\n        const queryParams = {\n            tweet_mode: 'extended',\n            screen_name: username,\n            ...options,\n        };\n        const initialRq = await this.get('favorites/list.json', queryParams, { fullResponse: true });\n        return new UserFavoritesV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /* Users */\n    /**\n     * Returns a variety of information about the user specified by the required user_id or screen_name parameter.\n     * The author's most recent Tweet will be returned inline when possible.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/follow-search-get-users/api-reference/get-users-show\n     */\n    user(user) {\n        return this.get('users/show.json', { tweet_mode: 'extended', ...user });\n    }\n    /**\n     * Returns fully-hydrated user objects for up to 100 users per request,\n     * as specified by comma-separated values passed to the user_id and/or screen_name parameters.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/follow-search-get-users/api-reference/get-users-lookup\n     */\n    users(query) {\n        return this.get('users/lookup.json', { tweet_mode: 'extended', ...query });\n    }\n    /**\n     * Returns an HTTP 200 OK response code and a representation of the requesting user if authentication was successful;\n     * returns a 401 status code and an error message if not.\n     * Use this method to test if supplied user credentials are valid.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/manage-account-settings/api-reference/get-account-verify_credentials\n     */\n    verifyCredentials(options = {}) {\n        return this.get('account/verify_credentials.json', options);\n    }\n    /**\n     * Returns an array of user objects the authenticating user has muted.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/mute-block-report-users/api-reference/get-mutes-users-list\n     */\n    async listMutedUsers(options = {}) {\n        const queryParams = {\n            tweet_mode: 'extended',\n            ...options,\n        };\n        const initialRq = await this.get('mutes/users/list.json', queryParams, { fullResponse: true });\n        return new MuteUserListV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * Returns an array of numeric user ids the authenticating user has muted.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/mute-block-report-users/api-reference/get-mutes-users-ids\n     */\n    async listMutedUserIds(options = {}) {\n        const queryParams = {\n            stringify_ids: true,\n            ...options,\n        };\n        const initialRq = await this.get('mutes/users/ids.json', queryParams, { fullResponse: true });\n        return new MuteUserIdsV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * Returns an array of user objects of friends of the specified user.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/follow-search-get-users/api-reference/get-friends-list\n     */\n    async userFriendList(options = {}) {\n        const queryParams = {\n            ...options,\n        };\n        const initialRq = await this.get('friends/list.json', queryParams, { fullResponse: true });\n        return new UserFriendListV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * Returns an array of user objects of followers of the specified user.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/follow-search-get-users/api-reference/get-followers-list\n     */\n    async userFollowerList(options = {}) {\n        const queryParams = {\n            ...options,\n        };\n        const initialRq = await this.get('followers/list.json', queryParams, { fullResponse: true });\n        return new UserFollowerListV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * Returns an array of numeric user ids of followers of the specified user.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/follow-search-get-users/api-reference/get-followers-ids\n     */\n    async userFollowerIds(options = {}) {\n        const queryParams = {\n            stringify_ids: true,\n            ...options,\n        };\n        const initialRq = await this.get('followers/ids.json', queryParams, { fullResponse: true });\n        return new UserFollowerIdsV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * Returns an array of numeric user ids of friends of the specified user.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/follow-search-get-users/api-reference/get-friends-ids\n     */\n    async userFollowingIds(options = {}) {\n        const queryParams = {\n            stringify_ids: true,\n            ...options,\n        };\n        const initialRq = await this.get('friends/ids.json', queryParams, { fullResponse: true });\n        return new UserFollowersIdsV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * Provides a simple, relevance-based search interface to public user accounts on Twitter.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/follow-search-get-users/api-reference/get-users-search\n     */\n    async searchUsers(query, options = {}) {\n        const queryParams = {\n            q: query,\n            tweet_mode: 'extended',\n            page: 1,\n            ...options,\n        };\n        const initialRq = await this.get('users/search.json', queryParams, { fullResponse: true });\n        return new UserSearchV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /* Friendship API */\n    /**\n     * Returns detailed information about the relationship between two arbitrary users.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/follow-search-get-users/api-reference/get-friendships-show\n     */\n    friendship(sources) {\n        return this.get('friendships/show.json', sources);\n    }\n    /**\n     * Returns the relationships of the authenticating user to the comma-separated list of up to 100 screen_names or user_ids provided.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/follow-search-get-users/api-reference/get-friendships-lookup\n     */\n    friendships(friendships) {\n        return this.get('friendships/lookup.json', friendships);\n    }\n    /**\n     * Returns a collection of user_ids that the currently authenticated user does not want to receive retweets from.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/follow-search-get-users/api-reference/get-friendships-no_retweets-ids\n     */\n    friendshipsNoRetweets() {\n        return this.get('friendships/no_retweets/ids.json', { stringify_ids: true });\n    }\n    /**\n     * Returns a collection of numeric IDs for every user who has a pending request to follow the authenticating user.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/follow-search-get-users/api-reference/get-friendships-incoming\n     */\n    async friendshipsIncoming(options = {}) {\n        const queryParams = {\n            stringify_ids: true,\n            ...options,\n        };\n        const initialRq = await this.get('friendships/incoming.json', queryParams, { fullResponse: true });\n        return new FriendshipsIncomingV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * Returns a collection of numeric IDs for every protected user for whom the authenticating user has a pending follow request.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/follow-search-get-users/api-reference/get-friendships-outgoing\n     */\n    async friendshipsOutgoing(options = {}) {\n        const queryParams = {\n            stringify_ids: true,\n            ...options,\n        };\n        const initialRq = await this.get('friendships/outgoing.json', queryParams, { fullResponse: true });\n        return new FriendshipsOutgoingV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /* Account/user API */\n    /**\n     * Get current account settings for authenticating user.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/manage-account-settings/api-reference/get-account-settings\n     */\n    accountSettings() {\n        return this.get('account/settings.json');\n    }\n    /**\n     * Returns a map of the available size variations of the specified user's profile banner.\n     * If the user has not uploaded a profile banner, a HTTP 404 will be served instead.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/manage-account-settings/api-reference/get-users-profile_banner\n     */\n    userProfileBannerSizes(params) {\n        return this.get('users/profile_banner.json', params);\n    }\n    /* Lists */\n    /**\n     * Returns the specified list. Private lists will only be shown if the authenticated user owns the specified list.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/create-manage-lists/api-reference/get-lists-show\n     */\n    list(options) {\n        return this.get('lists/show.json', { tweet_mode: 'extended', ...options });\n    }\n    /**\n     * Returns all lists the authenticating or specified user subscribes to, including their own.\n     * If no user is given, the authenticating user is used.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/create-manage-lists/api-reference/get-lists-list\n     */\n    lists(options = {}) {\n        return this.get('lists/list.json', { tweet_mode: 'extended', ...options });\n    }\n    /**\n     * Returns the members of the specified list. Private list members will only be shown if the authenticated user owns the specified list.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/create-manage-lists/api-reference/get-lists-members\n     */\n    async listMembers(options = {}) {\n        const queryParams = {\n            tweet_mode: 'extended',\n            ...options,\n        };\n        const initialRq = await this.get('lists/members.json', queryParams, { fullResponse: true });\n        return new ListMembersV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * Check if the specified user is a member of the specified list.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/create-manage-lists/api-reference/get-lists-members-show\n     */\n    listGetMember(options) {\n        return this.get('lists/members/show.json', { tweet_mode: 'extended', ...options });\n    }\n    /**\n     * Returns the lists the specified user has been added to.\n     * If user_id or screen_name are not provided, the memberships for the authenticating user are returned.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/create-manage-lists/api-reference/get-lists-memberships\n     */\n    async listMemberships(options = {}) {\n        const queryParams = {\n            tweet_mode: 'extended',\n            ...options,\n        };\n        const initialRq = await this.get('lists/memberships.json', queryParams, { fullResponse: true });\n        return new ListMembershipsV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * Returns the lists owned by the specified Twitter user. Private lists will only be shown if the authenticated user is also the owner of the lists.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/create-manage-lists/api-reference/get-lists-ownerships\n     */\n    async listOwnerships(options = {}) {\n        const queryParams = {\n            tweet_mode: 'extended',\n            ...options,\n        };\n        const initialRq = await this.get('lists/ownerships.json', queryParams, { fullResponse: true });\n        return new ListOwnershipsV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * Returns a timeline of tweets authored by members of the specified list. Retweets are included by default.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/create-manage-lists/api-reference/get-lists-statuses\n     */\n    async listStatuses(options) {\n        const queryParams = {\n            tweet_mode: 'extended',\n            ...options,\n        };\n        const initialRq = await this.get('lists/statuses.json', queryParams, { fullResponse: true });\n        return new ListTimelineV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * Returns the subscribers of the specified list. Private list subscribers will only be shown if the authenticated user owns the specified list.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/create-manage-lists/api-reference/get-lists-subscribers\n     */\n    async listSubscribers(options = {}) {\n        const queryParams = {\n            tweet_mode: 'extended',\n            ...options,\n        };\n        const initialRq = await this.get('lists/subscribers.json', queryParams, { fullResponse: true });\n        return new ListSubscribersV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * Check if the specified user is a subscriber of the specified list. Returns the user if they are a subscriber.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/create-manage-lists/api-reference/get-lists-subscribers-show\n     */\n    listGetSubscriber(options) {\n        return this.get('lists/subscribers/show.json', { tweet_mode: 'extended', ...options });\n    }\n    /**\n     * Obtain a collection of the lists the specified user is subscribed to, 20 lists per page by default.\n     * Does not include the user's own lists.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/create-manage-lists/api-reference/get-lists-subscriptions\n     */\n    async listSubscriptions(options = {}) {\n        const queryParams = {\n            tweet_mode: 'extended',\n            ...options,\n        };\n        const initialRq = await this.get('lists/subscriptions.json', queryParams, { fullResponse: true });\n        return new ListSubscriptionsV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /* Media upload API */\n    /**\n     * The STATUS command (this method) is used to periodically poll for updates of media processing operation.\n     * After the STATUS command response returns succeeded, you can move on to the next step which is usually create Tweet with media_id.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/media/upload-media/api-reference/get-media-upload-status\n     */\n    mediaInfo(mediaId) {\n        return this.get('media/upload.json', {\n            command: 'STATUS',\n            media_id: mediaId,\n        }, { prefix: API_V1_1_UPLOAD_PREFIX });\n    }\n    filterStream({ autoConnect, ...params } = {}) {\n        const parameters = {};\n        for (const [key, value] of Object.entries(params)) {\n            if (key === 'follow' || key === 'track') {\n                parameters[key] = value.toString();\n            }\n            else if (key === 'locations') {\n                const locations = value;\n                parameters.locations = arrayWrap(locations).map(loc => `${loc.lng},${loc.lat}`).join(',');\n            }\n            else {\n                parameters[key] = value;\n            }\n        }\n        const streamClient = this.stream;\n        return streamClient.postStream('statuses/filter.json', parameters, { autoConnect });\n    }\n    sampleStream({ autoConnect, ...params } = {}) {\n        const streamClient = this.stream;\n        return streamClient.getStream('statuses/sample.json', params, { autoConnect });\n    }\n    /**\n     * Create a client that is prefixed with `https//stream.twitter.com` instead of classic API URL.\n     */\n    get stream() {\n        const copiedClient = new TwitterApiv1(this);\n        copiedClient.setPrefix(API_V1_1_STREAM_PREFIX);\n        return copiedClient;\n    }\n    /* Trends API */\n    /**\n     * Returns the top 50 trending topics for a specific id, if trending information is available for it.\n     * Note: The id parameter for this endpoint is the \"where on earth identifier\" or WOEID, which is a legacy identifier created by Yahoo and has been deprecated.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/trends/trends-for-location/api-reference/get-trends-place\n     */\n    trendsByPlace(woeId, options = {}) {\n        return this.get('trends/place.json', { id: woeId, ...options });\n    }\n    /**\n     * Returns the locations that Twitter has trending topic information for.\n     * The response is an array of \"locations\" that encode the location's WOEID\n     * and some other human-readable information such as a canonical name and country the location belongs in.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/trends/locations-with-trending-topics/api-reference/get-trends-available\n     */\n    trendsAvailable() {\n        return this.get('trends/available.json');\n    }\n    /**\n     * Returns the locations that Twitter has trending topic information for, closest to a specified location.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/trends/locations-with-trending-topics/api-reference/get-trends-closest\n     */\n    trendsClosest(lat, long) {\n        return this.get('trends/closest.json', { lat, long });\n    }\n    /* Geo API */\n    /**\n     * Returns all the information about a known place.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/geo/place-information/api-reference/get-geo-id-place_id\n     */\n    geoPlace(placeId) {\n        return this.get('geo/id/:place_id.json', undefined, { params: { place_id: placeId } });\n    }\n    /**\n     * Search for places that can be attached to a Tweet via POST statuses/update.\n     * This request will return a list of all the valid places that can be used as the place_id when updating a status.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/geo/places-near-location/api-reference/get-geo-search\n     */\n    geoSearch(options) {\n        return this.get('geo/search.json', options);\n    }\n    /**\n     * Given a latitude and a longitude, searches for up to 20 places that can be used as a place_id when updating a status.\n     * This request is an informative call and will deliver generalized results about geography.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/geo/places-near-location/api-reference/get-geo-reverse_geocode\n     */\n    geoReverseGeoCode(options) {\n        return this.get('geo/reverse_geocode.json', options);\n    }\n    /* Developer utilities */\n    /**\n     * Returns the current rate limits for methods belonging to the specified resource families.\n     * Each API resource belongs to a \"resource family\" which is indicated in its method documentation.\n     * The method's resource family can be determined from the first component of the path after the resource version.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/developer-utilities/rate-limit-status/api-reference/get-application-rate_limit_status\n     */\n    rateLimitStatuses(...resources) {\n        return this.get('application/rate_limit_status.json', { resources });\n    }\n    /**\n     * Returns the list of languages supported by Twitter along with the language code supported by Twitter.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/developer-utilities/supported-languages/api-reference/get-help-languages\n     */\n    supportedLanguages() {\n        return this.get('help/languages.json');\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAIe,MAAM,6BAA6B,4KAAA,CAAA,UAAmB;IACjE,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,OAAO,GAAG,gKAAA,CAAA,kBAAe;IAClC;IACA,UAAU,GACV;;;KAGC,GACD,YAAY,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE;QAC/B,OAAO,IAAI,CAAC,GAAG,CAAC,sBAAsB;YAAE,YAAY;YAAY,IAAI;YAAS,GAAG,OAAO;QAAC;IAC5F;IACA,OAAO,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE;QACtB,OAAO,IAAI,CAAC,IAAI,CAAC,wBAAwB;YAAE,YAAY;YAAY,IAAI;YAAK,GAAG,OAAO;QAAC;IAC3F;IACA;;;;KAIC,GACD,YAAY,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE;QAC/B,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU;YACtB,KAAK,CAAC,+BAA+B,EAAE,SAAS;YAChD,GAAG,OAAO;QACd,GAAG;YAAE,QAAQ;QAA+B;IAChD;IACA,oBAAoB,GACpB;;;;KAIC,GACD,MAAM,aAAa,UAAU,CAAC,CAAC,EAAE;QAC7B,MAAM,cAAc;YAChB,YAAY;YACZ,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,+BAA+B,aAAa;YAAE,cAAc;QAAK;QAClG,OAAO,IAAI,+LAAA,CAAA,0BAAuB,CAAC;YAC/B,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;;KAIC,GACD,MAAM,gBAAgB,UAAU,CAAC,CAAC,EAAE;QAChC,MAAM,cAAc;YAChB,YAAY;YACZ,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,mCAAmC,aAAa;YAAE,cAAc;QAAK;QACtG,OAAO,IAAI,+LAAA,CAAA,6BAA0B,CAAC;YAClC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;;KAIC,GACD,MAAM,aAAa,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QACrC,MAAM,cAAc;YAChB,YAAY;YACZ,SAAS;YACT,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,+BAA+B,aAAa;YAAE,cAAc;QAAK;QAClG,OAAO,IAAI,+LAAA,CAAA,0BAAuB,CAAC;YAC/B,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;;KAIC,GACD,MAAM,uBAAuB,QAAQ,EAAE,UAAU,CAAC,CAAC,EAAE;QACjD,MAAM,cAAc;YAChB,YAAY;YACZ,aAAa;YACb,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,+BAA+B,aAAa;YAAE,cAAc;QAAK;QAClG,OAAO,IAAI,+LAAA,CAAA,0BAAuB,CAAC;YAC/B,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;;KAIC,GACD,MAAM,iBAAiB,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QACzC,MAAM,cAAc;YAChB,YAAY;YACZ,SAAS;YACT,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,uBAAuB,aAAa;YAAE,cAAc;QAAK;QAC1F,OAAO,IAAI,+LAAA,CAAA,2BAAwB,CAAC;YAChC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;;KAIC,GACD,MAAM,2BAA2B,QAAQ,EAAE,UAAU,CAAC,CAAC,EAAE;QACrD,MAAM,cAAc;YAChB,YAAY;YACZ,aAAa;YACb,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,uBAAuB,aAAa;YAAE,cAAc;QAAK;QAC1F,OAAO,IAAI,+LAAA,CAAA,2BAAwB,CAAC;YAChC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA,SAAS,GACT;;;;KAIC,GACD,KAAK,IAAI,EAAE;QACP,OAAO,IAAI,CAAC,GAAG,CAAC,mBAAmB;YAAE,YAAY;YAAY,GAAG,IAAI;QAAC;IACzE;IACA;;;;KAIC,GACD,MAAM,KAAK,EAAE;QACT,OAAO,IAAI,CAAC,GAAG,CAAC,qBAAqB;YAAE,YAAY;YAAY,GAAG,KAAK;QAAC;IAC5E;IACA;;;;;KAKC,GACD,kBAAkB,UAAU,CAAC,CAAC,EAAE;QAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,mCAAmC;IACvD;IACA;;;KAGC,GACD,MAAM,eAAe,UAAU,CAAC,CAAC,EAAE;QAC/B,MAAM,cAAc;YAChB,YAAY;YACZ,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,yBAAyB,aAAa;YAAE,cAAc;QAAK;QAC5F,OAAO,IAAI,+LAAA,CAAA,0BAAuB,CAAC;YAC/B,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,iBAAiB,UAAU,CAAC,CAAC,EAAE;QACjC,MAAM,cAAc;YAChB,eAAe;YACf,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,wBAAwB,aAAa;YAAE,cAAc;QAAK;QAC3F,OAAO,IAAI,+LAAA,CAAA,yBAAsB,CAAC;YAC9B,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,eAAe,UAAU,CAAC,CAAC,EAAE;QAC/B,MAAM,cAAc;YAChB,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,qBAAqB,aAAa;YAAE,cAAc;QAAK;QACxF,OAAO,IAAI,iMAAA,CAAA,4BAAyB,CAAC;YACjC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,iBAAiB,UAAU,CAAC,CAAC,EAAE;QACjC,MAAM,cAAc;YAChB,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,uBAAuB,aAAa;YAAE,cAAc;QAAK;QAC1F,OAAO,IAAI,mMAAA,CAAA,8BAA2B,CAAC;YACnC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,gBAAgB,UAAU,CAAC,CAAC,EAAE;QAChC,MAAM,cAAc;YAChB,eAAe;YACf,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,sBAAsB,aAAa;YAAE,cAAc;QAAK;QACzF,OAAO,IAAI,mMAAA,CAAA,6BAA0B,CAAC;YAClC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,iBAAiB,UAAU,CAAC,CAAC,EAAE;QACjC,MAAM,cAAc;YAChB,eAAe;YACf,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,oBAAoB,aAAa;YAAE,cAAc;QAAK;QACvF,OAAO,IAAI,iMAAA,CAAA,8BAA2B,CAAC;YACnC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,YAAY,KAAK,EAAE,UAAU,CAAC,CAAC,EAAE;QACnC,MAAM,cAAc;YAChB,GAAG;YACH,YAAY;YACZ,MAAM;YACN,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,qBAAqB,aAAa;YAAE,cAAc;QAAK;QACxF,OAAO,IAAI,8LAAA,CAAA,wBAAqB,CAAC;YAC7B,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA,kBAAkB,GAClB;;;KAGC,GACD,WAAW,OAAO,EAAE;QAChB,OAAO,IAAI,CAAC,GAAG,CAAC,yBAAyB;IAC7C;IACA;;;KAGC,GACD,YAAY,WAAW,EAAE;QACrB,OAAO,IAAI,CAAC,GAAG,CAAC,2BAA2B;IAC/C;IACA;;;KAGC,GACD,wBAAwB;QACpB,OAAO,IAAI,CAAC,GAAG,CAAC,oCAAoC;YAAE,eAAe;QAAK;IAC9E;IACA;;;KAGC,GACD,MAAM,oBAAoB,UAAU,CAAC,CAAC,EAAE;QACpC,MAAM,cAAc;YAChB,eAAe;YACf,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,6BAA6B,aAAa;YAAE,cAAc;QAAK;QAChG,OAAO,IAAI,8LAAA,CAAA,iCAA8B,CAAC;YACtC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,oBAAoB,UAAU,CAAC,CAAC,EAAE;QACpC,MAAM,cAAc;YAChB,eAAe;YACf,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,6BAA6B,aAAa;YAAE,cAAc;QAAK;QAChG,OAAO,IAAI,8LAAA,CAAA,iCAA8B,CAAC;YACtC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA,oBAAoB,GACpB;;;KAGC,GACD,kBAAkB;QACd,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB;IACA;;;;KAIC,GACD,uBAAuB,MAAM,EAAE;QAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,6BAA6B;IACjD;IACA,SAAS,GACT;;;KAGC,GACD,KAAK,OAAO,EAAE;QACV,OAAO,IAAI,CAAC,GAAG,CAAC,mBAAmB;YAAE,YAAY;YAAY,GAAG,OAAO;QAAC;IAC5E;IACA;;;;KAIC,GACD,MAAM,UAAU,CAAC,CAAC,EAAE;QAChB,OAAO,IAAI,CAAC,GAAG,CAAC,mBAAmB;YAAE,YAAY;YAAY,GAAG,OAAO;QAAC;IAC5E;IACA;;;KAGC,GACD,MAAM,YAAY,UAAU,CAAC,CAAC,EAAE;QAC5B,MAAM,cAAc;YAChB,YAAY;YACZ,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,sBAAsB,aAAa;YAAE,cAAc;QAAK;QACzF,OAAO,IAAI,8LAAA,CAAA,yBAAsB,CAAC;YAC9B,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;KAGC,GACD,cAAc,OAAO,EAAE;QACnB,OAAO,IAAI,CAAC,GAAG,CAAC,2BAA2B;YAAE,YAAY;YAAY,GAAG,OAAO;QAAC;IACpF;IACA;;;;KAIC,GACD,MAAM,gBAAgB,UAAU,CAAC,CAAC,EAAE;QAChC,MAAM,cAAc;YAChB,YAAY;YACZ,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,0BAA0B,aAAa;YAAE,cAAc;QAAK;QAC7F,OAAO,IAAI,8LAAA,CAAA,6BAA0B,CAAC;YAClC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,eAAe,UAAU,CAAC,CAAC,EAAE;QAC/B,MAAM,cAAc;YAChB,YAAY;YACZ,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,yBAAyB,aAAa;YAAE,cAAc;QAAK;QAC5F,OAAO,IAAI,8LAAA,CAAA,4BAAyB,CAAC;YACjC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,aAAa,OAAO,EAAE;QACxB,MAAM,cAAc;YAChB,YAAY;YACZ,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,uBAAuB,aAAa;YAAE,cAAc;QAAK;QAC1F,OAAO,IAAI,+LAAA,CAAA,0BAAuB,CAAC;YAC/B,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,gBAAgB,UAAU,CAAC,CAAC,EAAE;QAChC,MAAM,cAAc;YAChB,YAAY;YACZ,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,0BAA0B,aAAa;YAAE,cAAc;QAAK;QAC7F,OAAO,IAAI,8LAAA,CAAA,6BAA0B,CAAC;YAClC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;KAGC,GACD,kBAAkB,OAAO,EAAE;QACvB,OAAO,IAAI,CAAC,GAAG,CAAC,+BAA+B;YAAE,YAAY;YAAY,GAAG,OAAO;QAAC;IACxF;IACA;;;;KAIC,GACD,MAAM,kBAAkB,UAAU,CAAC,CAAC,EAAE;QAClC,MAAM,cAAc;YAChB,YAAY;YACZ,GAAG,OAAO;QACd;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,4BAA4B,aAAa;YAAE,cAAc;QAAK;QAC/F,OAAO,IAAI,8LAAA,CAAA,+BAA4B,CAAC;YACpC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA,oBAAoB,GACpB;;;;KAIC,GACD,UAAU,OAAO,EAAE;QACf,OAAO,IAAI,CAAC,GAAG,CAAC,qBAAqB;YACjC,SAAS;YACT,UAAU;QACd,GAAG;YAAE,QAAQ,gKAAA,CAAA,yBAAsB;QAAC;IACxC;IACA,aAAa,EAAE,WAAW,EAAE,GAAG,QAAQ,GAAG,CAAC,CAAC,EAAE;QAC1C,MAAM,aAAa,CAAC;QACpB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;YAC/C,IAAI,QAAQ,YAAY,QAAQ,SAAS;gBACrC,UAAU,CAAC,IAAI,GAAG,MAAM,QAAQ;YACpC,OACK,IAAI,QAAQ,aAAa;gBAC1B,MAAM,YAAY;gBAClB,WAAW,SAAS,GAAG,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE,WAAW,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE,EAAE,IAAI,CAAC;YACzF,OACK;gBACD,UAAU,CAAC,IAAI,GAAG;YACtB;QACJ;QACA,MAAM,eAAe,IAAI,CAAC,MAAM;QAChC,OAAO,aAAa,UAAU,CAAC,wBAAwB,YAAY;YAAE;QAAY;IACrF;IACA,aAAa,EAAE,WAAW,EAAE,GAAG,QAAQ,GAAG,CAAC,CAAC,EAAE;QAC1C,MAAM,eAAe,IAAI,CAAC,MAAM;QAChC,OAAO,aAAa,SAAS,CAAC,wBAAwB,QAAQ;YAAE;QAAY;IAChF;IACA;;KAEC,GACD,IAAI,SAAS;QACT,MAAM,eAAe,IAAI,2KAAA,CAAA,UAAY,CAAC,IAAI;QAC1C,aAAa,SAAS,CAAC,gKAAA,CAAA,yBAAsB;QAC7C,OAAO;IACX;IACA,cAAc,GACd;;;;KAIC,GACD,cAAc,KAAK,EAAE,UAAU,CAAC,CAAC,EAAE;QAC/B,OAAO,IAAI,CAAC,GAAG,CAAC,qBAAqB;YAAE,IAAI;YAAO,GAAG,OAAO;QAAC;IACjE;IACA;;;;;KAKC,GACD,kBAAkB;QACd,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB;IACA;;;KAGC,GACD,cAAc,GAAG,EAAE,IAAI,EAAE;QACrB,OAAO,IAAI,CAAC,GAAG,CAAC,uBAAuB;YAAE;YAAK;QAAK;IACvD;IACA,WAAW,GACX;;;KAGC,GACD,SAAS,OAAO,EAAE;QACd,OAAO,IAAI,CAAC,GAAG,CAAC,yBAAyB,WAAW;YAAE,QAAQ;gBAAE,UAAU;YAAQ;QAAE;IACxF;IACA;;;;KAIC,GACD,UAAU,OAAO,EAAE;QACf,OAAO,IAAI,CAAC,GAAG,CAAC,mBAAmB;IACvC;IACA;;;;KAIC,GACD,kBAAkB,OAAO,EAAE;QACvB,OAAO,IAAI,CAAC,GAAG,CAAC,4BAA4B;IAChD;IACA,uBAAuB,GACvB;;;;;KAKC,GACD,kBAAkB,GAAG,SAAS,EAAE;QAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,sCAAsC;YAAE;QAAU;IACtE;IACA;;;KAGC,GACD,qBAAqB;QACjB,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3902, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/v1/media-helpers.v1.js"], "sourcesContent": ["import * as fs from 'fs';\nimport { safeDeprecationWarning } from '../helpers';\nimport { EUploadMimeType } from '../types';\nexport async function readFileIntoBuffer(file) {\n    const handle = await getFileHandle(file);\n    if (typeof handle === 'number') {\n        return new Promise((resolve, reject) => {\n            fs.readFile(handle, (err, data) => {\n                if (err) {\n                    return reject(err);\n                }\n                resolve(data);\n            });\n        });\n    }\n    else if (handle instanceof Buffer) {\n        return handle;\n    }\n    else {\n        return handle.readFile();\n    }\n}\nexport function getFileHandle(file) {\n    if (typeof file === 'string') {\n        return fs.promises.open(file, 'r');\n    }\n    else if (typeof file === 'number') {\n        return file;\n    }\n    else if (typeof file === 'object' && !(file instanceof Buffer)) {\n        return file;\n    }\n    else if (!(file instanceof Buffer)) {\n        throw new Error('Given file is not valid, please check its type.');\n    }\n    else {\n        return file;\n    }\n}\nexport async function getFileSizeFromFileHandle(fileHandle) {\n    // Get the file size\n    if (typeof fileHandle === 'number') {\n        const stats = await new Promise((resolve, reject) => {\n            fs.fstat(fileHandle, (err, stats) => {\n                if (err)\n                    reject(err);\n                resolve(stats);\n            });\n        });\n        return stats.size;\n    }\n    else if (fileHandle instanceof Buffer) {\n        return fileHandle.length;\n    }\n    else {\n        return (await fileHandle.stat()).size;\n    }\n}\nexport function getMimeType(file, type, mimeType) {\n    if (typeof mimeType === 'string') {\n        return mimeType;\n    }\n    else if (typeof file === 'string' && !type) {\n        return getMimeByName(file);\n    }\n    else if (typeof type === 'string') {\n        return getMimeByType(type);\n    }\n    throw new Error('You must specify type if file is a file handle or Buffer.');\n}\nfunction getMimeByName(name) {\n    if (name.endsWith('.jpeg') || name.endsWith('.jpg'))\n        return EUploadMimeType.Jpeg;\n    if (name.endsWith('.png'))\n        return EUploadMimeType.Png;\n    if (name.endsWith('.webp'))\n        return EUploadMimeType.Webp;\n    if (name.endsWith('.gif'))\n        return EUploadMimeType.Gif;\n    if (name.endsWith('.mpeg4') || name.endsWith('.mp4'))\n        return EUploadMimeType.Mp4;\n    if (name.endsWith('.mov') || name.endsWith('.mov'))\n        return EUploadMimeType.Mov;\n    if (name.endsWith('.srt'))\n        return EUploadMimeType.Srt;\n    safeDeprecationWarning({\n        instance: 'TwitterApiv1ReadWrite',\n        method: 'uploadMedia',\n        problem: 'options.mimeType is missing and filename couldn\\'t help to resolve MIME type, so it will fallback to image/jpeg',\n        resolution: 'If you except to give filenames without extensions, please specify explicitlty the MIME type using options.mimeType',\n    });\n    return EUploadMimeType.Jpeg;\n}\nfunction getMimeByType(type) {\n    safeDeprecationWarning({\n        instance: 'TwitterApiv1ReadWrite',\n        method: 'uploadMedia',\n        problem: 'you\\'re using options.type',\n        resolution: 'Remove options.type argument and migrate to options.mimeType which takes the real MIME type. ' +\n            'If you\\'re using type=longmp4, add options.longVideo alongside of mimeType=EUploadMimeType.Mp4',\n    });\n    if (type === 'gif')\n        return EUploadMimeType.Gif;\n    if (type === 'jpg')\n        return EUploadMimeType.Jpeg;\n    if (type === 'png')\n        return EUploadMimeType.Png;\n    if (type === 'webp')\n        return EUploadMimeType.Webp;\n    if (type === 'srt')\n        return EUploadMimeType.Srt;\n    if (type === 'mp4' || type === 'longmp4')\n        return EUploadMimeType.Mp4;\n    if (type === 'mov')\n        return EUploadMimeType.Mov;\n    return type;\n}\nexport function getMediaCategoryByMime(name, target) {\n    if (name === EUploadMimeType.Mp4 || name === EUploadMimeType.Mov)\n        return target === 'tweet' ? 'TweetVideo' : 'DmVideo';\n    if (name === EUploadMimeType.Gif)\n        return target === 'tweet' ? 'TweetGif' : 'DmGif';\n    if (name === EUploadMimeType.Srt)\n        return 'Subtitles';\n    else\n        return target === 'tweet' ? 'TweetImage' : 'DmImage';\n}\nexport function sleepSecs(seconds) {\n    return new Promise(resolve => setTimeout(resolve, seconds * 1000));\n}\nexport async function readNextPartOf(file, chunkLength, bufferOffset = 0, buffer) {\n    if (file instanceof Buffer) {\n        const rt = file.slice(bufferOffset, bufferOffset + chunkLength);\n        return [rt, rt.length];\n    }\n    if (!buffer) {\n        throw new Error('Well, we will need a buffer to store file content.');\n    }\n    let bytesRead;\n    if (typeof file === 'number') {\n        bytesRead = await new Promise((resolve, reject) => {\n            fs.read(file, buffer, 0, chunkLength, bufferOffset, (err, nread) => {\n                if (err)\n                    reject(err);\n                resolve(nread);\n            });\n        });\n    }\n    else {\n        const res = await file.read(buffer, 0, chunkLength, bufferOffset);\n        bytesRead = res.bytesRead;\n    }\n    return [buffer, bytesRead];\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;AAAA;;;;AACO,eAAe,mBAAmB,IAAI;IACzC,MAAM,SAAS,MAAM,cAAc;IACnC,IAAI,OAAO,WAAW,UAAU;QAC5B,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,CAAA,GAAA,6FAAA,CAAA,WAAW,AAAD,EAAE,QAAQ,CAAC,KAAK;gBACtB,IAAI,KAAK;oBACL,OAAO,OAAO;gBAClB;gBACA,QAAQ;YACZ;QACJ;IACJ,OACK,IAAI,kBAAkB,QAAQ;QAC/B,OAAO;IACX,OACK;QACD,OAAO,OAAO,QAAQ;IAC1B;AACJ;AACO,SAAS,cAAc,IAAI;IAC9B,IAAI,OAAO,SAAS,UAAU;QAC1B,OAAO,6FAAA,CAAA,WAAW,CAAC,IAAI,CAAC,MAAM;IAClC,OACK,IAAI,OAAO,SAAS,UAAU;QAC/B,OAAO;IACX,OACK,IAAI,OAAO,SAAS,YAAY,CAAC,CAAC,gBAAgB,MAAM,GAAG;QAC5D,OAAO;IACX,OACK,IAAI,CAAC,CAAC,gBAAgB,MAAM,GAAG;QAChC,MAAM,IAAI,MAAM;IACpB,OACK;QACD,OAAO;IACX;AACJ;AACO,eAAe,0BAA0B,UAAU;IACtD,oBAAoB;IACpB,IAAI,OAAO,eAAe,UAAU;QAChC,MAAM,QAAQ,MAAM,IAAI,QAAQ,CAAC,SAAS;YACtC,CAAA,GAAA,6FAAA,CAAA,QAAQ,AAAD,EAAE,YAAY,CAAC,KAAK;gBACvB,IAAI,KACA,OAAO;gBACX,QAAQ;YACZ;QACJ;QACA,OAAO,MAAM,IAAI;IACrB,OACK,IAAI,sBAAsB,QAAQ;QACnC,OAAO,WAAW,MAAM;IAC5B,OACK;QACD,OAAO,CAAC,MAAM,WAAW,IAAI,EAAE,EAAE,IAAI;IACzC;AACJ;AACO,SAAS,YAAY,IAAI,EAAE,IAAI,EAAE,QAAQ;IAC5C,IAAI,OAAO,aAAa,UAAU;QAC9B,OAAO;IACX,OACK,IAAI,OAAO,SAAS,YAAY,CAAC,MAAM;QACxC,OAAO,cAAc;IACzB,OACK,IAAI,OAAO,SAAS,UAAU;QAC/B,OAAO,cAAc;IACzB;IACA,MAAM,IAAI,MAAM;AACpB;AACA,SAAS,cAAc,IAAI;IACvB,IAAI,KAAK,QAAQ,CAAC,YAAY,KAAK,QAAQ,CAAC,SACxC,OAAO,4LAAA,CAAA,kBAAe,CAAC,IAAI;IAC/B,IAAI,KAAK,QAAQ,CAAC,SACd,OAAO,4LAAA,CAAA,kBAAe,CAAC,GAAG;IAC9B,IAAI,KAAK,QAAQ,CAAC,UACd,OAAO,4LAAA,CAAA,kBAAe,CAAC,IAAI;IAC/B,IAAI,KAAK,QAAQ,CAAC,SACd,OAAO,4LAAA,CAAA,kBAAe,CAAC,GAAG;IAC9B,IAAI,KAAK,QAAQ,CAAC,aAAa,KAAK,QAAQ,CAAC,SACzC,OAAO,4LAAA,CAAA,kBAAe,CAAC,GAAG;IAC9B,IAAI,KAAK,QAAQ,CAAC,WAAW,KAAK,QAAQ,CAAC,SACvC,OAAO,4LAAA,CAAA,kBAAe,CAAC,GAAG;IAC9B,IAAI,KAAK,QAAQ,CAAC,SACd,OAAO,4LAAA,CAAA,kBAAe,CAAC,GAAG;IAC9B,CAAA,GAAA,gKAAA,CAAA,yBAAsB,AAAD,EAAE;QACnB,UAAU;QACV,QAAQ;QACR,SAAS;QACT,YAAY;IAChB;IACA,OAAO,4LAAA,CAAA,kBAAe,CAAC,IAAI;AAC/B;AACA,SAAS,cAAc,IAAI;IACvB,CAAA,GAAA,gKAAA,CAAA,yBAAsB,AAAD,EAAE;QACnB,UAAU;QACV,QAAQ;QACR,SAAS;QACT,YAAY,kGACR;IACR;IACA,IAAI,SAAS,OACT,OAAO,4LAAA,CAAA,kBAAe,CAAC,GAAG;IAC9B,IAAI,SAAS,OACT,OAAO,4LAAA,CAAA,kBAAe,CAAC,IAAI;IAC/B,IAAI,SAAS,OACT,OAAO,4LAAA,CAAA,kBAAe,CAAC,GAAG;IAC9B,IAAI,SAAS,QACT,OAAO,4LAAA,CAAA,kBAAe,CAAC,IAAI;IAC/B,IAAI,SAAS,OACT,OAAO,4LAAA,CAAA,kBAAe,CAAC,GAAG;IAC9B,IAAI,SAAS,SAAS,SAAS,WAC3B,OAAO,4LAAA,CAAA,kBAAe,CAAC,GAAG;IAC9B,IAAI,SAAS,OACT,OAAO,4LAAA,CAAA,kBAAe,CAAC,GAAG;IAC9B,OAAO;AACX;AACO,SAAS,uBAAuB,IAAI,EAAE,MAAM;IAC/C,IAAI,SAAS,4LAAA,CAAA,kBAAe,CAAC,GAAG,IAAI,SAAS,4LAAA,CAAA,kBAAe,CAAC,GAAG,EAC5D,OAAO,WAAW,UAAU,eAAe;IAC/C,IAAI,SAAS,4LAAA,CAAA,kBAAe,CAAC,GAAG,EAC5B,OAAO,WAAW,UAAU,aAAa;IAC7C,IAAI,SAAS,4LAAA,CAAA,kBAAe,CAAC,GAAG,EAC5B,OAAO;SAEP,OAAO,WAAW,UAAU,eAAe;AACnD;AACO,SAAS,UAAU,OAAO;IAC7B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,UAAU;AAChE;AACO,eAAe,eAAe,IAAI,EAAE,WAAW,EAAE,eAAe,CAAC,EAAE,MAAM;IAC5E,IAAI,gBAAgB,QAAQ;QACxB,MAAM,KAAK,KAAK,KAAK,CAAC,cAAc,eAAe;QACnD,OAAO;YAAC;YAAI,GAAG,MAAM;SAAC;IAC1B;IACA,IAAI,CAAC,QAAQ;QACT,MAAM,IAAI,MAAM;IACpB;IACA,IAAI;IACJ,IAAI,OAAO,SAAS,UAAU;QAC1B,YAAY,MAAM,IAAI,QAAQ,CAAC,SAAS;YACpC,CAAA,GAAA,6FAAA,CAAA,OAAO,AAAD,EAAE,MAAM,QAAQ,GAAG,aAAa,cAAc,CAAC,KAAK;gBACtD,IAAI,KACA,OAAO;gBACX,QAAQ;YACZ;QACJ;IACJ,OACK;QACD,MAAM,MAAM,MAAM,KAAK,IAAI,CAAC,QAAQ,GAAG,aAAa;QACpD,YAAY,IAAI,SAAS;IAC7B;IACA,OAAO;QAAC;QAAQ;KAAU;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4049, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/v1/client.v1.write.js"], "sourcesContent": ["import * as fs from 'fs';\nimport { API_V1_1_PREFIX, API_V1_1_UPLOAD_PREFIX } from '../globals';\nimport { hasMultipleItems } from '../helpers';\nimport { EUploadMimeType, } from '../types';\nimport TwitterApiv1ReadOnly from './client.v1.read';\nimport { getFileHandle, getFileSizeFromFileHandle, getMediaCategoryByMime, getMimeType, readFileIntoBuffer, readNextPartOf, sleepSecs } from './media-helpers.v1';\nconst UPLOAD_ENDPOINT = 'media/upload.json';\n/**\n * Base Twitter v1 client with read/write rights.\n */\nexport default class TwitterApiv1ReadWrite extends TwitterApiv1ReadOnly {\n    constructor() {\n        super(...arguments);\n        this._prefix = API_V1_1_PREFIX;\n    }\n    /**\n     * Get a client with only read rights.\n     */\n    get readOnly() {\n        return this;\n    }\n    /* Tweet API */\n    /**\n     * Post a new tweet.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/tweets/post-and-engage/api-reference/post-statuses-update\n     */\n    tweet(status, payload = {}) {\n        const queryParams = {\n            status,\n            tweet_mode: 'extended',\n            ...payload,\n        };\n        return this.post('statuses/update.json', queryParams);\n    }\n    /**\n     * Quote an existing tweet.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/tweets/post-and-engage/api-reference/post-statuses-update\n     */\n    async quote(status, quotingStatusId, payload = {}) {\n        const url = 'https://twitter.com/i/statuses/' + quotingStatusId;\n        return this.tweet(status, { ...payload, attachment_url: url });\n    }\n    /**\n     * Post a series of tweets.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/tweets/post-and-engage/api-reference/post-statuses-update\n     */\n    async tweetThread(tweets) {\n        const postedTweets = [];\n        for (const tweet of tweets) {\n            // Retrieve the last sent tweet\n            const lastTweet = postedTweets.length ? postedTweets[postedTweets.length - 1] : null;\n            // Build the tweet query params\n            const queryParams = { ...(typeof tweet === 'string' ? ({ status: tweet }) : tweet) };\n            // Reply to an existing tweet if needed\n            const inReplyToId = lastTweet ? lastTweet.id_str : queryParams.in_reply_to_status_id;\n            const status = queryParams.status;\n            if (inReplyToId) {\n                postedTweets.push(await this.reply(status, inReplyToId, queryParams));\n            }\n            else {\n                postedTweets.push(await this.tweet(status, queryParams));\n            }\n        }\n        return postedTweets;\n    }\n    /**\n     * Reply to an existing tweet. Shortcut to `.tweet` with tweaked parameters.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/tweets/post-and-engage/api-reference/post-statuses-update\n     */\n    reply(status, in_reply_to_status_id, payload = {}) {\n        return this.tweet(status, {\n            auto_populate_reply_metadata: true,\n            in_reply_to_status_id,\n            ...payload,\n        });\n    }\n    /**\n     * Delete an existing tweet belonging to you.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/tweets/post-and-engage/api-reference/post-statuses-destroy-id\n     */\n    deleteTweet(tweetId) {\n        return this.post('statuses/destroy/:id.json', { tweet_mode: 'extended' }, { params: { id: tweetId } });\n    }\n    /* User API */\n    /**\n     * Report the specified user as a spam account to Twitter.\n     * Additionally, optionally performs the equivalent of POST blocks/create on behalf of the authenticated user.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/mute-block-report-users/api-reference/post-users-report_spam\n     */\n    reportUserAsSpam(options) {\n        return this.post('users/report_spam.json', { tweet_mode: 'extended', ...options });\n    }\n    /**\n     * Turn on/off Retweets and device notifications from the specified user.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/follow-search-get-users/api-reference/post-friendships-update\n     */\n    updateFriendship(options) {\n        return this.post('friendships/update.json', options);\n    }\n    /**\n     * Follow the specified user.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/follow-search-get-users/api-reference/post-friendships-create\n     */\n    createFriendship(options) {\n        return this.post('friendships/create.json', options);\n    }\n    /**\n     * Unfollow the specified user.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/follow-search-get-users/api-reference/post-friendships-destroy\n     */\n    destroyFriendship(options) {\n        return this.post('friendships/destroy.json', options);\n    }\n    /* Account API */\n    /**\n     * Update current account settings for authenticating user.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/manage-account-settings/api-reference/get-account-settings\n     */\n    updateAccountSettings(options) {\n        return this.post('account/settings.json', options);\n    }\n    /**\n     * Sets some values that users are able to set under the \"Account\" tab of their settings page.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/manage-account-settings/api-reference/post-account-update_profile\n     */\n    updateAccountProfile(options) {\n        return this.post('account/update_profile.json', options);\n    }\n    /**\n     * Uploads a profile banner on behalf of the authenticating user.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/manage-account-settings/api-reference/post-account-update_profile_banner\n     */\n    async updateAccountProfileBanner(file, options = {}) {\n        const queryParams = {\n            banner: await readFileIntoBuffer(file),\n            ...options,\n        };\n        return this.post('account/update_profile_banner.json', queryParams, { forceBodyMode: 'form-data' });\n    }\n    /**\n     * Updates the authenticating user's profile image.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/manage-account-settings/api-reference/post-account-update_profile_image\n     */\n    async updateAccountProfileImage(file, options = {}) {\n        const queryParams = {\n            tweet_mode: 'extended',\n            image: await readFileIntoBuffer(file),\n            ...options,\n        };\n        return this.post('account/update_profile_image.json', queryParams, { forceBodyMode: 'form-data' });\n    }\n    /**\n     * Removes the uploaded profile banner for the authenticating user.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/manage-account-settings/api-reference/post-account-remove_profile_banner\n     */\n    removeAccountProfileBanner() {\n        return this.post('account/remove_profile_banner.json');\n    }\n    /* Lists */\n    /**\n     * Creates a new list for the authenticated user.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/create-manage-lists/api-reference/post-lists-create\n     */\n    createList(options) {\n        return this.post('lists/create.json', { tweet_mode: 'extended', ...options });\n    }\n    /**\n     * Updates the specified list. The authenticated user must own the list to be able to update it.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/create-manage-lists/api-reference/post-lists-update\n     */\n    updateList(options) {\n        return this.post('lists/update.json', { tweet_mode: 'extended', ...options });\n    }\n    /**\n     * Deletes the specified list. The authenticated user must own the list to be able to destroy it.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/create-manage-lists/api-reference/post-lists-destroy\n     */\n    removeList(options) {\n        return this.post('lists/destroy.json', { tweet_mode: 'extended', ...options });\n    }\n    /**\n     * Adds multiple members to a list, by specifying a comma-separated list of member ids or screen names.\n     * If you add a single `user_id` or `screen_name`, it will target `lists/members/create.json`, otherwise\n     * it will target `lists/members/create_all.json`.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/create-manage-lists/api-reference/post-lists-members-create_all\n     */\n    addListMembers(options) {\n        const hasMultiple = (options.user_id && hasMultipleItems(options.user_id)) || (options.screen_name && hasMultipleItems(options.screen_name));\n        const endpoint = hasMultiple ? 'lists/members/create_all.json' : 'lists/members/create.json';\n        return this.post(endpoint, options);\n    }\n    /**\n     * Removes multiple members to a list, by specifying a comma-separated list of member ids or screen names.\n     * If you add a single `user_id` or `screen_name`, it will target `lists/members/destroy.json`, otherwise\n     * it will target `lists/members/destroy_all.json`.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/create-manage-lists/api-reference/post-lists-members-destroy_all\n     */\n    removeListMembers(options) {\n        const hasMultiple = (options.user_id && hasMultipleItems(options.user_id)) || (options.screen_name && hasMultipleItems(options.screen_name));\n        const endpoint = hasMultiple ? 'lists/members/destroy_all.json' : 'lists/members/destroy.json';\n        return this.post(endpoint, options);\n    }\n    /**\n     * Subscribes the authenticated user to the specified list.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/create-manage-lists/api-reference/post-lists-subscribers-create\n     */\n    subscribeToList(options) {\n        return this.post('lists/subscribers/create.json', { tweet_mode: 'extended', ...options });\n    }\n    /**\n     * Unsubscribes the authenticated user of the specified list.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/accounts-and-users/create-manage-lists/api-reference/post-lists-subscribers-destroy\n     */\n    unsubscribeOfList(options) {\n        return this.post('lists/subscribers/destroy.json', { tweet_mode: 'extended', ...options });\n    }\n    /* Media upload API */\n    /**\n     * This endpoint can be used to provide additional information about the uploaded media_id.\n     * This feature is currently only supported for images and GIFs.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/media/upload-media/api-reference/post-media-metadata-create\n     */\n    createMediaMetadata(mediaId, metadata) {\n        return this.post('media/metadata/create.json', { media_id: mediaId, ...metadata }, { prefix: API_V1_1_UPLOAD_PREFIX, forceBodyMode: 'json' });\n    }\n    /**\n     * Use this endpoint to associate uploaded subtitles to an uploaded video. You can associate subtitles to video before or after Tweeting.\n     * **To obtain subtitle media ID, you must upload each subtitle file separately using `.uploadMedia()` method.**\n     *\n     * https://developer.twitter.com/en/docs/twitter-api/v1/media/upload-media/api-reference/post-media-subtitles-create\n     */\n    createMediaSubtitles(mediaId, subtitles) {\n        return this.post('media/subtitles/create.json', { media_id: mediaId, media_category: 'TweetVideo', subtitle_info: { subtitles } }, { prefix: API_V1_1_UPLOAD_PREFIX, forceBodyMode: 'json' });\n    }\n    /**\n     * Use this endpoint to dissociate subtitles from a video and delete the subtitles. You can dissociate subtitles from a video before or after Tweeting.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/media/upload-media/api-reference/post-media-subtitles-delete\n     */\n    deleteMediaSubtitles(mediaId, ...languages) {\n        return this.post('media/subtitles/delete.json', {\n            media_id: mediaId,\n            media_category: 'TweetVideo',\n            subtitle_info: { subtitles: languages.map(lang => ({ language_code: lang })) },\n        }, { prefix: API_V1_1_UPLOAD_PREFIX, forceBodyMode: 'json' });\n    }\n    /**\n     * Upload a media (JPG/PNG/GIF/MP4/MOV/WEBP) or subtitle (SRT) to Twitter and return the media_id to use in tweet/DM send.\n     *\n     * @param file If `string`, filename is supposed.\n     * A `Buffer` is a raw file.\n     * `fs.promises.FileHandle` or `number` are file pointers.\n     *\n     * @param options.type File type (Enum 'jpg' | 'longmp4' | 'mp4' | 'mov | 'png' | 'gif' | 'srt' | 'webp').\n     * If filename is given, it could be guessed with file extension, otherwise this parameter is mandatory.\n     * If type is not part of the enum, it will be used as mime type.\n     *\n     * Type `longmp4` is **required** is you try to upload a video higher than 140 seconds.\n     *\n     * @param options.chunkLength Maximum chunk length sent to Twitter. Default goes to 1 MB.\n     *\n     * @param options.additionalOwners Other user IDs allowed to use the returned media_id. Default goes to none.\n     *\n     * @param options.maxConcurrentUploads Maximum uploaded chunks in the same time. Default goes to 3.\n     *\n     * @param options.target Target type `tweet` or `dm`. Defaults to `tweet`.\n     * You must specify it if you send a media to use in DMs.\n     */\n    async uploadMedia(file, options = {}) {\n        var _a;\n        const chunkLength = (_a = options.chunkLength) !== null && _a !== void 0 ? _a : (1024 * 1024);\n        const { fileHandle, mediaCategory, fileSize, mimeType } = await this.getUploadMediaRequirements(file, options);\n        // Get the file handle (if not buffer)\n        try {\n            // Finally! We can send INIT message.\n            const mediaData = await this.post(UPLOAD_ENDPOINT, {\n                command: 'INIT',\n                total_bytes: fileSize,\n                media_type: mimeType,\n                media_category: mediaCategory,\n                additional_owners: options.additionalOwners,\n                shared: options.shared ? true : undefined,\n            }, { prefix: API_V1_1_UPLOAD_PREFIX });\n            // Upload the media chunk by chunk\n            await this.mediaChunkedUpload(fileHandle, chunkLength, mediaData.media_id_string, options.maxConcurrentUploads);\n            // Finalize media\n            const fullMediaData = await this.post(UPLOAD_ENDPOINT, {\n                command: 'FINALIZE',\n                media_id: mediaData.media_id_string,\n            }, { prefix: API_V1_1_UPLOAD_PREFIX });\n            if (fullMediaData.processing_info && fullMediaData.processing_info.state !== 'succeeded') {\n                // Must wait if video is still computed\n                await this.awaitForMediaProcessingCompletion(fullMediaData);\n            }\n            // Video is ready, return media_id\n            return fullMediaData.media_id_string;\n        }\n        finally {\n            // Close file if any\n            if (typeof file === 'number') {\n                // eslint-disable-next-line @typescript-eslint/no-empty-function\n                fs.close(file, () => { });\n            }\n            else if (typeof fileHandle === 'object' && !(fileHandle instanceof Buffer)) {\n                fileHandle.close();\n            }\n        }\n    }\n    async awaitForMediaProcessingCompletion(fullMediaData) {\n        var _a;\n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            fullMediaData = await this.mediaInfo(fullMediaData.media_id_string);\n            const { processing_info } = fullMediaData;\n            if (!processing_info || processing_info.state === 'succeeded') {\n                // Ok, completed!\n                return;\n            }\n            if ((_a = processing_info.error) === null || _a === void 0 ? void 0 : _a.code) {\n                const { name, message } = processing_info.error;\n                throw new Error(`Failed to process media: ${name} - ${message}.`);\n            }\n            if (processing_info.state === 'failed') {\n                // No error data\n                throw new Error('Failed to process the media.');\n            }\n            if (processing_info.check_after_secs) {\n                // Await for given seconds\n                await sleepSecs(processing_info.check_after_secs);\n            }\n            else {\n                // No info; Await for 5 seconds\n                await sleepSecs(5);\n            }\n        }\n    }\n    async getUploadMediaRequirements(file, { mimeType, type, target, longVideo } = {}) {\n        // Get the file handle (if not buffer)\n        let fileHandle;\n        try {\n            fileHandle = await getFileHandle(file);\n            // Get the mimetype\n            const realMimeType = getMimeType(file, type, mimeType);\n            // Get the media category\n            let mediaCategory;\n            // If explicit longmp4 OR explicit MIME type and not DM target\n            if (realMimeType === EUploadMimeType.Mp4 && ((!mimeType && !type && target !== 'dm') || longVideo)) {\n                mediaCategory = 'amplify_video';\n            }\n            else {\n                mediaCategory = getMediaCategoryByMime(realMimeType, target !== null && target !== void 0 ? target : 'tweet');\n            }\n            return {\n                fileHandle,\n                mediaCategory,\n                fileSize: await getFileSizeFromFileHandle(fileHandle),\n                mimeType: realMimeType,\n            };\n        }\n        catch (e) {\n            // Close file if any\n            if (typeof file === 'number') {\n                // eslint-disable-next-line @typescript-eslint/no-empty-function\n                fs.close(file, () => { });\n            }\n            else if (typeof fileHandle === 'object' && !(fileHandle instanceof Buffer)) {\n                fileHandle.close();\n            }\n            throw e;\n        }\n    }\n    async mediaChunkedUpload(fileHandle, chunkLength, mediaId, maxConcurrentUploads = 3) {\n        // Send chunk by chunk\n        let chunkIndex = 0;\n        if (maxConcurrentUploads < 1) {\n            throw new RangeError('Bad maxConcurrentUploads parameter.');\n        }\n        // Creating a buffer for doing file stuff (if we don't have one)\n        const buffer = fileHandle instanceof Buffer ? undefined : Buffer.alloc(chunkLength);\n        // Sliced/filled buffer returned for each part\n        let readBuffer;\n        // Needed to know when we should stop reading the file\n        let nread;\n        // Needed to use the buffer object (file handles always \"remembers\" file position)\n        let offset = 0;\n        [readBuffer, nread] = await readNextPartOf(fileHandle, chunkLength, offset, buffer);\n        offset += nread;\n        // Handle max concurrent uploads\n        const currentUploads = new Set();\n        // Read buffer until file is completely read\n        while (nread) {\n            const mediaBufferPart = readBuffer.slice(0, nread);\n            // Sent part if part has something inside\n            if (mediaBufferPart.length) {\n                const request = this.post(UPLOAD_ENDPOINT, {\n                    command: 'APPEND',\n                    media_id: mediaId,\n                    segment_index: chunkIndex,\n                    media: mediaBufferPart,\n                }, { prefix: API_V1_1_UPLOAD_PREFIX });\n                currentUploads.add(request);\n                request.then(() => {\n                    currentUploads.delete(request);\n                });\n                chunkIndex++;\n            }\n            if (currentUploads.size >= maxConcurrentUploads) {\n                // Await for first promise to be finished\n                await Promise.race(currentUploads);\n            }\n            [readBuffer, nread] = await readNextPartOf(fileHandle, chunkLength, offset, buffer);\n            offset += nread;\n        }\n        await Promise.all([...currentUploads]);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;;;;;;;AACA,MAAM,kBAAkB;AAIT,MAAM,8BAA8B,mLAAA,CAAA,UAAoB;IACnE,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,OAAO,GAAG,gKAAA,CAAA,kBAAe;IAClC;IACA;;KAEC,GACD,IAAI,WAAW;QACX,OAAO,IAAI;IACf;IACA,aAAa,GACb;;;KAGC,GACD,MAAM,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QACxB,MAAM,cAAc;YAChB;YACA,YAAY;YACZ,GAAG,OAAO;QACd;QACA,OAAO,IAAI,CAAC,IAAI,CAAC,wBAAwB;IAC7C;IACA;;;KAGC,GACD,MAAM,MAAM,MAAM,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC,EAAE;QAC/C,MAAM,MAAM,oCAAoC;QAChD,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAAE,GAAG,OAAO;YAAE,gBAAgB;QAAI;IAChE;IACA;;;KAGC,GACD,MAAM,YAAY,MAAM,EAAE;QACtB,MAAM,eAAe,EAAE;QACvB,KAAK,MAAM,SAAS,OAAQ;YACxB,+BAA+B;YAC/B,MAAM,YAAY,aAAa,MAAM,GAAG,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,GAAG;YAChF,+BAA+B;YAC/B,MAAM,cAAc;gBAAE,GAAI,OAAO,UAAU,WAAY;oBAAE,QAAQ;gBAAM,IAAK,KAAK;YAAE;YACnF,uCAAuC;YACvC,MAAM,cAAc,YAAY,UAAU,MAAM,GAAG,YAAY,qBAAqB;YACpF,MAAM,SAAS,YAAY,MAAM;YACjC,IAAI,aAAa;gBACb,aAAa,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,aAAa;YAC5D,OACK;gBACD,aAAa,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC/C;QACJ;QACA,OAAO;IACX;IACA;;;KAGC,GACD,MAAM,MAAM,EAAE,qBAAqB,EAAE,UAAU,CAAC,CAAC,EAAE;QAC/C,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YACtB,8BAA8B;YAC9B;YACA,GAAG,OAAO;QACd;IACJ;IACA;;;KAGC,GACD,YAAY,OAAO,EAAE;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,6BAA6B;YAAE,YAAY;QAAW,GAAG;YAAE,QAAQ;gBAAE,IAAI;YAAQ;QAAE;IACxG;IACA,YAAY,GACZ;;;;KAIC,GACD,iBAAiB,OAAO,EAAE;QACtB,OAAO,IAAI,CAAC,IAAI,CAAC,0BAA0B;YAAE,YAAY;YAAY,GAAG,OAAO;QAAC;IACpF;IACA;;;KAGC,GACD,iBAAiB,OAAO,EAAE;QACtB,OAAO,IAAI,CAAC,IAAI,CAAC,2BAA2B;IAChD;IACA;;;KAGC,GACD,iBAAiB,OAAO,EAAE;QACtB,OAAO,IAAI,CAAC,IAAI,CAAC,2BAA2B;IAChD;IACA;;;KAGC,GACD,kBAAkB,OAAO,EAAE;QACvB,OAAO,IAAI,CAAC,IAAI,CAAC,4BAA4B;IACjD;IACA,eAAe,GACf;;;KAGC,GACD,sBAAsB,OAAO,EAAE;QAC3B,OAAO,IAAI,CAAC,IAAI,CAAC,yBAAyB;IAC9C;IACA;;;KAGC,GACD,qBAAqB,OAAO,EAAE;QAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,+BAA+B;IACpD;IACA;;;KAGC,GACD,MAAM,2BAA2B,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE;QACjD,MAAM,cAAc;YAChB,QAAQ,MAAM,CAAA,GAAA,qLAAA,CAAA,qBAAkB,AAAD,EAAE;YACjC,GAAG,OAAO;QACd;QACA,OAAO,IAAI,CAAC,IAAI,CAAC,sCAAsC,aAAa;YAAE,eAAe;QAAY;IACrG;IACA;;;KAGC,GACD,MAAM,0BAA0B,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE;QAChD,MAAM,cAAc;YAChB,YAAY;YACZ,OAAO,MAAM,CAAA,GAAA,qLAAA,CAAA,qBAAkB,AAAD,EAAE;YAChC,GAAG,OAAO;QACd;QACA,OAAO,IAAI,CAAC,IAAI,CAAC,qCAAqC,aAAa;YAAE,eAAe;QAAY;IACpG;IACA;;;KAGC,GACD,6BAA6B;QACzB,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB;IACA,SAAS,GACT;;;KAGC,GACD,WAAW,OAAO,EAAE;QAChB,OAAO,IAAI,CAAC,IAAI,CAAC,qBAAqB;YAAE,YAAY;YAAY,GAAG,OAAO;QAAC;IAC/E;IACA;;;KAGC,GACD,WAAW,OAAO,EAAE;QAChB,OAAO,IAAI,CAAC,IAAI,CAAC,qBAAqB;YAAE,YAAY;YAAY,GAAG,OAAO;QAAC;IAC/E;IACA;;;KAGC,GACD,WAAW,OAAO,EAAE;QAChB,OAAO,IAAI,CAAC,IAAI,CAAC,sBAAsB;YAAE,YAAY;YAAY,GAAG,OAAO;QAAC;IAChF;IACA;;;;;KAKC,GACD,eAAe,OAAO,EAAE;QACpB,MAAM,cAAc,AAAC,QAAQ,OAAO,IAAI,CAAA,GAAA,gKAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,OAAO,KAAO,QAAQ,WAAW,IAAI,CAAA,GAAA,gKAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,WAAW;QAC1I,MAAM,WAAW,cAAc,kCAAkC;QACjE,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU;IAC/B;IACA;;;;;KAKC,GACD,kBAAkB,OAAO,EAAE;QACvB,MAAM,cAAc,AAAC,QAAQ,OAAO,IAAI,CAAA,GAAA,gKAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,OAAO,KAAO,QAAQ,WAAW,IAAI,CAAA,GAAA,gKAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,WAAW;QAC1I,MAAM,WAAW,cAAc,mCAAmC;QAClE,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU;IAC/B;IACA;;;KAGC,GACD,gBAAgB,OAAO,EAAE;QACrB,OAAO,IAAI,CAAC,IAAI,CAAC,iCAAiC;YAAE,YAAY;YAAY,GAAG,OAAO;QAAC;IAC3F;IACA;;;KAGC,GACD,kBAAkB,OAAO,EAAE;QACvB,OAAO,IAAI,CAAC,IAAI,CAAC,kCAAkC;YAAE,YAAY;YAAY,GAAG,OAAO;QAAC;IAC5F;IACA,oBAAoB,GACpB;;;;KAIC,GACD,oBAAoB,OAAO,EAAE,QAAQ,EAAE;QACnC,OAAO,IAAI,CAAC,IAAI,CAAC,8BAA8B;YAAE,UAAU;YAAS,GAAG,QAAQ;QAAC,GAAG;YAAE,QAAQ,gKAAA,CAAA,yBAAsB;YAAE,eAAe;QAAO;IAC/I;IACA;;;;;KAKC,GACD,qBAAqB,OAAO,EAAE,SAAS,EAAE;QACrC,OAAO,IAAI,CAAC,IAAI,CAAC,+BAA+B;YAAE,UAAU;YAAS,gBAAgB;YAAc,eAAe;gBAAE;YAAU;QAAE,GAAG;YAAE,QAAQ,gKAAA,CAAA,yBAAsB;YAAE,eAAe;QAAO;IAC/L;IACA;;;KAGC,GACD,qBAAqB,OAAO,EAAE,GAAG,SAAS,EAAE;QACxC,OAAO,IAAI,CAAC,IAAI,CAAC,+BAA+B;YAC5C,UAAU;YACV,gBAAgB;YAChB,eAAe;gBAAE,WAAW,UAAU,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAAE,eAAe;oBAAK,CAAC;YAAG;QACjF,GAAG;YAAE,QAAQ,gKAAA,CAAA,yBAAsB;YAAE,eAAe;QAAO;IAC/D;IACA;;;;;;;;;;;;;;;;;;;;;KAqBC,GACD,MAAM,YAAY,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE;QAClC,IAAI;QACJ,MAAM,cAAc,CAAC,KAAK,QAAQ,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAM,OAAO;QACxF,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,MAAM;QACtG,sCAAsC;QACtC,IAAI;YACA,qCAAqC;YACrC,MAAM,YAAY,MAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB;gBAC/C,SAAS;gBACT,aAAa;gBACb,YAAY;gBACZ,gBAAgB;gBAChB,mBAAmB,QAAQ,gBAAgB;gBAC3C,QAAQ,QAAQ,MAAM,GAAG,OAAO;YACpC,GAAG;gBAAE,QAAQ,gKAAA,CAAA,yBAAsB;YAAC;YACpC,kCAAkC;YAClC,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,aAAa,UAAU,eAAe,EAAE,QAAQ,oBAAoB;YAC9G,iBAAiB;YACjB,MAAM,gBAAgB,MAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB;gBACnD,SAAS;gBACT,UAAU,UAAU,eAAe;YACvC,GAAG;gBAAE,QAAQ,gKAAA,CAAA,yBAAsB;YAAC;YACpC,IAAI,cAAc,eAAe,IAAI,cAAc,eAAe,CAAC,KAAK,KAAK,aAAa;gBACtF,uCAAuC;gBACvC,MAAM,IAAI,CAAC,iCAAiC,CAAC;YACjD;YACA,kCAAkC;YAClC,OAAO,cAAc,eAAe;QACxC,SACQ;YACJ,oBAAoB;YACpB,IAAI,OAAO,SAAS,UAAU;gBAC1B,gEAAgE;gBAChE,CAAA,GAAA,6FAAA,CAAA,QAAQ,AAAD,EAAE,MAAM,KAAQ;YAC3B,OACK,IAAI,OAAO,eAAe,YAAY,CAAC,CAAC,sBAAsB,MAAM,GAAG;gBACxE,WAAW,KAAK;YACpB;QACJ;IACJ;IACA,MAAM,kCAAkC,aAAa,EAAE;QACnD,IAAI;QACJ,iDAAiD;QACjD,MAAO,KAAM;YACT,gBAAgB,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,eAAe;YAClE,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,IAAI,CAAC,mBAAmB,gBAAgB,KAAK,KAAK,aAAa;gBAC3D,iBAAiB;gBACjB;YACJ;YACA,IAAI,CAAC,KAAK,gBAAgB,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,EAAE;gBAC3E,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,gBAAgB,KAAK;gBAC/C,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,KAAK,GAAG,EAAE,QAAQ,CAAC,CAAC;YACpE;YACA,IAAI,gBAAgB,KAAK,KAAK,UAAU;gBACpC,gBAAgB;gBAChB,MAAM,IAAI,MAAM;YACpB;YACA,IAAI,gBAAgB,gBAAgB,EAAE;gBAClC,0BAA0B;gBAC1B,MAAM,CAAA,GAAA,qLAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,gBAAgB;YACpD,OACK;gBACD,+BAA+B;gBAC/B,MAAM,CAAA,GAAA,qLAAA,CAAA,YAAS,AAAD,EAAE;YACpB;QACJ;IACJ;IACA,MAAM,2BAA2B,IAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC,EAAE;QAC/E,sCAAsC;QACtC,IAAI;QACJ,IAAI;YACA,aAAa,MAAM,CAAA,GAAA,qLAAA,CAAA,gBAAa,AAAD,EAAE;YACjC,mBAAmB;YACnB,MAAM,eAAe,CAAA,GAAA,qLAAA,CAAA,cAAW,AAAD,EAAE,MAAM,MAAM;YAC7C,yBAAyB;YACzB,IAAI;YACJ,8DAA8D;YAC9D,IAAI,iBAAiB,4LAAA,CAAA,kBAAe,CAAC,GAAG,IAAI,CAAC,AAAC,CAAC,YAAY,CAAC,QAAQ,WAAW,QAAS,SAAS,GAAG;gBAChG,gBAAgB;YACpB,OACK;gBACD,gBAAgB,CAAA,GAAA,qLAAA,CAAA,yBAAsB,AAAD,EAAE,cAAc,WAAW,QAAQ,WAAW,KAAK,IAAI,SAAS;YACzG;YACA,OAAO;gBACH;gBACA;gBACA,UAAU,MAAM,CAAA,GAAA,qLAAA,CAAA,4BAAyB,AAAD,EAAE;gBAC1C,UAAU;YACd;QACJ,EACA,OAAO,GAAG;YACN,oBAAoB;YACpB,IAAI,OAAO,SAAS,UAAU;gBAC1B,gEAAgE;gBAChE,CAAA,GAAA,6FAAA,CAAA,QAAQ,AAAD,EAAE,MAAM,KAAQ;YAC3B,OACK,IAAI,OAAO,eAAe,YAAY,CAAC,CAAC,sBAAsB,MAAM,GAAG;gBACxE,WAAW,KAAK;YACpB;YACA,MAAM;QACV;IACJ;IACA,MAAM,mBAAmB,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,uBAAuB,CAAC,EAAE;QACjF,sBAAsB;QACtB,IAAI,aAAa;QACjB,IAAI,uBAAuB,GAAG;YAC1B,MAAM,IAAI,WAAW;QACzB;QACA,gEAAgE;QAChE,MAAM,SAAS,sBAAsB,SAAS,YAAY,OAAO,KAAK,CAAC;QACvE,8CAA8C;QAC9C,IAAI;QACJ,sDAAsD;QACtD,IAAI;QACJ,kFAAkF;QAClF,IAAI,SAAS;QACb,CAAC,YAAY,MAAM,GAAG,MAAM,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,aAAa,QAAQ;QAC5E,UAAU;QACV,gCAAgC;QAChC,MAAM,iBAAiB,IAAI;QAC3B,4CAA4C;QAC5C,MAAO,MAAO;YACV,MAAM,kBAAkB,WAAW,KAAK,CAAC,GAAG;YAC5C,yCAAyC;YACzC,IAAI,gBAAgB,MAAM,EAAE;gBACxB,MAAM,UAAU,IAAI,CAAC,IAAI,CAAC,iBAAiB;oBACvC,SAAS;oBACT,UAAU;oBACV,eAAe;oBACf,OAAO;gBACX,GAAG;oBAAE,QAAQ,gKAAA,CAAA,yBAAsB;gBAAC;gBACpC,eAAe,GAAG,CAAC;gBACnB,QAAQ,IAAI,CAAC;oBACT,eAAe,MAAM,CAAC;gBAC1B;gBACA;YACJ;YACA,IAAI,eAAe,IAAI,IAAI,sBAAsB;gBAC7C,yCAAyC;gBACzC,MAAM,QAAQ,IAAI,CAAC;YACvB;YACA,CAAC,YAAY,MAAM,GAAG,MAAM,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,aAAa,QAAQ;YAC5E,UAAU;QACd;QACA,MAAM,QAAQ,GAAG,CAAC;eAAI;SAAe;IACzC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4504, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/v1/client.v1.js"], "sourcesContent": ["import { API_V1_1_PREFIX } from '../globals';\nimport { DmEventsV1Paginator, WelcomeDmV1Paginator } from '../paginators/dm.paginator.v1';\nimport { EDirectMessageEventTypeV1, } from '../types';\nimport TwitterApiv1ReadWrite from './client.v1.write';\n/**\n * Twitter v1.1 API client with read/write/DMs rights.\n */\nexport class TwitterApiv1 extends TwitterApiv1ReadWrite {\n    constructor() {\n        super(...arguments);\n        this._prefix = API_V1_1_PREFIX;\n    }\n    /**\n     * Get a client with read/write rights.\n     */\n    get readWrite() {\n        return this;\n    }\n    /* Direct messages */\n    // Part: Sending and receiving events\n    /**\n     * Publishes a new message_create event resulting in a Direct Message sent to a specified user from the authenticating user.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/direct-messages/sending-and-receiving/api-reference/new-event\n     */\n    sendDm({ recipient_id, custom_profile_id, ...params }) {\n        const args = {\n            event: {\n                type: EDirectMessageEventTypeV1.Create,\n                [EDirectMessageEventTypeV1.Create]: {\n                    target: { recipient_id },\n                    message_data: params,\n                },\n            },\n        };\n        if (custom_profile_id) {\n            args.event[EDirectMessageEventTypeV1.Create].custom_profile_id = custom_profile_id;\n        }\n        return this.post('direct_messages/events/new.json', args, {\n            forceBodyMode: 'json',\n        });\n    }\n    /**\n     * Returns a single Direct Message event by the given id.\n     *\n     * https://developer.twitter.com/en/docs/twitter-api/v1/direct-messages/sending-and-receiving/api-reference/get-event\n     */\n    getDmEvent(id) {\n        return this.get('direct_messages/events/show.json', { id });\n    }\n    /**\n     * Deletes the direct message specified in the required ID parameter.\n     * The authenticating user must be the recipient of the specified direct message.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/direct-messages/sending-and-receiving/api-reference/delete-message-event\n     */\n    deleteDm(id) {\n        return this.delete('direct_messages/events/destroy.json', { id });\n    }\n    /**\n     * Returns all Direct Message events (both sent and received) within the last 30 days.\n     * Sorted in reverse-chronological order.\n     *\n     * https://developer.twitter.com/en/docs/twitter-api/v1/direct-messages/sending-and-receiving/api-reference/list-events\n     */\n    async listDmEvents(args = {}) {\n        const queryParams = { ...args };\n        const initialRq = await this.get('direct_messages/events/list.json', queryParams, { fullResponse: true });\n        return new DmEventsV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    // Part: Welcome messages (events)\n    /**\n     * Creates a new Welcome Message that will be stored and sent in the future from the authenticating user in defined circumstances.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/direct-messages/welcome-messages/api-reference/new-welcome-message\n     */\n    newWelcomeDm(name, data) {\n        const args = {\n            [EDirectMessageEventTypeV1.WelcomeCreate]: {\n                name,\n                message_data: data,\n            },\n        };\n        return this.post('direct_messages/welcome_messages/new.json', args, {\n            forceBodyMode: 'json',\n        });\n    }\n    /**\n     * Returns a Welcome Message by the given id.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/direct-messages/welcome-messages/api-reference/get-welcome-message\n     */\n    getWelcomeDm(id) {\n        return this.get('direct_messages/welcome_messages/show.json', { id });\n    }\n    /**\n     * Deletes a Welcome Message by the given id.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/direct-messages/welcome-messages/api-reference/delete-welcome-message\n     */\n    deleteWelcomeDm(id) {\n        return this.delete('direct_messages/welcome_messages/destroy.json', { id });\n    }\n    /**\n     * Updates a Welcome Message by the given ID.\n     * Updates to the welcome_message object are atomic.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/direct-messages/welcome-messages/api-reference/update-welcome-message\n     */\n    updateWelcomeDm(id, data) {\n        const args = { message_data: data };\n        return this.put('direct_messages/welcome_messages/update.json', args, {\n            forceBodyMode: 'json',\n            query: { id },\n        });\n    }\n    /**\n     * Returns all Direct Message events (both sent and received) within the last 30 days.\n     * Sorted in reverse-chronological order.\n     *\n     * https://developer.twitter.com/en/docs/twitter-api/v1/direct-messages/sending-and-receiving/api-reference/list-events\n     */\n    async listWelcomeDms(args = {}) {\n        const queryParams = { ...args };\n        const initialRq = await this.get('direct_messages/welcome_messages/list.json', queryParams, { fullResponse: true });\n        return new WelcomeDmV1Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    // Part: Welcome message (rules)\n    /**\n     * Creates a new Welcome Message Rule that determines which Welcome Message will be shown in a given conversation.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/direct-messages/welcome-messages/api-reference/new-welcome-message-rule\n     */\n    newWelcomeDmRule(welcomeMessageId) {\n        return this.post('direct_messages/welcome_messages/rules/new.json', {\n            welcome_message_rule: { welcome_message_id: welcomeMessageId },\n        }, {\n            forceBodyMode: 'json',\n        });\n    }\n    /**\n     * Returns a Welcome Message Rule by the given id.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/direct-messages/welcome-messages/api-reference/get-welcome-message-rule\n     */\n    getWelcomeDmRule(id) {\n        return this.get('direct_messages/welcome_messages/rules/show.json', { id });\n    }\n    /**\n     * Deletes a Welcome Message Rule by the given id.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/direct-messages/welcome-messages/api-reference/delete-welcome-message-rule\n     */\n    deleteWelcomeDmRule(id) {\n        return this.delete('direct_messages/welcome_messages/rules/destroy.json', { id });\n    }\n    /**\n     * Retrieves all welcome DM rules for this account.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/direct-messages/welcome-messages/api-reference/list-welcome-message-rules\n     */\n    async listWelcomeDmRules(args = {}) {\n        const queryParams = { ...args };\n        return this.get('direct_messages/welcome_messages/rules/list.json', queryParams);\n    }\n    /**\n     * Set the current showed welcome message for logged account ; wrapper for Welcome DM rules.\n     * Test if a rule already exists, delete if any, then create a rule for current message ID.\n     *\n     * If you don't have already a welcome message, create it with `.newWelcomeMessage`.\n     */\n    async setWelcomeDm(welcomeMessageId, deleteAssociatedWelcomeDmWhenDeletingRule = true) {\n        var _a;\n        const existingRules = await this.listWelcomeDmRules();\n        if ((_a = existingRules.welcome_message_rules) === null || _a === void 0 ? void 0 : _a.length) {\n            for (const rule of existingRules.welcome_message_rules) {\n                await this.deleteWelcomeDmRule(rule.id);\n                if (deleteAssociatedWelcomeDmWhenDeletingRule) {\n                    await this.deleteWelcomeDm(rule.welcome_message_id);\n                }\n            }\n        }\n        return this.newWelcomeDmRule(welcomeMessageId);\n    }\n    // Part: Read indicator\n    /**\n     * Marks a message as read in the recipient’s Direct Message conversation view with the sender.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/direct-messages/typing-indicator-and-read-receipts/api-reference/new-read-receipt\n     */\n    markDmAsRead(lastEventId, recipientId) {\n        return this.post('direct_messages/mark_read.json', {\n            last_read_event_id: lastEventId,\n            recipient_id: recipientId,\n        }, { forceBodyMode: 'url' });\n    }\n    /**\n     * Displays a visual typing indicator in the recipient’s Direct Message conversation view with the sender.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/direct-messages/typing-indicator-and-read-receipts/api-reference/new-typing-indicator\n     */\n    indicateDmTyping(recipientId) {\n        return this.post('direct_messages/indicate_typing.json', {\n            recipient_id: recipientId,\n        }, { forceBodyMode: 'url' });\n    }\n    // Part: Images\n    /**\n     * Get a single image attached to a direct message. TwitterApi client must be logged with OAuth 1.0a.\n     * https://developer.twitter.com/en/docs/twitter-api/v1/direct-messages/message-attachments/guides/retrieving-media\n     */\n    async downloadDmImage(urlOrDm) {\n        if (typeof urlOrDm !== 'string') {\n            const attachment = urlOrDm[EDirectMessageEventTypeV1.Create].message_data.attachment;\n            if (!attachment) {\n                throw new Error('The given direct message doesn\\'t contain any attachment');\n            }\n            urlOrDm = attachment.media.media_url_https;\n        }\n        const data = await this.get(urlOrDm, undefined, { forceParseMode: 'buffer', prefix: '' });\n        if (!data.length) {\n            throw new Error('Image not found. Make sure you are logged with credentials able to access direct messages, and check the URL.');\n        }\n        return data;\n    }\n}\nexport default TwitterApiv1;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;;;;;AAIO,MAAM,qBAAqB,oLAAA,CAAA,UAAqB;IACnD,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,OAAO,GAAG,gKAAA,CAAA,kBAAe;IAClC;IACA;;KAEC,GACD,IAAI,YAAY;QACZ,OAAO,IAAI;IACf;IACA,mBAAmB,GACnB,qCAAqC;IACrC;;;KAGC,GACD,OAAO,EAAE,YAAY,EAAE,iBAAiB,EAAE,GAAG,QAAQ,EAAE;QACnD,MAAM,OAAO;YACT,OAAO;gBACH,MAAM,yLAAA,CAAA,4BAAyB,CAAC,MAAM;gBACtC,CAAC,yLAAA,CAAA,4BAAyB,CAAC,MAAM,CAAC,EAAE;oBAChC,QAAQ;wBAAE;oBAAa;oBACvB,cAAc;gBAClB;YACJ;QACJ;QACA,IAAI,mBAAmB;YACnB,KAAK,KAAK,CAAC,yLAAA,CAAA,4BAAyB,CAAC,MAAM,CAAC,CAAC,iBAAiB,GAAG;QACrE;QACA,OAAO,IAAI,CAAC,IAAI,CAAC,mCAAmC,MAAM;YACtD,eAAe;QACnB;IACJ;IACA;;;;KAIC,GACD,WAAW,EAAE,EAAE;QACX,OAAO,IAAI,CAAC,GAAG,CAAC,oCAAoC;YAAE;QAAG;IAC7D;IACA;;;;KAIC,GACD,SAAS,EAAE,EAAE;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,uCAAuC;YAAE;QAAG;IACnE;IACA;;;;;KAKC,GACD,MAAM,aAAa,OAAO,CAAC,CAAC,EAAE;QAC1B,MAAM,cAAc;YAAE,GAAG,IAAI;QAAC;QAC9B,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,oCAAoC,aAAa;YAAE,cAAc;QAAK;QACvG,OAAO,IAAI,4LAAA,CAAA,sBAAmB,CAAC;YAC3B,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA,kCAAkC;IAClC;;;KAGC,GACD,aAAa,IAAI,EAAE,IAAI,EAAE;QACrB,MAAM,OAAO;YACT,CAAC,yLAAA,CAAA,4BAAyB,CAAC,aAAa,CAAC,EAAE;gBACvC;gBACA,cAAc;YAClB;QACJ;QACA,OAAO,IAAI,CAAC,IAAI,CAAC,6CAA6C,MAAM;YAChE,eAAe;QACnB;IACJ;IACA;;;KAGC,GACD,aAAa,EAAE,EAAE;QACb,OAAO,IAAI,CAAC,GAAG,CAAC,8CAA8C;YAAE;QAAG;IACvE;IACA;;;KAGC,GACD,gBAAgB,EAAE,EAAE;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC,iDAAiD;YAAE;QAAG;IAC7E;IACA;;;;KAIC,GACD,gBAAgB,EAAE,EAAE,IAAI,EAAE;QACtB,MAAM,OAAO;YAAE,cAAc;QAAK;QAClC,OAAO,IAAI,CAAC,GAAG,CAAC,gDAAgD,MAAM;YAClE,eAAe;YACf,OAAO;gBAAE;YAAG;QAChB;IACJ;IACA;;;;;KAKC,GACD,MAAM,eAAe,OAAO,CAAC,CAAC,EAAE;QAC5B,MAAM,cAAc;YAAE,GAAG,IAAI;QAAC;QAC9B,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,8CAA8C,aAAa;YAAE,cAAc;QAAK;QACjH,OAAO,IAAI,4LAAA,CAAA,uBAAoB,CAAC;YAC5B,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA,gCAAgC;IAChC;;;KAGC,GACD,iBAAiB,gBAAgB,EAAE;QAC/B,OAAO,IAAI,CAAC,IAAI,CAAC,mDAAmD;YAChE,sBAAsB;gBAAE,oBAAoB;YAAiB;QACjE,GAAG;YACC,eAAe;QACnB;IACJ;IACA;;;KAGC,GACD,iBAAiB,EAAE,EAAE;QACjB,OAAO,IAAI,CAAC,GAAG,CAAC,oDAAoD;YAAE;QAAG;IAC7E;IACA;;;KAGC,GACD,oBAAoB,EAAE,EAAE;QACpB,OAAO,IAAI,CAAC,MAAM,CAAC,uDAAuD;YAAE;QAAG;IACnF;IACA;;;KAGC,GACD,MAAM,mBAAmB,OAAO,CAAC,CAAC,EAAE;QAChC,MAAM,cAAc;YAAE,GAAG,IAAI;QAAC;QAC9B,OAAO,IAAI,CAAC,GAAG,CAAC,oDAAoD;IACxE;IACA;;;;;KAKC,GACD,MAAM,aAAa,gBAAgB,EAAE,4CAA4C,IAAI,EAAE;QACnF,IAAI;QACJ,MAAM,gBAAgB,MAAM,IAAI,CAAC,kBAAkB;QACnD,IAAI,CAAC,KAAK,cAAc,qBAAqB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,EAAE;YAC3F,KAAK,MAAM,QAAQ,cAAc,qBAAqB,CAAE;gBACpD,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE;gBACtC,IAAI,2CAA2C;oBAC3C,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,kBAAkB;gBACtD;YACJ;QACJ;QACA,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC;IACA,uBAAuB;IACvB;;;KAGC,GACD,aAAa,WAAW,EAAE,WAAW,EAAE;QACnC,OAAO,IAAI,CAAC,IAAI,CAAC,kCAAkC;YAC/C,oBAAoB;YACpB,cAAc;QAClB,GAAG;YAAE,eAAe;QAAM;IAC9B;IACA;;;KAGC,GACD,iBAAiB,WAAW,EAAE;QAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,wCAAwC;YACrD,cAAc;QAClB,GAAG;YAAE,eAAe;QAAM;IAC9B;IACA,eAAe;IACf;;;KAGC,GACD,MAAM,gBAAgB,OAAO,EAAE;QAC3B,IAAI,OAAO,YAAY,UAAU;YAC7B,MAAM,aAAa,OAAO,CAAC,yLAAA,CAAA,4BAAyB,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,UAAU;YACpF,IAAI,CAAC,YAAY;gBACb,MAAM,IAAI,MAAM;YACpB;YACA,UAAU,WAAW,KAAK,CAAC,eAAe;QAC9C;QACA,MAAM,OAAO,MAAM,IAAI,CAAC,GAAG,CAAC,SAAS,WAAW;YAAE,gBAAgB;YAAU,QAAQ;QAAG;QACvF,IAAI,CAAC,KAAK,MAAM,EAAE;YACd,MAAM,IAAI,MAAM;QACpB;QACA,OAAO;IACX;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4759, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/v2/includes.v2.helper.js"], "sourcesContent": ["/**\n * Provide helpers for `.includes` of a v2 API result.\n * Needed expansions for a method to work are specified (*`like this`*).\n */\nexport class TwitterV2IncludesHelper {\n    constructor(result) {\n        this.result = result;\n    }\n    /* Tweets */\n    get tweets() {\n        return TwitterV2IncludesHelper.tweets(this.result);\n    }\n    static tweets(result) {\n        var _a, _b;\n        return (_b = (_a = result.includes) === null || _a === void 0 ? void 0 : _a.tweets) !== null && _b !== void 0 ? _b : [];\n    }\n    tweetById(id) {\n        return TwitterV2IncludesHelper.tweetById(this.result, id);\n    }\n    static tweetById(result, id) {\n        return this.tweets(result).find(tweet => tweet.id === id);\n    }\n    /** Retweet associated with the given tweet (*`referenced_tweets.id`*) */\n    retweet(tweet) {\n        return TwitterV2IncludesHelper.retweet(this.result, tweet);\n    }\n    /** Retweet associated with the given tweet (*`referenced_tweets.id`*) */\n    static retweet(result, tweet) {\n        var _a;\n        const retweetIds = ((_a = tweet.referenced_tweets) !== null && _a !== void 0 ? _a : [])\n            .filter(ref => ref.type === 'retweeted')\n            .map(ref => ref.id);\n        return this.tweets(result).find(t => retweetIds.includes(t.id));\n    }\n    /** Quoted tweet associated with the given tweet (*`referenced_tweets.id`*) */\n    quote(tweet) {\n        return TwitterV2IncludesHelper.quote(this.result, tweet);\n    }\n    /** Quoted tweet associated with the given tweet (*`referenced_tweets.id`*) */\n    static quote(result, tweet) {\n        var _a;\n        const quoteIds = ((_a = tweet.referenced_tweets) !== null && _a !== void 0 ? _a : [])\n            .filter(ref => ref.type === 'quoted')\n            .map(ref => ref.id);\n        return this.tweets(result).find(t => quoteIds.includes(t.id));\n    }\n    /** Tweet whose has been answered by the given tweet (*`referenced_tweets.id`*) */\n    repliedTo(tweet) {\n        return TwitterV2IncludesHelper.repliedTo(this.result, tweet);\n    }\n    /** Tweet whose has been answered by the given tweet (*`referenced_tweets.id`*) */\n    static repliedTo(result, tweet) {\n        var _a;\n        const repliesIds = ((_a = tweet.referenced_tweets) !== null && _a !== void 0 ? _a : [])\n            .filter(ref => ref.type === 'replied_to')\n            .map(ref => ref.id);\n        return this.tweets(result).find(t => repliesIds.includes(t.id));\n    }\n    /** Tweet author user object of the given tweet (*`author_id`* or *`referenced_tweets.id.author_id`*) */\n    author(tweet) {\n        return TwitterV2IncludesHelper.author(this.result, tweet);\n    }\n    /** Tweet author user object of the given tweet (*`author_id`* or *`referenced_tweets.id.author_id`*) */\n    static author(result, tweet) {\n        const authorId = tweet.author_id;\n        return authorId ? this.users(result).find(u => u.id === authorId) : undefined;\n    }\n    /** Tweet author user object of the tweet answered by the given tweet (*`in_reply_to_user_id`*) */\n    repliedToAuthor(tweet) {\n        return TwitterV2IncludesHelper.repliedToAuthor(this.result, tweet);\n    }\n    /** Tweet author user object of the tweet answered by the given tweet (*`in_reply_to_user_id`*) */\n    static repliedToAuthor(result, tweet) {\n        const inReplyUserId = tweet.in_reply_to_user_id;\n        return inReplyUserId ? this.users(result).find(u => u.id === inReplyUserId) : undefined;\n    }\n    /* Users */\n    get users() {\n        return TwitterV2IncludesHelper.users(this.result);\n    }\n    static users(result) {\n        var _a, _b;\n        return (_b = (_a = result.includes) === null || _a === void 0 ? void 0 : _a.users) !== null && _b !== void 0 ? _b : [];\n    }\n    userById(id) {\n        return TwitterV2IncludesHelper.userById(this.result, id);\n    }\n    static userById(result, id) {\n        return this.users(result).find(u => u.id === id);\n    }\n    /** Pinned tweet of the given user (*`pinned_tweet_id`*) */\n    pinnedTweet(user) {\n        return TwitterV2IncludesHelper.pinnedTweet(this.result, user);\n    }\n    /** Pinned tweet of the given user (*`pinned_tweet_id`*) */\n    static pinnedTweet(result, user) {\n        return user.pinned_tweet_id ? this.tweets(result).find(t => t.id === user.pinned_tweet_id) : undefined;\n    }\n    /* Medias */\n    get media() {\n        return TwitterV2IncludesHelper.media(this.result);\n    }\n    static media(result) {\n        var _a, _b;\n        return (_b = (_a = result.includes) === null || _a === void 0 ? void 0 : _a.media) !== null && _b !== void 0 ? _b : [];\n    }\n    /** Medias associated with the given tweet (*`attachments.media_keys`*) */\n    medias(tweet) {\n        return TwitterV2IncludesHelper.medias(this.result, tweet);\n    }\n    /** Medias associated with the given tweet (*`attachments.media_keys`*) */\n    static medias(result, tweet) {\n        var _a, _b;\n        const keys = (_b = (_a = tweet.attachments) === null || _a === void 0 ? void 0 : _a.media_keys) !== null && _b !== void 0 ? _b : [];\n        return this.media(result).filter(m => keys.includes(m.media_key));\n    }\n    /* Polls */\n    get polls() {\n        return TwitterV2IncludesHelper.polls(this.result);\n    }\n    static polls(result) {\n        var _a, _b;\n        return (_b = (_a = result.includes) === null || _a === void 0 ? void 0 : _a.polls) !== null && _b !== void 0 ? _b : [];\n    }\n    /** Poll associated with the given tweet (*`attachments.poll_ids`*) */\n    poll(tweet) {\n        return TwitterV2IncludesHelper.poll(this.result, tweet);\n    }\n    /** Poll associated with the given tweet (*`attachments.poll_ids`*) */\n    static poll(result, tweet) {\n        var _a, _b;\n        const pollIds = (_b = (_a = tweet.attachments) === null || _a === void 0 ? void 0 : _a.poll_ids) !== null && _b !== void 0 ? _b : [];\n        if (pollIds.length) {\n            const pollId = pollIds[0];\n            return this.polls(result).find(p => p.id === pollId);\n        }\n        return undefined;\n    }\n    /* Places */\n    get places() {\n        return TwitterV2IncludesHelper.places(this.result);\n    }\n    static places(result) {\n        var _a, _b;\n        return (_b = (_a = result.includes) === null || _a === void 0 ? void 0 : _a.places) !== null && _b !== void 0 ? _b : [];\n    }\n    /** Place associated with the given tweet (*`geo.place_id`*) */\n    place(tweet) {\n        return TwitterV2IncludesHelper.place(this.result, tweet);\n    }\n    /** Place associated with the given tweet (*`geo.place_id`*) */\n    static place(result, tweet) {\n        var _a;\n        const placeId = (_a = tweet.geo) === null || _a === void 0 ? void 0 : _a.place_id;\n        return placeId ? this.places(result).find(p => p.id === placeId) : undefined;\n    }\n    /* Lists */\n    /** List owner of the given list (*`owner_id`*) */\n    listOwner(list) {\n        return TwitterV2IncludesHelper.listOwner(this.result, list);\n    }\n    /** List owner of the given list (*`owner_id`*) */\n    static listOwner(result, list) {\n        const creatorId = list.owner_id;\n        return creatorId ? this.users(result).find(p => p.id === creatorId) : undefined;\n    }\n    /* Spaces */\n    /** Creator of the given space (*`creator_id`*) */\n    spaceCreator(space) {\n        return TwitterV2IncludesHelper.spaceCreator(this.result, space);\n    }\n    /** Creator of the given space (*`creator_id`*) */\n    static spaceCreator(result, space) {\n        const creatorId = space.creator_id;\n        return creatorId ? this.users(result).find(p => p.id === creatorId) : undefined;\n    }\n    /** Current hosts of the given space (*`host_ids`*) */\n    spaceHosts(space) {\n        return TwitterV2IncludesHelper.spaceHosts(this.result, space);\n    }\n    /** Current hosts of the given space (*`host_ids`*) */\n    static spaceHosts(result, space) {\n        var _a;\n        const hostIds = (_a = space.host_ids) !== null && _a !== void 0 ? _a : [];\n        return this.users(result).filter(u => hostIds.includes(u.id));\n    }\n    /** Current speakers of the given space (*`speaker_ids`*) */\n    spaceSpeakers(space) {\n        return TwitterV2IncludesHelper.spaceSpeakers(this.result, space);\n    }\n    /** Current speakers of the given space (*`speaker_ids`*) */\n    static spaceSpeakers(result, space) {\n        var _a;\n        const speakerIds = (_a = space.speaker_ids) !== null && _a !== void 0 ? _a : [];\n        return this.users(result).filter(u => speakerIds.includes(u.id));\n    }\n    /** Current invited users of the given space (*`invited_user_ids`*) */\n    spaceInvitedUsers(space) {\n        return TwitterV2IncludesHelper.spaceInvitedUsers(this.result, space);\n    }\n    /** Current invited users of the given space (*`invited_user_ids`*) */\n    static spaceInvitedUsers(result, space) {\n        var _a;\n        const invitedUserIds = (_a = space.invited_user_ids) !== null && _a !== void 0 ? _a : [];\n        return this.users(result).filter(u => invitedUserIds.includes(u.id));\n    }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AACM,MAAM;IACT,YAAY,MAAM,CAAE;QAChB,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,UAAU,GACV,IAAI,SAAS;QACT,OAAO,wBAAwB,MAAM,CAAC,IAAI,CAAC,MAAM;IACrD;IACA,OAAO,OAAO,MAAM,EAAE;QAClB,IAAI,IAAI;QACR,OAAO,CAAC,KAAK,CAAC,KAAK,OAAO,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;IAC3H;IACA,UAAU,EAAE,EAAE;QACV,OAAO,wBAAwB,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE;IAC1D;IACA,OAAO,UAAU,MAAM,EAAE,EAAE,EAAE;QACzB,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAC1D;IACA,uEAAuE,GACvE,QAAQ,KAAK,EAAE;QACX,OAAO,wBAAwB,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE;IACxD;IACA,uEAAuE,GACvE,OAAO,QAAQ,MAAM,EAAE,KAAK,EAAE;QAC1B,IAAI;QACJ,MAAM,aAAa,CAAC,CAAC,KAAK,MAAM,iBAAiB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE,EACjF,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,aAC3B,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;QACtB,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAA,IAAK,WAAW,QAAQ,CAAC,EAAE,EAAE;IACjE;IACA,4EAA4E,GAC5E,MAAM,KAAK,EAAE;QACT,OAAO,wBAAwB,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE;IACtD;IACA,4EAA4E,GAC5E,OAAO,MAAM,MAAM,EAAE,KAAK,EAAE;QACxB,IAAI;QACJ,MAAM,WAAW,CAAC,CAAC,KAAK,MAAM,iBAAiB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE,EAC/E,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,UAC3B,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;QACtB,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAA,IAAK,SAAS,QAAQ,CAAC,EAAE,EAAE;IAC/D;IACA,gFAAgF,GAChF,UAAU,KAAK,EAAE;QACb,OAAO,wBAAwB,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE;IAC1D;IACA,gFAAgF,GAChF,OAAO,UAAU,MAAM,EAAE,KAAK,EAAE;QAC5B,IAAI;QACJ,MAAM,aAAa,CAAC,CAAC,KAAK,MAAM,iBAAiB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE,EACjF,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,cAC3B,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;QACtB,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAA,IAAK,WAAW,QAAQ,CAAC,EAAE,EAAE;IACjE;IACA,sGAAsG,GACtG,OAAO,KAAK,EAAE;QACV,OAAO,wBAAwB,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE;IACvD;IACA,sGAAsG,GACtG,OAAO,OAAO,MAAM,EAAE,KAAK,EAAE;QACzB,MAAM,WAAW,MAAM,SAAS;QAChC,OAAO,WAAW,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY;IACxE;IACA,gGAAgG,GAChG,gBAAgB,KAAK,EAAE;QACnB,OAAO,wBAAwB,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE;IAChE;IACA,gGAAgG,GAChG,OAAO,gBAAgB,MAAM,EAAE,KAAK,EAAE;QAClC,MAAM,gBAAgB,MAAM,mBAAmB;QAC/C,OAAO,gBAAgB,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB;IAClF;IACA,SAAS,GACT,IAAI,QAAQ;QACR,OAAO,wBAAwB,KAAK,CAAC,IAAI,CAAC,MAAM;IACpD;IACA,OAAO,MAAM,MAAM,EAAE;QACjB,IAAI,IAAI;QACR,OAAO,CAAC,KAAK,CAAC,KAAK,OAAO,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;IAC1H;IACA,SAAS,EAAE,EAAE;QACT,OAAO,wBAAwB,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE;IACzD;IACA,OAAO,SAAS,MAAM,EAAE,EAAE,EAAE;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACjD;IACA,yDAAyD,GACzD,YAAY,IAAI,EAAE;QACd,OAAO,wBAAwB,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE;IAC5D;IACA,yDAAyD,GACzD,OAAO,YAAY,MAAM,EAAE,IAAI,EAAE;QAC7B,OAAO,KAAK,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,eAAe,IAAI;IACjG;IACA,UAAU,GACV,IAAI,QAAQ;QACR,OAAO,wBAAwB,KAAK,CAAC,IAAI,CAAC,MAAM;IACpD;IACA,OAAO,MAAM,MAAM,EAAE;QACjB,IAAI,IAAI;QACR,OAAO,CAAC,KAAK,CAAC,KAAK,OAAO,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;IAC1H;IACA,wEAAwE,GACxE,OAAO,KAAK,EAAE;QACV,OAAO,wBAAwB,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE;IACvD;IACA,wEAAwE,GACxE,OAAO,OAAO,MAAM,EAAE,KAAK,EAAE;QACzB,IAAI,IAAI;QACR,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,MAAM,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;QACnI,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,MAAM,CAAC,CAAA,IAAK,KAAK,QAAQ,CAAC,EAAE,SAAS;IACnE;IACA,SAAS,GACT,IAAI,QAAQ;QACR,OAAO,wBAAwB,KAAK,CAAC,IAAI,CAAC,MAAM;IACpD;IACA,OAAO,MAAM,MAAM,EAAE;QACjB,IAAI,IAAI;QACR,OAAO,CAAC,KAAK,CAAC,KAAK,OAAO,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;IAC1H;IACA,oEAAoE,GACpE,KAAK,KAAK,EAAE;QACR,OAAO,wBAAwB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;IACrD;IACA,oEAAoE,GACpE,OAAO,KAAK,MAAM,EAAE,KAAK,EAAE;QACvB,IAAI,IAAI;QACR,MAAM,UAAU,CAAC,KAAK,CAAC,KAAK,MAAM,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;QACpI,IAAI,QAAQ,MAAM,EAAE;YAChB,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACjD;QACA,OAAO;IACX;IACA,UAAU,GACV,IAAI,SAAS;QACT,OAAO,wBAAwB,MAAM,CAAC,IAAI,CAAC,MAAM;IACrD;IACA,OAAO,OAAO,MAAM,EAAE;QAClB,IAAI,IAAI;QACR,OAAO,CAAC,KAAK,CAAC,KAAK,OAAO,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;IAC3H;IACA,6DAA6D,GAC7D,MAAM,KAAK,EAAE;QACT,OAAO,wBAAwB,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE;IACtD;IACA,6DAA6D,GAC7D,OAAO,MAAM,MAAM,EAAE,KAAK,EAAE;QACxB,IAAI;QACJ,MAAM,UAAU,CAAC,KAAK,MAAM,GAAG,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ;QACjF,OAAO,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW;IACvE;IACA,SAAS,GACT,gDAAgD,GAChD,UAAU,IAAI,EAAE;QACZ,OAAO,wBAAwB,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE;IAC1D;IACA,gDAAgD,GAChD,OAAO,UAAU,MAAM,EAAE,IAAI,EAAE;QAC3B,MAAM,YAAY,KAAK,QAAQ;QAC/B,OAAO,YAAY,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa;IAC1E;IACA,UAAU,GACV,gDAAgD,GAChD,aAAa,KAAK,EAAE;QAChB,OAAO,wBAAwB,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE;IAC7D;IACA,gDAAgD,GAChD,OAAO,aAAa,MAAM,EAAE,KAAK,EAAE;QAC/B,MAAM,YAAY,MAAM,UAAU;QAClC,OAAO,YAAY,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa;IAC1E;IACA,oDAAoD,GACpD,WAAW,KAAK,EAAE;QACd,OAAO,wBAAwB,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE;IAC3D;IACA,oDAAoD,GACpD,OAAO,WAAW,MAAM,EAAE,KAAK,EAAE;QAC7B,IAAI;QACJ,MAAM,UAAU,CAAC,KAAK,MAAM,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;QACzE,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,MAAM,CAAC,CAAA,IAAK,QAAQ,QAAQ,CAAC,EAAE,EAAE;IAC/D;IACA,0DAA0D,GAC1D,cAAc,KAAK,EAAE;QACjB,OAAO,wBAAwB,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE;IAC9D;IACA,0DAA0D,GAC1D,OAAO,cAAc,MAAM,EAAE,KAAK,EAAE;QAChC,IAAI;QACJ,MAAM,aAAa,CAAC,KAAK,MAAM,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;QAC/E,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,MAAM,CAAC,CAAA,IAAK,WAAW,QAAQ,CAAC,EAAE,EAAE;IAClE;IACA,oEAAoE,GACpE,kBAAkB,KAAK,EAAE;QACrB,OAAO,wBAAwB,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE;IAClE;IACA,oEAAoE,GACpE,OAAO,kBAAkB,MAAM,EAAE,KAAK,EAAE;QACpC,IAAI;QACJ,MAAM,iBAAiB,CAAC,KAAK,MAAM,gBAAgB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;QACxF,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,MAAM,CAAC,CAAA,IAAK,eAAe,QAAQ,CAAC,EAAE,EAAE;IACtE;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4933, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/paginators/v2.paginator.js"], "sourcesContent": ["import { TwitterV2IncludesHelper } from '../v2/includes.v2.helper';\nimport { PreviousableTwitterPaginator } from './TwitterPaginator';\n/** A generic PreviousableTwitterPaginator with common v2 helper methods. */\nexport class TwitterV2Paginator extends PreviousableTwitterPaginator {\n    updateIncludes(data) {\n        // Update errors\n        if (data.errors) {\n            if (!this._realData.errors) {\n                this._realData.errors = [];\n            }\n            this._realData.errors = [...this._realData.errors, ...data.errors];\n        }\n        // Update includes\n        if (!data.includes) {\n            return;\n        }\n        if (!this._realData.includes) {\n            this._realData.includes = {};\n        }\n        const includesRealData = this._realData.includes;\n        for (const [includeKey, includeArray] of Object.entries(data.includes)) {\n            if (!includesRealData[includeKey]) {\n                includesRealData[includeKey] = [];\n            }\n            includesRealData[includeKey] = [\n                ...includesRealData[includeKey],\n                ...includeArray,\n            ];\n        }\n    }\n    /** Throw if the current paginator is not usable. */\n    assertUsable() {\n        if (this.unusable) {\n            throw new Error('Unable to use this paginator to fetch more data, as it does not contain any metadata.' +\n                ' Check .errors property for more details.');\n        }\n    }\n    get meta() {\n        return this._realData.meta;\n    }\n    get includes() {\n        var _a;\n        if (!((_a = this._realData) === null || _a === void 0 ? void 0 : _a.includes)) {\n            return new TwitterV2IncludesHelper(this._realData);\n        }\n        if (this._includesInstance) {\n            return this._includesInstance;\n        }\n        return this._includesInstance = new TwitterV2IncludesHelper(this._realData);\n    }\n    get errors() {\n        var _a;\n        return (_a = this._realData.errors) !== null && _a !== void 0 ? _a : [];\n    }\n    /** `true` if this paginator only contains error payload and no metadata found to consume data. */\n    get unusable() {\n        return this.errors.length > 0 && !this._realData.meta && !this._realData.data;\n    }\n}\n/** A generic TwitterV2Paginator able to consume v2 timelines that use max_results and pagination tokens. */\nexport class TimelineV2Paginator extends TwitterV2Paginator {\n    refreshInstanceFromResult(response, isNextPage) {\n        var _a;\n        const result = response.data;\n        const resultData = (_a = result.data) !== null && _a !== void 0 ? _a : [];\n        this._rateLimit = response.rateLimit;\n        if (!this._realData.data) {\n            this._realData.data = [];\n        }\n        if (isNextPage) {\n            this._realData.meta.result_count += result.meta.result_count;\n            this._realData.meta.next_token = result.meta.next_token;\n            this._realData.data.push(...resultData);\n        }\n        else {\n            this._realData.meta.result_count += result.meta.result_count;\n            this._realData.meta.previous_token = result.meta.previous_token;\n            this._realData.data.unshift(...resultData);\n        }\n        this.updateIncludes(result);\n    }\n    getNextQueryParams(maxResults) {\n        this.assertUsable();\n        return {\n            ...this.injectQueryParams(maxResults),\n            pagination_token: this._realData.meta.next_token,\n        };\n    }\n    getPreviousQueryParams(maxResults) {\n        this.assertUsable();\n        return {\n            ...this.injectQueryParams(maxResults),\n            pagination_token: this._realData.meta.previous_token,\n        };\n    }\n    getPageLengthFromRequest(result) {\n        var _a, _b;\n        return (_b = (_a = result.data.data) === null || _a === void 0 ? void 0 : _a.length) !== null && _b !== void 0 ? _b : 0;\n    }\n    isFetchLastOver(result) {\n        var _a;\n        return !((_a = result.data.data) === null || _a === void 0 ? void 0 : _a.length) || !this.canFetchNextPage(result.data);\n    }\n    canFetchNextPage(result) {\n        var _a;\n        return !!((_a = result.meta) === null || _a === void 0 ? void 0 : _a.next_token);\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,MAAM,2BAA2B,uLAAA,CAAA,+BAA4B;IAChE,eAAe,IAAI,EAAE;QACjB,gBAAgB;QAChB,IAAI,KAAK,MAAM,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;gBACxB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE;YAC9B;YACA,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG;mBAAI,IAAI,CAAC,SAAS,CAAC,MAAM;mBAAK,KAAK,MAAM;aAAC;QACtE;QACA,kBAAkB;QAClB,IAAI,CAAC,KAAK,QAAQ,EAAE;YAChB;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;YAC1B,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,CAAC;QAC/B;QACA,MAAM,mBAAmB,IAAI,CAAC,SAAS,CAAC,QAAQ;QAChD,KAAK,MAAM,CAAC,YAAY,aAAa,IAAI,OAAO,OAAO,CAAC,KAAK,QAAQ,EAAG;YACpE,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE;gBAC/B,gBAAgB,CAAC,WAAW,GAAG,EAAE;YACrC;YACA,gBAAgB,CAAC,WAAW,GAAG;mBACxB,gBAAgB,CAAC,WAAW;mBAC5B;aACN;QACL;IACJ;IACA,kDAAkD,GAClD,eAAe;QACX,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,MAAM,IAAI,MAAM,0FACZ;QACR;IACJ;IACA,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI;IAC9B;IACA,IAAI,WAAW;QACX,IAAI;QACJ,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,GAAG;YAC3E,OAAO,IAAI,uLAAA,CAAA,0BAAuB,CAAC,IAAI,CAAC,SAAS;QACrD;QACA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,OAAO,IAAI,CAAC,iBAAiB;QACjC;QACA,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,uLAAA,CAAA,0BAAuB,CAAC,IAAI,CAAC,SAAS;IAC9E;IACA,IAAI,SAAS;QACT,IAAI;QACJ,OAAO,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;IAC3E;IACA,gGAAgG,GAChG,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI;IACjF;AACJ;AAEO,MAAM,4BAA4B;IACrC,0BAA0B,QAAQ,EAAE,UAAU,EAAE;QAC5C,IAAI;QACJ,MAAM,SAAS,SAAS,IAAI;QAC5B,MAAM,aAAa,CAAC,KAAK,OAAO,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;QACzE,IAAI,CAAC,UAAU,GAAG,SAAS,SAAS;QACpC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;YACtB,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,EAAE;QAC5B;QACA,IAAI,YAAY;YACZ,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,IAAI,OAAO,IAAI,CAAC,YAAY;YAC5D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,GAAG,OAAO,IAAI,CAAC,UAAU;YACvD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI;QAChC,OACK;YACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,IAAI,OAAO,IAAI,CAAC,YAAY;YAC5D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,GAAG,OAAO,IAAI,CAAC,cAAc;YAC/D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,IAAI;QACnC;QACA,IAAI,CAAC,cAAc,CAAC;IACxB;IACA,mBAAmB,UAAU,EAAE;QAC3B,IAAI,CAAC,YAAY;QACjB,OAAO;YACH,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW;YACrC,kBAAkB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU;QACpD;IACJ;IACA,uBAAuB,UAAU,EAAE;QAC/B,IAAI,CAAC,YAAY;QACjB,OAAO;YACH,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW;YACrC,kBAAkB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc;QACxD;IACJ;IACA,yBAAyB,MAAM,EAAE;QAC7B,IAAI,IAAI;QACR,OAAO,CAAC,KAAK,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IAC1H;IACA,gBAAgB,MAAM,EAAE;QACpB,IAAI;QACJ,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,IAAI;IAC1H;IACA,iBAAiB,MAAM,EAAE;QACrB,IAAI;QACJ,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU;IACnF;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5050, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/paginators/tweet.paginator.v2.js"], "sourcesContent": ["import { TimelineV2Paginator, TwitterV2Paginator } from './v2.paginator';\n/** A generic PreviousableTwitterPaginator able to consume TweetV2 timelines with since_id, until_id and next_token (when available). */\nclass TweetTimelineV2Paginator extends TwitterV2Paginator {\n    refreshInstanceFromResult(response, isNextPage) {\n        var _a;\n        const result = response.data;\n        const resultData = (_a = result.data) !== null && _a !== void 0 ? _a : [];\n        this._rateLimit = response.rateLimit;\n        if (!this._realData.data) {\n            this._realData.data = [];\n        }\n        if (isNextPage) {\n            this._realData.meta.oldest_id = result.meta.oldest_id;\n            this._realData.meta.result_count += result.meta.result_count;\n            this._realData.meta.next_token = result.meta.next_token;\n            this._realData.data.push(...resultData);\n        }\n        else {\n            this._realData.meta.newest_id = result.meta.newest_id;\n            this._realData.meta.result_count += result.meta.result_count;\n            this._realData.data.unshift(...resultData);\n        }\n        this.updateIncludes(result);\n    }\n    getNextQueryParams(maxResults) {\n        this.assertUsable();\n        const params = { ...this.injectQueryParams(maxResults) };\n        if (this._realData.meta.next_token) {\n            params.next_token = this._realData.meta.next_token;\n        }\n        else {\n            if (params.start_time) {\n                // until_id and start_time are forbidden together for some reason, so convert start_time to a since_id.\n                params.since_id = this.dateStringToSnowflakeId(params.start_time);\n                delete params.start_time;\n            }\n            if (params.end_time) {\n                // until_id overrides end_time, so delete it\n                delete params.end_time;\n            }\n            params.until_id = this._realData.meta.oldest_id;\n        }\n        return params;\n    }\n    getPreviousQueryParams(maxResults) {\n        this.assertUsable();\n        return {\n            ...this.injectQueryParams(maxResults),\n            since_id: this._realData.meta.newest_id,\n        };\n    }\n    getPageLengthFromRequest(result) {\n        var _a, _b;\n        return (_b = (_a = result.data.data) === null || _a === void 0 ? void 0 : _a.length) !== null && _b !== void 0 ? _b : 0;\n    }\n    isFetchLastOver(result) {\n        var _a;\n        return !((_a = result.data.data) === null || _a === void 0 ? void 0 : _a.length) || !this.canFetchNextPage(result.data);\n    }\n    canFetchNextPage(result) {\n        return !!result.meta.next_token;\n    }\n    getItemArray() {\n        return this.tweets;\n    }\n    dateStringToSnowflakeId(dateStr) {\n        const TWITTER_START_EPOCH = BigInt('1288834974657');\n        const date = new Date(dateStr);\n        if (isNaN(date.valueOf())) {\n            throw new Error('Unable to convert start_time/end_time to a valid date. A ISO 8601 DateTime is excepted, please check your input.');\n        }\n        const dateTimestamp = BigInt(date.valueOf());\n        return ((dateTimestamp - TWITTER_START_EPOCH) << BigInt('22')).toString();\n    }\n    /**\n     * Tweets returned by paginator.\n     */\n    get tweets() {\n        var _a;\n        return (_a = this._realData.data) !== null && _a !== void 0 ? _a : [];\n    }\n    get meta() {\n        return super.meta;\n    }\n}\n/** A generic PreviousableTwitterPaginator able to consume TweetV2 timelines with pagination_tokens. */\nclass TweetPaginableTimelineV2Paginator extends TimelineV2Paginator {\n    refreshInstanceFromResult(response, isNextPage) {\n        super.refreshInstanceFromResult(response, isNextPage);\n        const result = response.data;\n        if (isNextPage) {\n            this._realData.meta.oldest_id = result.meta.oldest_id;\n        }\n        else {\n            this._realData.meta.newest_id = result.meta.newest_id;\n        }\n    }\n    getItemArray() {\n        return this.tweets;\n    }\n    /**\n     * Tweets returned by paginator.\n     */\n    get tweets() {\n        var _a;\n        return (_a = this._realData.data) !== null && _a !== void 0 ? _a : [];\n    }\n    get meta() {\n        return super.meta;\n    }\n}\n// ----------------\n// - Tweet search -\n// ----------------\nexport class TweetSearchRecentV2Paginator extends TweetTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'tweets/search/recent';\n    }\n}\nexport class TweetSearchAllV2Paginator extends TweetTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'tweets/search/all';\n    }\n}\nexport class QuotedTweetsTimelineV2Paginator extends TweetPaginableTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'tweets/:id/quote_tweets';\n    }\n}\n// -----------------\n// - Home timeline -\n// -----------------\nexport class TweetHomeTimelineV2Paginator extends TweetPaginableTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'users/:id/timelines/reverse_chronological';\n    }\n}\nexport class TweetUserTimelineV2Paginator extends TweetPaginableTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'users/:id/tweets';\n    }\n}\nexport class TweetUserMentionTimelineV2Paginator extends TweetPaginableTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'users/:id/mentions';\n    }\n}\n// -------------\n// - Bookmarks -\n// -------------\nexport class TweetBookmarksTimelineV2Paginator extends TweetPaginableTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'users/:id/bookmarks';\n    }\n}\n// ---------------------------------------------------------------------------------\n// - Tweet lists (consume tweets with pagination tokens instead of since/until id) -\n// ---------------------------------------------------------------------------------\n/** A generic TwitterPaginator able to consume TweetV2 timelines. */\nclass TweetListV2Paginator extends TimelineV2Paginator {\n    /**\n     * Tweets returned by paginator.\n     */\n    get tweets() {\n        var _a;\n        return (_a = this._realData.data) !== null && _a !== void 0 ? _a : [];\n    }\n    get meta() {\n        return super.meta;\n    }\n    getItemArray() {\n        return this.tweets;\n    }\n}\nexport class TweetV2UserLikedTweetsPaginator extends TweetListV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'users/:id/liked_tweets';\n    }\n}\nexport class TweetV2ListTweetsPaginator extends TweetListV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'lists/:id/tweets';\n    }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AACA,sIAAsI,GACtI,MAAM,iCAAiC,sLAAA,CAAA,qBAAkB;IACrD,0BAA0B,QAAQ,EAAE,UAAU,EAAE;QAC5C,IAAI;QACJ,MAAM,SAAS,SAAS,IAAI;QAC5B,MAAM,aAAa,CAAC,KAAK,OAAO,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;QACzE,IAAI,CAAC,UAAU,GAAG,SAAS,SAAS;QACpC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;YACtB,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,EAAE;QAC5B;QACA,IAAI,YAAY;YACZ,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,IAAI,CAAC,SAAS;YACrD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,IAAI,OAAO,IAAI,CAAC,YAAY;YAC5D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,GAAG,OAAO,IAAI,CAAC,UAAU;YACvD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI;QAChC,OACK;YACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,IAAI,CAAC,SAAS;YACrD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,IAAI,OAAO,IAAI,CAAC,YAAY;YAC5D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,IAAI;QACnC;QACA,IAAI,CAAC,cAAc,CAAC;IACxB;IACA,mBAAmB,UAAU,EAAE;QAC3B,IAAI,CAAC,YAAY;QACjB,MAAM,SAAS;YAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW;QAAC;QACvD,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE;YAChC,OAAO,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU;QACtD,OACK;YACD,IAAI,OAAO,UAAU,EAAE;gBACnB,uGAAuG;gBACvG,OAAO,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,UAAU;gBAChE,OAAO,OAAO,UAAU;YAC5B;YACA,IAAI,OAAO,QAAQ,EAAE;gBACjB,4CAA4C;gBAC5C,OAAO,OAAO,QAAQ;YAC1B;YACA,OAAO,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS;QACnD;QACA,OAAO;IACX;IACA,uBAAuB,UAAU,EAAE;QAC/B,IAAI,CAAC,YAAY;QACjB,OAAO;YACH,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW;YACrC,UAAU,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS;QAC3C;IACJ;IACA,yBAAyB,MAAM,EAAE;QAC7B,IAAI,IAAI;QACR,OAAO,CAAC,KAAK,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IAC1H;IACA,gBAAgB,MAAM,EAAE;QACpB,IAAI;QACJ,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,IAAI;IAC1H;IACA,iBAAiB,MAAM,EAAE;QACrB,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC,UAAU;IACnC;IACA,eAAe;QACX,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,wBAAwB,OAAO,EAAE;QAC7B,MAAM,sBAAsB,OAAO;QACnC,MAAM,OAAO,IAAI,KAAK;QACtB,IAAI,MAAM,KAAK,OAAO,KAAK;YACvB,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,gBAAgB,OAAO,KAAK,OAAO;QACzC,OAAO,CAAC,AAAC,gBAAgB,uBAAwB,OAAO,KAAK,EAAE,QAAQ;IAC3E;IACA;;KAEC,GACD,IAAI,SAAS;QACT,IAAI;QACJ,OAAO,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;IACzE;IACA,IAAI,OAAO;QACP,OAAO,KAAK,CAAC;IACjB;AACJ;AACA,qGAAqG,GACrG,MAAM,0CAA0C,sLAAA,CAAA,sBAAmB;IAC/D,0BAA0B,QAAQ,EAAE,UAAU,EAAE;QAC5C,KAAK,CAAC,0BAA0B,UAAU;QAC1C,MAAM,SAAS,SAAS,IAAI;QAC5B,IAAI,YAAY;YACZ,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,IAAI,CAAC,SAAS;QACzD,OACK;YACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,IAAI,CAAC,SAAS;QACzD;IACJ;IACA,eAAe;QACX,OAAO,IAAI,CAAC,MAAM;IACtB;IACA;;KAEC,GACD,IAAI,SAAS;QACT,IAAI;QACJ,OAAO,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;IACzE;IACA,IAAI,OAAO;QACP,OAAO,KAAK,CAAC;IACjB;AACJ;AAIO,MAAM,qCAAqC;IAC9C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,kCAAkC;IAC3C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,wCAAwC;IACjD,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AAIO,MAAM,qCAAqC;IAC9C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,qCAAqC;IAC9C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,4CAA4C;IACrD,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AAIO,MAAM,0CAA0C;IACnD,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACA,oFAAoF;AACpF,oFAAoF;AACpF,oFAAoF;AACpF,kEAAkE,GAClE,MAAM,6BAA6B,sLAAA,CAAA,sBAAmB;IAClD;;KAEC,GACD,IAAI,SAAS;QACT,IAAI;QACJ,OAAO,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;IACzE;IACA,IAAI,OAAO;QACP,OAAO,KAAK,CAAC;IACjB;IACA,eAAe;QACX,OAAO,IAAI,CAAC,MAAM;IACtB;AACJ;AACO,MAAM,wCAAwC;IACjD,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,mCAAmC;IAC5C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5245, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/paginators/user.paginator.v2.js"], "sourcesContent": ["import { TimelineV2Paginator } from './v2.paginator';\n/** A generic PreviousableTwitterPaginator able to consume UserV2 timelines. */\nclass UserTimelineV2Paginator extends TimelineV2Paginator {\n    getItemArray() {\n        return this.users;\n    }\n    /**\n     * Users returned by paginator.\n     */\n    get users() {\n        var _a;\n        return (_a = this._realData.data) !== null && _a !== void 0 ? _a : [];\n    }\n    get meta() {\n        return super.meta;\n    }\n}\nexport class UserBlockingUsersV2Paginator extends UserTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'users/:id/blocking';\n    }\n}\nexport class UserMutingUsersV2Paginator extends UserTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'users/:id/muting';\n    }\n}\nexport class UserFollowersV2Paginator extends UserTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'users/:id/followers';\n    }\n}\nexport class UserFollowingV2Paginator extends UserTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'users/:id/following';\n    }\n}\nexport class UserListMembersV2Paginator extends UserTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'lists/:id/members';\n    }\n}\nexport class UserListFollowersV2Paginator extends UserTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'lists/:id/followers';\n    }\n}\nexport class TweetLikingUsersV2Paginator extends UserTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'tweets/:id/liking_users';\n    }\n}\nexport class TweetRetweetersUsersV2Paginator extends UserTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'tweets/:id/retweeted_by';\n    }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACA,6EAA6E,GAC7E,MAAM,gCAAgC,sLAAA,CAAA,sBAAmB;IACrD,eAAe;QACX,OAAO,IAAI,CAAC,KAAK;IACrB;IACA;;KAEC,GACD,IAAI,QAAQ;QACR,IAAI;QACJ,OAAO,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;IACzE;IACA,IAAI,OAAO;QACP,OAAO,KAAK,CAAC;IACjB;AACJ;AACO,MAAM,qCAAqC;IAC9C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,mCAAmC;IAC5C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,iCAAiC;IAC1C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,iCAAiC;IAC1C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,mCAAmC;IAC5C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,qCAAqC;IAC9C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,oCAAoC;IAC7C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,wCAAwC;IACjD,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5325, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/paginators/list.paginator.v2.js"], "sourcesContent": ["import { TimelineV2Paginator } from './v2.paginator';\nclass ListTimelineV2Paginator extends TimelineV2Paginator {\n    getItemArray() {\n        return this.lists;\n    }\n    /**\n     * Lists returned by paginator.\n     */\n    get lists() {\n        var _a;\n        return (_a = this._realData.data) !== null && _a !== void 0 ? _a : [];\n    }\n    get meta() {\n        return super.meta;\n    }\n}\nexport class UserOwnedListsV2Paginator extends ListTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'users/:id/owned_lists';\n    }\n}\nexport class UserListMembershipsV2Paginator extends ListTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'users/:id/list_memberships';\n    }\n}\nexport class UserListFollowedV2Paginator extends ListTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'users/:id/followed_lists';\n    }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AACA,MAAM,gCAAgC,sLAAA,CAAA,sBAAmB;IACrD,eAAe;QACX,OAAO,IAAI,CAAC,KAAK;IACrB;IACA;;KAEC,GACD,IAAI,QAAQ;QACR,IAAI;QACJ,OAAO,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;IACzE;IACA,IAAI,OAAO;QACP,OAAO,KAAK,CAAC;IACjB;AACJ;AACO,MAAM,kCAAkC;IAC3C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,uCAAuC;IAChD,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,oCAAoC;IAC7C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5370, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/paginators/index.js"], "sourcesContent": ["export * from './tweet.paginator.v2';\nexport * from './TwitterPaginator';\nexport * from './dm.paginator.v1';\nexport * from './mutes.paginator.v1';\nexport * from './tweet.paginator.v1';\nexport * from './user.paginator.v1';\nexport * from './user.paginator.v2';\nexport * from './list.paginator.v1';\nexport * from './list.paginator.v2';\nexport * from './friends.paginator.v1';\nexport * from './followers.paginator.v1';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5418, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/v2-labs/client.v2.labs.read.js"], "sourcesContent": ["import TwitterApiSubClient from '../client.subclient';\nimport { API_V2_LABS_PREFIX } from '../globals';\n/**\n * Base Twitter v2 labs client with only read right.\n */\nexport default class TwitterApiv2LabsReadOnly extends TwitterApiSubClient {\n    constructor() {\n        super(...arguments);\n        this._prefix = API_V2_LABS_PREFIX;\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAIe,MAAM,iCAAiC,4KAAA,CAAA,UAAmB;IACrE,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,OAAO,GAAG,gKAAA,CAAA,qBAAkB;IACrC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5437, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/paginators/dm.paginator.v2.js"], "sourcesContent": ["import { TimelineV2Paginator } from './v2.paginator';\nexport class DMTimelineV2Paginator extends TimelineV2Paginator {\n    getItemArray() {\n        return this.events;\n    }\n    /**\n     * Events returned by paginator.\n     */\n    get events() {\n        var _a;\n        return (_a = this._realData.data) !== null && _a !== void 0 ? _a : [];\n    }\n    get meta() {\n        return super.meta;\n    }\n}\nexport class FullDMTimelineV2Paginator extends DMTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'dm_events';\n    }\n}\nexport class OneToOneDMTimelineV2Paginator extends DMTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'dm_conversations/with/:participant_id/dm_events';\n    }\n}\nexport class ConversationDMTimelineV2Paginator extends DMTimelineV2Paginator {\n    constructor() {\n        super(...arguments);\n        this._endpoint = 'dm_conversations/:dm_conversation_id/dm_events';\n    }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,8BAA8B,sLAAA,CAAA,sBAAmB;IAC1D,eAAe;QACX,OAAO,IAAI,CAAC,MAAM;IACtB;IACA;;KAEC,GACD,IAAI,SAAS;QACT,IAAI;QACJ,OAAO,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;IACzE;IACA,IAAI,OAAO;QACP,OAAO,KAAK,CAAC;IACjB;AACJ;AACO,MAAM,kCAAkC;IAC3C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,sCAAsC;IAC/C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ;AACO,MAAM,0CAA0C;IACnD,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;IACrB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5483, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/v2/client.v2.read.js"], "sourcesContent": ["import TwitterApiSubClient from '../client.subclient';\nimport { API_V2_PREFIX } from '../globals';\nimport { TweetSearchAllV2Paginator, TweetSearchRecentV2Paginator, TweetUserMentionTimelineV2Paginator, TweetUserTimelineV2Paginator, TweetV2UserLikedTweetsPaginator, UserOwnedListsV2Paginator, UserListMembershipsV2Paginator, UserListFollowedV2Paginator, TweetV2ListTweetsPaginator, TweetBookmarksTimelineV2Paginator, QuotedTweetsTimelineV2Paginator, TweetHomeTimelineV2Paginator, } from '../paginators';\nimport TwitterApiv2LabsReadOnly from '../v2-labs/client.v2.labs.read';\nimport { TweetLikingUsersV2Paginator, TweetRetweetersUsersV2Paginator, UserBlockingUsersV2Paginator, UserFollowersV2Paginator, UserFollowingV2Paginator, UserListFollowersV2Paginator, UserListMembersV2Paginator, UserMutingUsersV2Paginator } from '../paginators/user.paginator.v2';\nimport { isTweetStreamV2ErrorPayload } from '../helpers';\nimport { ConversationDMTimelineV2Paginator, FullDMTimelineV2Paginator, OneToOneDMTimelineV2Paginator } from '../paginators/dm.paginator.v2';\n/**\n * Base Twitter v2 client with only read right.\n */\nexport default class TwitterApiv2ReadOnly extends TwitterApiSubClient {\n    constructor() {\n        super(...arguments);\n        this._prefix = API_V2_PREFIX;\n    }\n    /* Sub-clients */\n    /**\n     * Get a client for v2 labs endpoints.\n     */\n    get labs() {\n        if (this._labs)\n            return this._labs;\n        return this._labs = new TwitterApiv2LabsReadOnly(this);\n    }\n    async search(queryOrOptions, options = {}) {\n        const queryParams = typeof queryOrOptions === 'string' ?\n            { ...options, query: queryOrOptions } :\n            { ...queryOrOptions };\n        const initialRq = await this.get('tweets/search/recent', queryParams, { fullResponse: true });\n        return new TweetSearchRecentV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * The full-archive search endpoint returns the complete history of public Tweets matching a search query;\n     * since the first Tweet was created March 26, 2006.\n     *\n     * This endpoint is only available to those users who have been approved for the Academic Research product track.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/search/api-reference/get-tweets-search-all\n     */\n    async searchAll(query, options = {}) {\n        const queryParams = { ...options, query };\n        const initialRq = await this.get('tweets/search/all', queryParams, { fullResponse: true });\n        return new TweetSearchAllV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams,\n        });\n    }\n    /**\n     * Returns a variety of information about a single Tweet specified by the requested ID.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/lookup/api-reference/get-tweets-id\n     *\n     * OAuth2 scope: `users.read`, `tweet.read`\n     */\n    singleTweet(tweetId, options = {}) {\n        return this.get('tweets/:id', options, { params: { id: tweetId } });\n    }\n    /**\n     * Returns a variety of information about tweets specified by list of IDs.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/lookup/api-reference/get-tweets\n     *\n     * OAuth2 scope: `users.read`, `tweet.read`\n     */\n    tweets(tweetIds, options = {}) {\n        return this.get('tweets', { ids: tweetIds, ...options });\n    }\n    /**\n     * The recent Tweet counts endpoint returns count of Tweets from the last seven days that match a search query.\n     * OAuth2 Bearer auth only.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/counts/api-reference/get-tweets-counts-recent\n     */\n    tweetCountRecent(query, options = {}) {\n        return this.get('tweets/counts/recent', { query, ...options });\n    }\n    /**\n     * This endpoint is only available to those users who have been approved for the Academic Research product track.\n     * The full-archive search endpoint returns the complete history of public Tweets matching a search query;\n     * since the first Tweet was created March 26, 2006.\n     * OAuth2 Bearer auth only.\n     * **This endpoint has pagination, yet it is not supported by bundled paginators. Use `next_token` to fetch next page.**\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/counts/api-reference/get-tweets-counts-all\n     */\n    tweetCountAll(query, options = {}) {\n        return this.get('tweets/counts/all', { query, ...options });\n    }\n    async tweetRetweetedBy(tweetId, options = {}) {\n        const { asPaginator, ...parameters } = options;\n        const initialRq = await this.get('tweets/:id/retweeted_by', parameters, {\n            fullResponse: true,\n            params: { id: tweetId },\n        });\n        if (!asPaginator) {\n            return initialRq.data;\n        }\n        return new TweetRetweetersUsersV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: parameters,\n            sharedParams: { id: tweetId },\n        });\n    }\n    async tweetLikedBy(tweetId, options = {}) {\n        const { asPaginator, ...parameters } = options;\n        const initialRq = await this.get('tweets/:id/liking_users', parameters, {\n            fullResponse: true,\n            params: { id: tweetId },\n        });\n        if (!asPaginator) {\n            return initialRq.data;\n        }\n        return new TweetLikingUsersV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: parameters,\n            sharedParams: { id: tweetId },\n        });\n    }\n    /**\n     * Allows you to retrieve a collection of the most recent Tweets and Retweets posted by you and users you follow, also known as home timeline.\n     * This endpoint returns up to the last 3200 Tweets.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/timelines/api-reference/get-users-id-reverse-chronological\n     *\n     * OAuth 2 scopes: `tweet.read` `users.read`\n     */\n    async homeTimeline(options = {}) {\n        const meUser = await this.getCurrentUserV2Object();\n        const initialRq = await this.get('users/:id/timelines/reverse_chronological', options, {\n            fullResponse: true,\n            params: { id: meUser.data.id },\n        });\n        return new TweetHomeTimelineV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: options,\n            sharedParams: { id: meUser.data.id },\n        });\n    }\n    /**\n     * Returns Tweets composed by a single user, specified by the requested user ID.\n     * By default, the most recent ten Tweets are returned per request.\n     * Using pagination, the most recent 3,200 Tweets can be retrieved.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/timelines/api-reference/get-users-id-tweets\n     */\n    async userTimeline(userId, options = {}) {\n        const initialRq = await this.get('users/:id/tweets', options, {\n            fullResponse: true,\n            params: { id: userId },\n        });\n        return new TweetUserTimelineV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: options,\n            sharedParams: { id: userId },\n        });\n    }\n    /**\n     * Returns Tweets mentioning a single user specified by the requested user ID.\n     * By default, the most recent ten Tweets are returned per request.\n     * Using pagination, up to the most recent 800 Tweets can be retrieved.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/timelines/api-reference/get-users-id-mentions\n     */\n    async userMentionTimeline(userId, options = {}) {\n        const initialRq = await this.get('users/:id/mentions', options, {\n            fullResponse: true,\n            params: { id: userId },\n        });\n        return new TweetUserMentionTimelineV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: options,\n            sharedParams: { id: userId },\n        });\n    }\n    /**\n     * Returns Quote Tweets for a Tweet specified by the requested Tweet ID.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/quote-tweets/api-reference/get-tweets-id-quote_tweets\n     *\n     * OAuth2 scopes: `users.read` `tweet.read`\n     */\n    async quotes(tweetId, options = {}) {\n        const initialRq = await this.get('tweets/:id/quote_tweets', options, {\n            fullResponse: true,\n            params: { id: tweetId },\n        });\n        return new QuotedTweetsTimelineV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: options,\n            sharedParams: { id: tweetId },\n        });\n    }\n    /* Bookmarks */\n    /**\n     * Allows you to get information about a authenticated user’s 800 most recent bookmarked Tweets.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/bookmarks/api-reference/get-users-id-bookmarks\n     *\n     * OAuth2 scopes: `users.read` `tweet.read` `bookmark.read`\n     */\n    async bookmarks(options = {}) {\n        const user = await this.getCurrentUserV2Object();\n        const initialRq = await this.get('users/:id/bookmarks', options, {\n            fullResponse: true,\n            params: { id: user.data.id },\n        });\n        return new TweetBookmarksTimelineV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: options,\n            sharedParams: { id: user.data.id },\n        });\n    }\n    /* Users */\n    /**\n     * Returns information about an authorized user.\n     * https://developer.twitter.com/en/docs/twitter-api/users/lookup/api-reference/get-users-me\n     *\n     * OAuth2 scopes: `tweet.read` & `users.read`\n     */\n    me(options = {}) {\n        return this.get('users/me', options);\n    }\n    /**\n     * Returns a variety of information about a single user specified by the requested ID.\n     * https://developer.twitter.com/en/docs/twitter-api/users/lookup/api-reference/get-users-id\n     */\n    user(userId, options = {}) {\n        return this.get('users/:id', options, { params: { id: userId } });\n    }\n    /**\n     * Returns a variety of information about one or more users specified by the requested IDs.\n     * https://developer.twitter.com/en/docs/twitter-api/users/lookup/api-reference/get-users\n     */\n    users(userIds, options = {}) {\n        const ids = Array.isArray(userIds) ? userIds.join(',') : userIds;\n        return this.get('users', { ...options, ids });\n    }\n    /**\n     * Returns a variety of information about a single user specified by their username.\n     * https://developer.twitter.com/en/docs/twitter-api/users/lookup/api-reference/get-users-by-username-username\n     */\n    userByUsername(username, options = {}) {\n        return this.get('users/by/username/:username', options, { params: { username } });\n    }\n    /**\n     * Returns a variety of information about one or more users specified by their usernames.\n     * https://developer.twitter.com/en/docs/twitter-api/users/lookup/api-reference/get-users-by\n     *\n     * OAuth2 scope: `users.read`, `tweet.read`\n     */\n    usersByUsernames(usernames, options = {}) {\n        usernames = Array.isArray(usernames) ? usernames.join(',') : usernames;\n        return this.get('users/by', { ...options, usernames });\n    }\n    async followers(userId, options = {}) {\n        const { asPaginator, ...parameters } = options;\n        const params = { id: userId };\n        if (!asPaginator) {\n            return this.get('users/:id/followers', parameters, { params });\n        }\n        const initialRq = await this.get('users/:id/followers', parameters, { fullResponse: true, params });\n        return new UserFollowersV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: parameters,\n            sharedParams: params,\n        });\n    }\n    async following(userId, options = {}) {\n        const { asPaginator, ...parameters } = options;\n        const params = { id: userId };\n        if (!asPaginator) {\n            return this.get('users/:id/following', parameters, { params });\n        }\n        const initialRq = await this.get('users/:id/following', parameters, { fullResponse: true, params });\n        return new UserFollowingV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: parameters,\n            sharedParams: params,\n        });\n    }\n    /**\n     * Allows you to get information about a user’s liked Tweets.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/likes/api-reference/get-users-id-liked_tweets\n     */\n    async userLikedTweets(userId, options = {}) {\n        const params = { id: userId };\n        const initialRq = await this.get('users/:id/liked_tweets', options, { fullResponse: true, params });\n        return new TweetV2UserLikedTweetsPaginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: { ...options },\n            sharedParams: params,\n        });\n    }\n    /**\n     * Returns a list of users who are blocked by the authenticating user.\n     * https://developer.twitter.com/en/docs/twitter-api/users/blocks/api-reference/get-users-blocking\n     */\n    async userBlockingUsers(userId, options = {}) {\n        const params = { id: userId };\n        const initialRq = await this.get('users/:id/blocking', options, { fullResponse: true, params });\n        return new UserBlockingUsersV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: { ...options },\n            sharedParams: params,\n        });\n    }\n    /**\n     * Returns a list of users who are muted by the authenticating user.\n     * https://developer.twitter.com/en/docs/twitter-api/users/mutes/api-reference/get-users-muting\n     */\n    async userMutingUsers(userId, options = {}) {\n        const params = { id: userId };\n        const initialRq = await this.get('users/:id/muting', options, { fullResponse: true, params });\n        return new UserMutingUsersV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: { ...options },\n            sharedParams: params,\n        });\n    }\n    /* Lists */\n    /**\n     * Returns the details of a specified List.\n     * https://developer.twitter.com/en/docs/twitter-api/lists/list-lookup/api-reference/get-lists-id\n     */\n    list(id, options = {}) {\n        return this.get('lists/:id', options, { params: { id } });\n    }\n    /**\n     * Returns all Lists owned by the specified user.\n     * https://developer.twitter.com/en/docs/twitter-api/lists/list-lookup/api-reference/get-users-id-owned_lists\n     */\n    async listsOwned(userId, options = {}) {\n        const params = { id: userId };\n        const initialRq = await this.get('users/:id/owned_lists', options, { fullResponse: true, params });\n        return new UserOwnedListsV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: { ...options },\n            sharedParams: params,\n        });\n    }\n    /**\n     * Returns all Lists a specified user is a member of.\n     * https://developer.twitter.com/en/docs/twitter-api/lists/list-members/api-reference/get-users-id-list_memberships\n     */\n    async listMemberships(userId, options = {}) {\n        const params = { id: userId };\n        const initialRq = await this.get('users/:id/list_memberships', options, { fullResponse: true, params });\n        return new UserListMembershipsV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: { ...options },\n            sharedParams: params,\n        });\n    }\n    /**\n     * Returns all Lists a specified user follows.\n     * https://developer.twitter.com/en/docs/twitter-api/lists/list-follows/api-reference/get-users-id-followed_lists\n     */\n    async listFollowed(userId, options = {}) {\n        const params = { id: userId };\n        const initialRq = await this.get('users/:id/followed_lists', options, { fullResponse: true, params });\n        return new UserListFollowedV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: { ...options },\n            sharedParams: params,\n        });\n    }\n    /**\n     * Returns a list of Tweets from the specified List.\n     * https://developer.twitter.com/en/docs/twitter-api/lists/list-tweets/api-reference/get-lists-id-tweets\n     */\n    async listTweets(listId, options = {}) {\n        const params = { id: listId };\n        const initialRq = await this.get('lists/:id/tweets', options, { fullResponse: true, params });\n        return new TweetV2ListTweetsPaginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: { ...options },\n            sharedParams: params,\n        });\n    }\n    /**\n     * Returns a list of users who are members of the specified List.\n     * https://developer.twitter.com/en/docs/twitter-api/lists/list-members/api-reference/get-lists-id-members\n     */\n    async listMembers(listId, options = {}) {\n        const params = { id: listId };\n        const initialRq = await this.get('lists/:id/members', options, { fullResponse: true, params });\n        return new UserListMembersV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: { ...options },\n            sharedParams: params,\n        });\n    }\n    /**\n     * Returns a list of users who are followers of the specified List.\n     * https://developer.twitter.com/en/docs/twitter-api/lists/list-follows/api-reference/get-lists-id-followers\n     */\n    async listFollowers(listId, options = {}) {\n        const params = { id: listId };\n        const initialRq = await this.get('lists/:id/followers', options, { fullResponse: true, params });\n        return new UserListFollowersV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: { ...options },\n            sharedParams: params,\n        });\n    }\n    /* Direct messages */\n    /**\n     * Returns a list of Direct Messages for the authenticated user, both sent and received.\n     * Direct Message events are returned in reverse chronological order.\n     * Supports retrieving events from the previous 30 days.\n     *\n     * OAuth 2 scopes: `dm.read`, `tweet.read`, `user.read`\n     *\n     * https://developer.twitter.com/en/docs/twitter-api/direct-messages/lookup/api-reference/get-dm_events\n     */\n    async listDmEvents(options = {}) {\n        const initialRq = await this.get('dm_events', options, { fullResponse: true });\n        return new FullDMTimelineV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: { ...options },\n        });\n    }\n    /**\n     * Returns a list of Direct Messages (DM) events within a 1-1 conversation with the user specified in the participant_id path parameter.\n     * Messages are returned in reverse chronological order.\n     *\n     * OAuth 2 scopes: `dm.read`, `tweet.read`, `user.read`\n     *\n     * https://developer.twitter.com/en/docs/twitter-api/direct-messages/lookup/api-reference/get-dm_conversations-dm_conversation_id-dm_events\n     */\n    async listDmEventsWithParticipant(participantId, options = {}) {\n        const params = { participant_id: participantId };\n        const initialRq = await this.get('dm_conversations/with/:participant_id/dm_events', options, { fullResponse: true, params });\n        return new OneToOneDMTimelineV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: { ...options },\n            sharedParams: params,\n        });\n    }\n    /**\n     * Returns a list of Direct Messages within a conversation specified in the dm_conversation_id path parameter.\n     * Messages are returned in reverse chronological order.\n     *\n     * OAuth 2 scopes: `dm.read`, `tweet.read`, `user.read`\n     *\n     * https://developer.twitter.com/en/docs/twitter-api/direct-messages/lookup/api-reference/get-dm_conversations-dm_conversation_id-dm_events\n     */\n    async listDmEventsOfConversation(dmConversationId, options = {}) {\n        const params = { dm_conversation_id: dmConversationId };\n        const initialRq = await this.get('dm_conversations/:dm_conversation_id/dm_events', options, { fullResponse: true, params });\n        return new ConversationDMTimelineV2Paginator({\n            realData: initialRq.data,\n            rateLimit: initialRq.rateLimit,\n            instance: this,\n            queryParams: { ...options },\n            sharedParams: params,\n        });\n    }\n    /* Spaces */\n    /**\n     * Get a single space by ID.\n     * https://developer.twitter.com/en/docs/twitter-api/spaces/lookup/api-reference/get-spaces-id\n     *\n     * OAuth2 scopes: `tweet.read`, `users.read`, `space.read`.\n     */\n    space(spaceId, options = {}) {\n        return this.get('spaces/:id', options, { params: { id: spaceId } });\n    }\n    /**\n     * Get spaces using their IDs.\n     * https://developer.twitter.com/en/docs/twitter-api/spaces/lookup/api-reference/get-spaces\n     *\n     * OAuth2 scopes: `tweet.read`, `users.read`, `space.read`.\n     */\n    spaces(spaceIds, options = {}) {\n        return this.get('spaces', { ids: spaceIds, ...options });\n    }\n    /**\n     * Get spaces using their creator user ID(s). (no pagination available)\n     * https://developer.twitter.com/en/docs/twitter-api/spaces/lookup/api-reference/get-spaces-by-creator-ids\n     *\n     * OAuth2 scopes: `tweet.read`, `users.read`, `space.read`.\n     */\n    spacesByCreators(creatorIds, options = {}) {\n        return this.get('spaces/by/creator_ids', { user_ids: creatorIds, ...options });\n    }\n    /**\n     * Search through spaces using multiple params. (no pagination available)\n     * https://developer.twitter.com/en/docs/twitter-api/spaces/search/api-reference/get-spaces-search\n     */\n    searchSpaces(options) {\n        return this.get('spaces/search', options);\n    }\n    /**\n    * Returns a list of user who purchased a ticket to the requested Space.\n    * You must authenticate the request using the Access Token of the creator of the requested Space.\n    *\n    * **OAuth 2.0 Access Token required**\n    *\n    * https://developer.twitter.com/en/docs/twitter-api/spaces/lookup/api-reference/get-spaces-id-buyers\n    *\n    * OAuth2 scopes: `tweet.read`, `users.read`, `space.read`.\n    */\n    spaceBuyers(spaceId, options = {}) {\n        return this.get('spaces/:id/buyers', options, { params: { id: spaceId } });\n    }\n    /**\n     * Returns Tweets shared in the requested Spaces.\n     * https://developer.twitter.com/en/docs/twitter-api/spaces/lookup/api-reference/get-spaces-id-tweets\n     *\n     * OAuth2 scope: `users.read`, `tweet.read`, `space.read`\n     */\n    spaceTweets(spaceId, options = {}) {\n        return this.get('spaces/:id/tweets', options, { params: { id: spaceId } });\n    }\n    searchStream({ autoConnect, ...options } = {}) {\n        return this.getStream('tweets/search/stream', options, { payloadIsError: isTweetStreamV2ErrorPayload, autoConnect });\n    }\n    /**\n     * Return a list of rules currently active on the streaming endpoint, either as a list or individually.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/filtered-stream/api-reference/get-tweets-search-stream-rules\n     */\n    streamRules(options = {}) {\n        return this.get('tweets/search/stream/rules', options);\n    }\n    updateStreamRules(options, query = {}) {\n        return this.post('tweets/search/stream/rules', options, { query });\n    }\n    sampleStream({ autoConnect, ...options } = {}) {\n        return this.getStream('tweets/sample/stream', options, { payloadIsError: isTweetStreamV2ErrorPayload, autoConnect });\n    }\n    sample10Stream({ autoConnect, ...options } = {}) {\n        return this.getStream('tweets/sample10/stream', options, { payloadIsError: isTweetStreamV2ErrorPayload, autoConnect });\n    }\n    /* Batch compliance */\n    /**\n     * Returns a list of recent compliance jobs.\n     * https://developer.twitter.com/en/docs/twitter-api/compliance/batch-compliance/api-reference/get-compliance-jobs\n     */\n    complianceJobs(options) {\n        return this.get('compliance/jobs', options);\n    }\n    /**\n     * Get a single compliance job with the specified ID.\n     * https://developer.twitter.com/en/docs/twitter-api/compliance/batch-compliance/api-reference/get-compliance-jobs-id\n     */\n    complianceJob(jobId) {\n        return this.get('compliance/jobs/:id', undefined, { params: { id: jobId } });\n    }\n    /**\n     * Creates a new compliance job for Tweet IDs or user IDs, send your file, await result and parse it into an array.\n     * You can run one batch job at a time. Returns the created job, but **not the job result!**.\n     *\n     * You can obtain the result (**after job is completed**) with `.complianceJobResult`.\n     * https://developer.twitter.com/en/docs/twitter-api/compliance/batch-compliance/api-reference/post-compliance-jobs\n     */\n    async sendComplianceJob(jobParams) {\n        const job = await this.post('compliance/jobs', { type: jobParams.type, name: jobParams.name });\n        // Send the IDs\n        const rawIdsBody = jobParams.ids instanceof Buffer ? jobParams.ids : Buffer.from(jobParams.ids.join('\\n'));\n        // Upload the IDs\n        await this.put(job.data.upload_url, rawIdsBody, {\n            forceBodyMode: 'raw',\n            enableAuth: false,\n            headers: { 'Content-Type': 'text/plain' },\n            prefix: '',\n        });\n        return job;\n    }\n    /**\n     * Get the result of a running or completed job, obtained through `.complianceJob`, `.complianceJobs` or `.sendComplianceJob`.\n     * If job is still running (`in_progress`), it will await until job is completed. **This could be quite long!**\n     * https://developer.twitter.com/en/docs/twitter-api/compliance/batch-compliance/api-reference/post-compliance-jobs\n     */\n    async complianceJobResult(job) {\n        let runningJob = job;\n        while (runningJob.status !== 'complete') {\n            if (runningJob.status === 'expired' || runningJob.status === 'failed') {\n                throw new Error('Job failed to be completed.');\n            }\n            await new Promise(resolve => setTimeout(resolve, 3500));\n            runningJob = (await this.complianceJob(job.id)).data;\n        }\n        // Download and parse result\n        const result = await this.get(job.download_url, undefined, {\n            enableAuth: false,\n            prefix: '',\n        });\n        return result\n            .trim()\n            .split('\\n')\n            .filter(line => line)\n            .map(line => JSON.parse(line));\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AAIe,MAAM,6BAA6B,4KAAA,CAAA,UAAmB;IACjE,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,OAAO,GAAG,gKAAA,CAAA,gBAAa;IAChC;IACA,eAAe,GACf;;KAEC,GACD,IAAI,OAAO;QACP,IAAI,IAAI,CAAC,KAAK,EACV,OAAO,IAAI,CAAC,KAAK;QACrB,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,mMAAA,CAAA,UAAwB,CAAC,IAAI;IACzD;IACA,MAAM,OAAO,cAAc,EAAE,UAAU,CAAC,CAAC,EAAE;QACvC,MAAM,cAAc,OAAO,mBAAmB,WAC1C;YAAE,GAAG,OAAO;YAAE,OAAO;QAAe,IACpC;YAAE,GAAG,cAAc;QAAC;QACxB,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,wBAAwB,aAAa;YAAE,cAAc;QAAK;QAC3F,OAAO,IAAI,+LAAA,CAAA,+BAA4B,CAAC;YACpC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;;;;KAMC,GACD,MAAM,UAAU,KAAK,EAAE,UAAU,CAAC,CAAC,EAAE;QACjC,MAAM,cAAc;YAAE,GAAG,OAAO;YAAE;QAAM;QACxC,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,qBAAqB,aAAa;YAAE,cAAc;QAAK;QACxF,OAAO,IAAI,+LAAA,CAAA,4BAAyB,CAAC;YACjC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd;QACJ;IACJ;IACA;;;;;KAKC,GACD,YAAY,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE;QAC/B,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,SAAS;YAAE,QAAQ;gBAAE,IAAI;YAAQ;QAAE;IACrE;IACA;;;;;KAKC,GACD,OAAO,QAAQ,EAAE,UAAU,CAAC,CAAC,EAAE;QAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU;YAAE,KAAK;YAAU,GAAG,OAAO;QAAC;IAC1D;IACA;;;;KAIC,GACD,iBAAiB,KAAK,EAAE,UAAU,CAAC,CAAC,EAAE;QAClC,OAAO,IAAI,CAAC,GAAG,CAAC,wBAAwB;YAAE;YAAO,GAAG,OAAO;QAAC;IAChE;IACA;;;;;;;KAOC,GACD,cAAc,KAAK,EAAE,UAAU,CAAC,CAAC,EAAE;QAC/B,OAAO,IAAI,CAAC,GAAG,CAAC,qBAAqB;YAAE;YAAO,GAAG,OAAO;QAAC;IAC7D;IACA,MAAM,iBAAiB,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE;QAC1C,MAAM,EAAE,WAAW,EAAE,GAAG,YAAY,GAAG;QACvC,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,2BAA2B,YAAY;YACpE,cAAc;YACd,QAAQ;gBAAE,IAAI;YAAQ;QAC1B;QACA,IAAI,CAAC,aAAa;YACd,OAAO,UAAU,IAAI;QACzB;QACA,OAAO,IAAI,8LAAA,CAAA,kCAA+B,CAAC;YACvC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;YACb,cAAc;gBAAE,IAAI;YAAQ;QAChC;IACJ;IACA,MAAM,aAAa,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE;QACtC,MAAM,EAAE,WAAW,EAAE,GAAG,YAAY,GAAG;QACvC,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,2BAA2B,YAAY;YACpE,cAAc;YACd,QAAQ;gBAAE,IAAI;YAAQ;QAC1B;QACA,IAAI,CAAC,aAAa;YACd,OAAO,UAAU,IAAI;QACzB;QACA,OAAO,IAAI,8LAAA,CAAA,8BAA2B,CAAC;YACnC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;YACb,cAAc;gBAAE,IAAI;YAAQ;QAChC;IACJ;IACA;;;;;;KAMC,GACD,MAAM,aAAa,UAAU,CAAC,CAAC,EAAE;QAC7B,MAAM,SAAS,MAAM,IAAI,CAAC,sBAAsB;QAChD,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,6CAA6C,SAAS;YACnF,cAAc;YACd,QAAQ;gBAAE,IAAI,OAAO,IAAI,CAAC,EAAE;YAAC;QACjC;QACA,OAAO,IAAI,+LAAA,CAAA,+BAA4B,CAAC;YACpC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;YACb,cAAc;gBAAE,IAAI,OAAO,IAAI,CAAC,EAAE;YAAC;QACvC;IACJ;IACA;;;;;KAKC,GACD,MAAM,aAAa,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QACrC,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,oBAAoB,SAAS;YAC1D,cAAc;YACd,QAAQ;gBAAE,IAAI;YAAO;QACzB;QACA,OAAO,IAAI,+LAAA,CAAA,+BAA4B,CAAC;YACpC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;YACb,cAAc;gBAAE,IAAI;YAAO;QAC/B;IACJ;IACA;;;;;KAKC,GACD,MAAM,oBAAoB,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QAC5C,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,sBAAsB,SAAS;YAC5D,cAAc;YACd,QAAQ;gBAAE,IAAI;YAAO;QACzB;QACA,OAAO,IAAI,+LAAA,CAAA,sCAAmC,CAAC;YAC3C,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;YACb,cAAc;gBAAE,IAAI;YAAO;QAC/B;IACJ;IACA;;;;;KAKC,GACD,MAAM,OAAO,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE;QAChC,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,2BAA2B,SAAS;YACjE,cAAc;YACd,QAAQ;gBAAE,IAAI;YAAQ;QAC1B;QACA,OAAO,IAAI,+LAAA,CAAA,kCAA+B,CAAC;YACvC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;YACb,cAAc;gBAAE,IAAI;YAAQ;QAChC;IACJ;IACA,aAAa,GACb;;;;;KAKC,GACD,MAAM,UAAU,UAAU,CAAC,CAAC,EAAE;QAC1B,MAAM,OAAO,MAAM,IAAI,CAAC,sBAAsB;QAC9C,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,uBAAuB,SAAS;YAC7D,cAAc;YACd,QAAQ;gBAAE,IAAI,KAAK,IAAI,CAAC,EAAE;YAAC;QAC/B;QACA,OAAO,IAAI,+LAAA,CAAA,oCAAiC,CAAC;YACzC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;YACb,cAAc;gBAAE,IAAI,KAAK,IAAI,CAAC,EAAE;YAAC;QACrC;IACJ;IACA,SAAS,GACT;;;;;KAKC,GACD,GAAG,UAAU,CAAC,CAAC,EAAE;QACb,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY;IAChC;IACA;;;KAGC,GACD,KAAK,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QACvB,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,SAAS;YAAE,QAAQ;gBAAE,IAAI;YAAO;QAAE;IACnE;IACA;;;KAGC,GACD,MAAM,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE;QACzB,MAAM,MAAM,MAAM,OAAO,CAAC,WAAW,QAAQ,IAAI,CAAC,OAAO;QACzD,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS;YAAE,GAAG,OAAO;YAAE;QAAI;IAC/C;IACA;;;KAGC,GACD,eAAe,QAAQ,EAAE,UAAU,CAAC,CAAC,EAAE;QACnC,OAAO,IAAI,CAAC,GAAG,CAAC,+BAA+B,SAAS;YAAE,QAAQ;gBAAE;YAAS;QAAE;IACnF;IACA;;;;;KAKC,GACD,iBAAiB,SAAS,EAAE,UAAU,CAAC,CAAC,EAAE;QACtC,YAAY,MAAM,OAAO,CAAC,aAAa,UAAU,IAAI,CAAC,OAAO;QAC7D,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY;YAAE,GAAG,OAAO;YAAE;QAAU;IACxD;IACA,MAAM,UAAU,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QAClC,MAAM,EAAE,WAAW,EAAE,GAAG,YAAY,GAAG;QACvC,MAAM,SAAS;YAAE,IAAI;QAAO;QAC5B,IAAI,CAAC,aAAa;YACd,OAAO,IAAI,CAAC,GAAG,CAAC,uBAAuB,YAAY;gBAAE;YAAO;QAChE;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,uBAAuB,YAAY;YAAE,cAAc;YAAM;QAAO;QACjG,OAAO,IAAI,8LAAA,CAAA,2BAAwB,CAAC;YAChC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;YACb,cAAc;QAClB;IACJ;IACA,MAAM,UAAU,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QAClC,MAAM,EAAE,WAAW,EAAE,GAAG,YAAY,GAAG;QACvC,MAAM,SAAS;YAAE,IAAI;QAAO;QAC5B,IAAI,CAAC,aAAa;YACd,OAAO,IAAI,CAAC,GAAG,CAAC,uBAAuB,YAAY;gBAAE;YAAO;QAChE;QACA,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,uBAAuB,YAAY;YAAE,cAAc;YAAM;QAAO;QACjG,OAAO,IAAI,8LAAA,CAAA,2BAAwB,CAAC;YAChC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;YACb,cAAc;QAClB;IACJ;IACA;;;KAGC,GACD,MAAM,gBAAgB,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QACxC,MAAM,SAAS;YAAE,IAAI;QAAO;QAC5B,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,0BAA0B,SAAS;YAAE,cAAc;YAAM;QAAO;QACjG,OAAO,IAAI,+LAAA,CAAA,kCAA+B,CAAC;YACvC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;gBAAE,GAAG,OAAO;YAAC;YAC1B,cAAc;QAClB;IACJ;IACA;;;KAGC,GACD,MAAM,kBAAkB,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QAC1C,MAAM,SAAS;YAAE,IAAI;QAAO;QAC5B,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,sBAAsB,SAAS;YAAE,cAAc;YAAM;QAAO;QAC7F,OAAO,IAAI,8LAAA,CAAA,+BAA4B,CAAC;YACpC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;gBAAE,GAAG,OAAO;YAAC;YAC1B,cAAc;QAClB;IACJ;IACA;;;KAGC,GACD,MAAM,gBAAgB,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QACxC,MAAM,SAAS;YAAE,IAAI;QAAO;QAC5B,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,oBAAoB,SAAS;YAAE,cAAc;YAAM;QAAO;QAC3F,OAAO,IAAI,8LAAA,CAAA,6BAA0B,CAAC;YAClC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;gBAAE,GAAG,OAAO;YAAC;YAC1B,cAAc;QAClB;IACJ;IACA,SAAS,GACT;;;KAGC,GACD,KAAK,EAAE,EAAE,UAAU,CAAC,CAAC,EAAE;QACnB,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,SAAS;YAAE,QAAQ;gBAAE;YAAG;QAAE;IAC3D;IACA;;;KAGC,GACD,MAAM,WAAW,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QACnC,MAAM,SAAS;YAAE,IAAI;QAAO;QAC5B,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,yBAAyB,SAAS;YAAE,cAAc;YAAM;QAAO;QAChG,OAAO,IAAI,8LAAA,CAAA,4BAAyB,CAAC;YACjC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;gBAAE,GAAG,OAAO;YAAC;YAC1B,cAAc;QAClB;IACJ;IACA;;;KAGC,GACD,MAAM,gBAAgB,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QACxC,MAAM,SAAS;YAAE,IAAI;QAAO;QAC5B,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,8BAA8B,SAAS;YAAE,cAAc;YAAM;QAAO;QACrG,OAAO,IAAI,8LAAA,CAAA,iCAA8B,CAAC;YACtC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;gBAAE,GAAG,OAAO;YAAC;YAC1B,cAAc;QAClB;IACJ;IACA;;;KAGC,GACD,MAAM,aAAa,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QACrC,MAAM,SAAS;YAAE,IAAI;QAAO;QAC5B,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,4BAA4B,SAAS;YAAE,cAAc;YAAM;QAAO;QACnG,OAAO,IAAI,8LAAA,CAAA,8BAA2B,CAAC;YACnC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;gBAAE,GAAG,OAAO;YAAC;YAC1B,cAAc;QAClB;IACJ;IACA;;;KAGC,GACD,MAAM,WAAW,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QACnC,MAAM,SAAS;YAAE,IAAI;QAAO;QAC5B,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,oBAAoB,SAAS;YAAE,cAAc;YAAM;QAAO;QAC3F,OAAO,IAAI,+LAAA,CAAA,6BAA0B,CAAC;YAClC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;gBAAE,GAAG,OAAO;YAAC;YAC1B,cAAc;QAClB;IACJ;IACA;;;KAGC,GACD,MAAM,YAAY,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QACpC,MAAM,SAAS;YAAE,IAAI;QAAO;QAC5B,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,qBAAqB,SAAS;YAAE,cAAc;YAAM;QAAO;QAC5F,OAAO,IAAI,8LAAA,CAAA,6BAA0B,CAAC;YAClC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;gBAAE,GAAG,OAAO;YAAC;YAC1B,cAAc;QAClB;IACJ;IACA;;;KAGC,GACD,MAAM,cAAc,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QACtC,MAAM,SAAS;YAAE,IAAI;QAAO;QAC5B,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,uBAAuB,SAAS;YAAE,cAAc;YAAM;QAAO;QAC9F,OAAO,IAAI,8LAAA,CAAA,+BAA4B,CAAC;YACpC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;gBAAE,GAAG,OAAO;YAAC;YAC1B,cAAc;QAClB;IACJ;IACA,mBAAmB,GACnB;;;;;;;;KAQC,GACD,MAAM,aAAa,UAAU,CAAC,CAAC,EAAE;QAC7B,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,aAAa,SAAS;YAAE,cAAc;QAAK;QAC5E,OAAO,IAAI,4LAAA,CAAA,4BAAyB,CAAC;YACjC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;gBAAE,GAAG,OAAO;YAAC;QAC9B;IACJ;IACA;;;;;;;KAOC,GACD,MAAM,4BAA4B,aAAa,EAAE,UAAU,CAAC,CAAC,EAAE;QAC3D,MAAM,SAAS;YAAE,gBAAgB;QAAc;QAC/C,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,mDAAmD,SAAS;YAAE,cAAc;YAAM;QAAO;QAC1H,OAAO,IAAI,4LAAA,CAAA,gCAA6B,CAAC;YACrC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;gBAAE,GAAG,OAAO;YAAC;YAC1B,cAAc;QAClB;IACJ;IACA;;;;;;;KAOC,GACD,MAAM,2BAA2B,gBAAgB,EAAE,UAAU,CAAC,CAAC,EAAE;QAC7D,MAAM,SAAS;YAAE,oBAAoB;QAAiB;QACtD,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,kDAAkD,SAAS;YAAE,cAAc;YAAM;QAAO;QACzH,OAAO,IAAI,4LAAA,CAAA,oCAAiC,CAAC;YACzC,UAAU,UAAU,IAAI;YACxB,WAAW,UAAU,SAAS;YAC9B,UAAU,IAAI;YACd,aAAa;gBAAE,GAAG,OAAO;YAAC;YAC1B,cAAc;QAClB;IACJ;IACA,UAAU,GACV;;;;;KAKC,GACD,MAAM,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE;QACzB,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,SAAS;YAAE,QAAQ;gBAAE,IAAI;YAAQ;QAAE;IACrE;IACA;;;;;KAKC,GACD,OAAO,QAAQ,EAAE,UAAU,CAAC,CAAC,EAAE;QAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU;YAAE,KAAK;YAAU,GAAG,OAAO;QAAC;IAC1D;IACA;;;;;KAKC,GACD,iBAAiB,UAAU,EAAE,UAAU,CAAC,CAAC,EAAE;QACvC,OAAO,IAAI,CAAC,GAAG,CAAC,yBAAyB;YAAE,UAAU;YAAY,GAAG,OAAO;QAAC;IAChF;IACA;;;KAGC,GACD,aAAa,OAAO,EAAE;QAClB,OAAO,IAAI,CAAC,GAAG,CAAC,iBAAiB;IACrC;IACA;;;;;;;;;IASA,GACA,YAAY,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE;QAC/B,OAAO,IAAI,CAAC,GAAG,CAAC,qBAAqB,SAAS;YAAE,QAAQ;gBAAE,IAAI;YAAQ;QAAE;IAC5E;IACA;;;;;KAKC,GACD,YAAY,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE;QAC/B,OAAO,IAAI,CAAC,GAAG,CAAC,qBAAqB,SAAS;YAAE,QAAQ;gBAAE,IAAI;YAAQ;QAAE;IAC5E;IACA,aAAa,EAAE,WAAW,EAAE,GAAG,SAAS,GAAG,CAAC,CAAC,EAAE;QAC3C,OAAO,IAAI,CAAC,SAAS,CAAC,wBAAwB,SAAS;YAAE,gBAAgB,gKAAA,CAAA,8BAA2B;YAAE;QAAY;IACtH;IACA;;;KAGC,GACD,YAAY,UAAU,CAAC,CAAC,EAAE;QACtB,OAAO,IAAI,CAAC,GAAG,CAAC,8BAA8B;IAClD;IACA,kBAAkB,OAAO,EAAE,QAAQ,CAAC,CAAC,EAAE;QACnC,OAAO,IAAI,CAAC,IAAI,CAAC,8BAA8B,SAAS;YAAE;QAAM;IACpE;IACA,aAAa,EAAE,WAAW,EAAE,GAAG,SAAS,GAAG,CAAC,CAAC,EAAE;QAC3C,OAAO,IAAI,CAAC,SAAS,CAAC,wBAAwB,SAAS;YAAE,gBAAgB,gKAAA,CAAA,8BAA2B;YAAE;QAAY;IACtH;IACA,eAAe,EAAE,WAAW,EAAE,GAAG,SAAS,GAAG,CAAC,CAAC,EAAE;QAC7C,OAAO,IAAI,CAAC,SAAS,CAAC,0BAA0B,SAAS;YAAE,gBAAgB,gKAAA,CAAA,8BAA2B;YAAE;QAAY;IACxH;IACA,oBAAoB,GACpB;;;KAGC,GACD,eAAe,OAAO,EAAE;QACpB,OAAO,IAAI,CAAC,GAAG,CAAC,mBAAmB;IACvC;IACA;;;KAGC,GACD,cAAc,KAAK,EAAE;QACjB,OAAO,IAAI,CAAC,GAAG,CAAC,uBAAuB,WAAW;YAAE,QAAQ;gBAAE,IAAI;YAAM;QAAE;IAC9E;IACA;;;;;;KAMC,GACD,MAAM,kBAAkB,SAAS,EAAE;QAC/B,MAAM,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,MAAM,UAAU,IAAI;YAAE,MAAM,UAAU,IAAI;QAAC;QAC5F,eAAe;QACf,MAAM,aAAa,UAAU,GAAG,YAAY,SAAS,UAAU,GAAG,GAAG,OAAO,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC;QACpG,iBAAiB;QACjB,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,YAAY;YAC5C,eAAe;YACf,YAAY;YACZ,SAAS;gBAAE,gBAAgB;YAAa;YACxC,QAAQ;QACZ;QACA,OAAO;IACX;IACA;;;;KAIC,GACD,MAAM,oBAAoB,GAAG,EAAE;QAC3B,IAAI,aAAa;QACjB,MAAO,WAAW,MAAM,KAAK,WAAY;YACrC,IAAI,WAAW,MAAM,KAAK,aAAa,WAAW,MAAM,KAAK,UAAU;gBACnE,MAAM,IAAI,MAAM;YACpB;YACA,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,aAAa,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI;QACxD;QACA,4BAA4B;QAC5B,MAAM,SAAS,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,YAAY,EAAE,WAAW;YACvD,YAAY;YACZ,QAAQ;QACZ;QACA,OAAO,OACF,IAAI,GACJ,KAAK,CAAC,MACN,MAAM,CAAC,CAAA,OAAQ,MACf,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC;IAChC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6280, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/v2-labs/client.v2.labs.write.js"], "sourcesContent": ["import { API_V2_LABS_PREFIX } from '../globals';\nimport TwitterApiv2LabsReadOnly from './client.v2.labs.read';\n/**\n * Base Twitter v2 labs client with read/write rights.\n */\nexport default class TwitterApiv2LabsReadWrite extends TwitterApiv2LabsReadOnly {\n    constructor() {\n        super(...arguments);\n        this._prefix = API_V2_LABS_PREFIX;\n    }\n    /**\n     * Get a client with only read rights.\n     */\n    get readOnly() {\n        return this;\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAIe,MAAM,kCAAkC,mMAAA,CAAA,UAAwB;IAC3E,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,OAAO,GAAG,gKAAA,CAAA,qBAAkB;IACrC;IACA;;KAEC,GACD,IAAI,WAAW;QACX,OAAO,IAAI;IACf;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6304, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/v2/client.v2.write.js"], "sourcesContent": ["import { API_V2_PREFIX } from '../globals';\nimport TwitterApiv2ReadOnly from './client.v2.read';\nimport TwitterApiv2LabsReadWrite from '../v2-labs/client.v2.labs.write';\n/**\n * Base Twitter v2 client with read/write rights.\n */\nexport default class TwitterApiv2ReadWrite extends TwitterApiv2ReadOnly {\n    constructor() {\n        super(...arguments);\n        this._prefix = API_V2_PREFIX;\n    }\n    /* Sub-clients */\n    /**\n     * Get a client with only read rights.\n     */\n    get readOnly() {\n        return this;\n    }\n    /**\n     * Get a client for v2 labs endpoints.\n     */\n    get labs() {\n        if (this._labs)\n            return this._labs;\n        return this._labs = new TwitterApiv2LabsReadWrite(this);\n    }\n    /* Tweets */\n    /**\n     * Hides or unhides a reply to a Tweet.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/hide-replies/api-reference/put-tweets-id-hidden\n     */\n    hideReply(tweetId, makeHidden) {\n        return this.put('tweets/:id/hidden', { hidden: makeHidden }, { params: { id: tweetId } });\n    }\n    /**\n     * Causes the user ID identified in the path parameter to Like the target Tweet.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/likes/api-reference/post-users-user_id-likes\n     *\n     * **Note**: You must specify the currently logged user ID ; you can obtain it through v1.1 API.\n     */\n    like(loggedUserId, targetTweetId) {\n        return this.post('users/:id/likes', { tweet_id: targetTweetId }, { params: { id: loggedUserId } });\n    }\n    /**\n     * Allows a user or authenticated user ID to unlike a Tweet.\n     * The request succeeds with no action when the user sends a request to a user they're not liking the Tweet or have already unliked the Tweet.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/likes/api-reference/delete-users-id-likes-tweet_id\n     *\n     * **Note**: You must specify the currently logged user ID ; you can obtain it through v1.1 API.\n     */\n    unlike(loggedUserId, targetTweetId) {\n        return this.delete('users/:id/likes/:tweet_id', undefined, {\n            params: { id: loggedUserId, tweet_id: targetTweetId },\n        });\n    }\n    /**\n     * Causes the user ID identified in the path parameter to Retweet the target Tweet.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/retweets/api-reference/post-users-id-retweets\n     *\n     * **Note**: You must specify the currently logged user ID ; you can obtain it through v1.1 API.\n     */\n    retweet(loggedUserId, targetTweetId) {\n        return this.post('users/:id/retweets', { tweet_id: targetTweetId }, { params: { id: loggedUserId } });\n    }\n    /**\n     * Allows a user or authenticated user ID to remove the Retweet of a Tweet.\n     * The request succeeds with no action when the user sends a request to a user they're not Retweeting the Tweet or have already removed the Retweet of.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/retweets/api-reference/delete-users-id-retweets-tweet_id\n     *\n     * **Note**: You must specify the currently logged user ID ; you can obtain it through v1.1 API.\n     */\n    unretweet(loggedUserId, targetTweetId) {\n        return this.delete('users/:id/retweets/:tweet_id', undefined, {\n            params: { id: loggedUserId, tweet_id: targetTweetId },\n        });\n    }\n    tweet(status, payload = {}) {\n        if (typeof status === 'object') {\n            payload = status;\n        }\n        else {\n            payload = { text: status, ...payload };\n        }\n        return this.post('tweets', payload);\n    }\n    /**\n     * Reply to a Tweet on behalf of an authenticated user.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/manage-tweets/api-reference/post-tweets\n     */\n    reply(status, toTweetId, payload = {}) {\n        var _a;\n        const reply = { in_reply_to_tweet_id: toTweetId, ...(_a = payload.reply) !== null && _a !== void 0 ? _a : {} };\n        return this.post('tweets', { text: status, ...payload, reply });\n    }\n    /**\n     * Quote an existing Tweet on behalf of an authenticated user.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/manage-tweets/api-reference/post-tweets\n     */\n    quote(status, quotedTweetId, payload = {}) {\n        return this.tweet(status, { ...payload, quote_tweet_id: quotedTweetId });\n    }\n    /**\n     * Post a series of tweets.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/manage-tweets/api-reference/post-tweets\n     */\n    async tweetThread(tweets) {\n        var _a, _b;\n        const postedTweets = [];\n        for (const tweet of tweets) {\n            // Retrieve the last sent tweet\n            const lastTweet = postedTweets.length ? postedTweets[postedTweets.length - 1] : null;\n            // Build the tweet query params\n            const queryParams = { ...(typeof tweet === 'string' ? ({ text: tweet }) : tweet) };\n            // Reply to an existing tweet if needed\n            const inReplyToId = lastTweet ? lastTweet.data.id : (_a = queryParams.reply) === null || _a === void 0 ? void 0 : _a.in_reply_to_tweet_id;\n            const status = (_b = queryParams.text) !== null && _b !== void 0 ? _b : '';\n            if (inReplyToId) {\n                postedTweets.push(await this.reply(status, inReplyToId, queryParams));\n            }\n            else {\n                postedTweets.push(await this.tweet(status, queryParams));\n            }\n        }\n        return postedTweets;\n    }\n    /**\n     * Allows a user or authenticated user ID to delete a Tweet\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/manage-tweets/api-reference/delete-tweets-id\n     */\n    deleteTweet(tweetId) {\n        return this.delete('tweets/:id', undefined, {\n            params: {\n                id: tweetId,\n            },\n        });\n    }\n    /* Bookmarks */\n    /**\n     * Causes the user ID of an authenticated user identified in the path parameter to Bookmark the target Tweet provided in the request body.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/bookmarks/api-reference/post-users-id-bookmarks\n     *\n     * OAuth2 scopes: `users.read` `tweet.read` `bookmark.write`\n     */\n    async bookmark(tweetId) {\n        const user = await this.getCurrentUserV2Object();\n        return this.post('users/:id/bookmarks', { tweet_id: tweetId }, { params: { id: user.data.id } });\n    }\n    /**\n     * Allows a user or authenticated user ID to remove a Bookmark of a Tweet.\n     * https://developer.twitter.com/en/docs/twitter-api/tweets/bookmarks/api-reference/delete-users-id-bookmarks-tweet_id\n     *\n     * OAuth2 scopes: `users.read` `tweet.read` `bookmark.write`\n     */\n    async deleteBookmark(tweetId) {\n        const user = await this.getCurrentUserV2Object();\n        return this.delete('users/:id/bookmarks/:tweet_id', undefined, { params: { id: user.data.id, tweet_id: tweetId } });\n    }\n    /* Users */\n    /**\n     * Allows a user ID to follow another user.\n     * If the target user does not have public Tweets, this endpoint will send a follow request.\n     * https://developer.twitter.com/en/docs/twitter-api/users/follows/api-reference/post-users-source_user_id-following\n     *\n     * OAuth2 scope: `follows.write`\n     *\n     * **Note**: You must specify the currently logged user ID ; you can obtain it through v1.1 API.\n     */\n    follow(loggedUserId, targetUserId) {\n        return this.post('users/:id/following', { target_user_id: targetUserId }, { params: { id: loggedUserId } });\n    }\n    /**\n     * Allows a user ID to unfollow another user.\n     * https://developer.twitter.com/en/docs/twitter-api/users/follows/api-reference/delete-users-source_id-following\n     *\n     * OAuth2 scope: `follows.write`\n     *\n     * **Note**: You must specify the currently logged user ID ; you can obtain it through v1.1 API.\n     */\n    unfollow(loggedUserId, targetUserId) {\n        return this.delete('users/:source_user_id/following/:target_user_id', undefined, {\n            params: { source_user_id: loggedUserId, target_user_id: targetUserId },\n        });\n    }\n    /**\n     * Causes the user (in the path) to block the target user.\n     * The user (in the path) must match the user context authorizing the request.\n     * https://developer.twitter.com/en/docs/twitter-api/users/blocks/api-reference/post-users-user_id-blocking\n     *\n     * **Note**: You must specify the currently logged user ID; you can obtain it through v1.1 API.\n     */\n    block(loggedUserId, targetUserId) {\n        return this.post('users/:id/blocking', { target_user_id: targetUserId }, { params: { id: loggedUserId } });\n    }\n    /**\n     * Allows a user or authenticated user ID to unblock another user.\n     * https://developer.twitter.com/en/docs/twitter-api/users/blocks/api-reference/delete-users-user_id-blocking\n     *\n     * **Note**: You must specify the currently logged user ID ; you can obtain it through v1.1 API.\n     */\n    unblock(loggedUserId, targetUserId) {\n        return this.delete('users/:source_user_id/blocking/:target_user_id', undefined, {\n            params: { source_user_id: loggedUserId, target_user_id: targetUserId },\n        });\n    }\n    /**\n     * Allows an authenticated user ID to mute the target user.\n     * https://developer.twitter.com/en/docs/twitter-api/users/mutes/api-reference/post-users-user_id-muting\n     *\n     * **Note**: You must specify the currently logged user ID ; you can obtain it through v1.1 API.\n     */\n    mute(loggedUserId, targetUserId) {\n        return this.post('users/:id/muting', { target_user_id: targetUserId }, { params: { id: loggedUserId } });\n    }\n    /**\n     * Allows an authenticated user ID to unmute the target user.\n     * The request succeeds with no action when the user sends a request to a user they're not muting or have already unmuted.\n     * https://developer.twitter.com/en/docs/twitter-api/users/mutes/api-reference/delete-users-user_id-muting\n     *\n     * **Note**: You must specify the currently logged user ID ; you can obtain it through v1.1 API.\n     */\n    unmute(loggedUserId, targetUserId) {\n        return this.delete('users/:source_user_id/muting/:target_user_id', undefined, {\n            params: { source_user_id: loggedUserId, target_user_id: targetUserId },\n        });\n    }\n    /* Lists */\n    /**\n     * Creates a new list for the authenticated user.\n     * https://developer.twitter.com/en/docs/twitter-api/lists/manage-lists/api-reference/post-lists\n     */\n    createList(options) {\n        return this.post('lists', options);\n    }\n    /**\n     * Updates the specified list. The authenticated user must own the list to be able to update it.\n     * https://developer.twitter.com/en/docs/twitter-api/lists/manage-lists/api-reference/put-lists-id\n     */\n    updateList(listId, options = {}) {\n        return this.put('lists/:id', options, { params: { id: listId } });\n    }\n    /**\n     * Deletes the specified list. The authenticated user must own the list to be able to destroy it.\n     * https://developer.twitter.com/en/docs/twitter-api/lists/manage-lists/api-reference/delete-lists-id\n     */\n    removeList(listId) {\n        return this.delete('lists/:id', undefined, { params: { id: listId } });\n    }\n    /**\n     * Adds a member to a list.\n     * https://developer.twitter.com/en/docs/twitter-api/lists/manage-lists/api-reference/post-lists-id-members\n     */\n    addListMember(listId, userId) {\n        return this.post('lists/:id/members', { user_id: userId }, { params: { id: listId } });\n    }\n    /**\n     * Remember a member to a list.\n     * https://developer.twitter.com/en/docs/twitter-api/lists/manage-lists/api-reference/delete-lists-id-members-user_id\n     */\n    removeListMember(listId, userId) {\n        return this.delete('lists/:id/members/:user_id', undefined, { params: { id: listId, user_id: userId } });\n    }\n    /**\n     * Subscribes the authenticated user to the specified list.\n     * https://developer.twitter.com/en/docs/twitter-api/lists/manage-lists/api-reference/post-users-id-followed-lists\n     */\n    subscribeToList(loggedUserId, listId) {\n        return this.post('users/:id/followed_lists', { list_id: listId }, { params: { id: loggedUserId } });\n    }\n    /**\n     * Unsubscribes the authenticated user to the specified list.\n     * https://developer.twitter.com/en/docs/twitter-api/lists/manage-lists/api-reference/delete-users-id-followed-lists-list_id\n     */\n    unsubscribeOfList(loggedUserId, listId) {\n        return this.delete('users/:id/followed_lists/:list_id', undefined, { params: { id: loggedUserId, list_id: listId } });\n    }\n    /**\n     * Enables the authenticated user to pin a List.\n     * https://developer.twitter.com/en/docs/twitter-api/lists/manage-lists/api-reference/post-users-id-pinned-lists\n     */\n    pinList(loggedUserId, listId) {\n        return this.post('users/:id/pinned_lists', { list_id: listId }, { params: { id: loggedUserId } });\n    }\n    /**\n     * Enables the authenticated user to unpin a List.\n     * https://developer.twitter.com/en/docs/twitter-api/lists/manage-lists/api-reference/delete-users-id-pinned-lists-list_id\n     */\n    unpinList(loggedUserId, listId) {\n        return this.delete('users/:id/pinned_lists/:list_id', undefined, { params: { id: loggedUserId, list_id: listId } });\n    }\n    /* Direct messages */\n    /**\n     * Creates a Direct Message on behalf of an authenticated user, and adds it to the specified conversation.\n     * https://developer.twitter.com/en/docs/twitter-api/direct-messages/manage/api-reference/post-dm_conversations-dm_conversation_id-messages\n     */\n    sendDmInConversation(conversationId, message) {\n        return this.post('dm_conversations/:dm_conversation_id/messages', message, { params: { dm_conversation_id: conversationId } });\n    }\n    /**\n     * Creates a one-to-one Direct Message and adds it to the one-to-one conversation.\n     * This method either creates a new one-to-one conversation or retrieves the current conversation and adds the Direct Message to it.\n     * https://developer.twitter.com/en/docs/twitter-api/direct-messages/manage/api-reference/post-dm_conversations-with-participant_id-messages\n     */\n    sendDmToParticipant(participantId, message) {\n        return this.post('dm_conversations/with/:participant_id/messages', message, { params: { participant_id: participantId } });\n    }\n    /**\n     * Creates a new group conversation and adds a Direct Message to it on behalf of an authenticated user.\n     * https://developer.twitter.com/en/docs/twitter-api/direct-messages/manage/api-reference/post-dm_conversations\n     */\n    createDmConversation(options) {\n        return this.post('dm_conversations', options);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAIe,MAAM,8BAA8B,mLAAA,CAAA,UAAoB;IACnE,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,OAAO,GAAG,gKAAA,CAAA,gBAAa;IAChC;IACA,eAAe,GACf;;KAEC,GACD,IAAI,WAAW;QACX,OAAO,IAAI;IACf;IACA;;KAEC,GACD,IAAI,OAAO;QACP,IAAI,IAAI,CAAC,KAAK,EACV,OAAO,IAAI,CAAC,KAAK;QACrB,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,oMAAA,CAAA,UAAyB,CAAC,IAAI;IAC1D;IACA,UAAU,GACV;;;KAGC,GACD,UAAU,OAAO,EAAE,UAAU,EAAE;QAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,qBAAqB;YAAE,QAAQ;QAAW,GAAG;YAAE,QAAQ;gBAAE,IAAI;YAAQ;QAAE;IAC3F;IACA;;;;;KAKC,GACD,KAAK,YAAY,EAAE,aAAa,EAAE;QAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,UAAU;QAAc,GAAG;YAAE,QAAQ;gBAAE,IAAI;YAAa;QAAE;IACpG;IACA;;;;;;KAMC,GACD,OAAO,YAAY,EAAE,aAAa,EAAE;QAChC,OAAO,IAAI,CAAC,MAAM,CAAC,6BAA6B,WAAW;YACvD,QAAQ;gBAAE,IAAI;gBAAc,UAAU;YAAc;QACxD;IACJ;IACA;;;;;KAKC,GACD,QAAQ,YAAY,EAAE,aAAa,EAAE;QACjC,OAAO,IAAI,CAAC,IAAI,CAAC,sBAAsB;YAAE,UAAU;QAAc,GAAG;YAAE,QAAQ;gBAAE,IAAI;YAAa;QAAE;IACvG;IACA;;;;;;KAMC,GACD,UAAU,YAAY,EAAE,aAAa,EAAE;QACnC,OAAO,IAAI,CAAC,MAAM,CAAC,gCAAgC,WAAW;YAC1D,QAAQ;gBAAE,IAAI;gBAAc,UAAU;YAAc;QACxD;IACJ;IACA,MAAM,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QACxB,IAAI,OAAO,WAAW,UAAU;YAC5B,UAAU;QACd,OACK;YACD,UAAU;gBAAE,MAAM;gBAAQ,GAAG,OAAO;YAAC;QACzC;QACA,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU;IAC/B;IACA;;;KAGC,GACD,MAAM,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,EAAE;QACnC,IAAI;QACJ,MAAM,QAAQ;YAAE,sBAAsB;YAAW,GAAG,CAAC,KAAK,QAAQ,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,CAAC,CAAC;QAAC;QAC7G,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,MAAM;YAAQ,GAAG,OAAO;YAAE;QAAM;IACjE;IACA;;;KAGC,GACD,MAAM,MAAM,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC,EAAE;QACvC,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAAE,GAAG,OAAO;YAAE,gBAAgB;QAAc;IAC1E;IACA;;;KAGC,GACD,MAAM,YAAY,MAAM,EAAE;QACtB,IAAI,IAAI;QACR,MAAM,eAAe,EAAE;QACvB,KAAK,MAAM,SAAS,OAAQ;YACxB,+BAA+B;YAC/B,MAAM,YAAY,aAAa,MAAM,GAAG,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,GAAG;YAChF,+BAA+B;YAC/B,MAAM,cAAc;gBAAE,GAAI,OAAO,UAAU,WAAY;oBAAE,MAAM;gBAAM,IAAK,KAAK;YAAE;YACjF,uCAAuC;YACvC,MAAM,cAAc,YAAY,UAAU,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,YAAY,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,oBAAoB;YACzI,MAAM,SAAS,CAAC,KAAK,YAAY,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;YACxE,IAAI,aAAa;gBACb,aAAa,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,aAAa;YAC5D,OACK;gBACD,aAAa,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC/C;QACJ;QACA,OAAO;IACX;IACA;;;KAGC,GACD,YAAY,OAAO,EAAE;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,WAAW;YACxC,QAAQ;gBACJ,IAAI;YACR;QACJ;IACJ;IACA,aAAa,GACb;;;;;KAKC,GACD,MAAM,SAAS,OAAO,EAAE;QACpB,MAAM,OAAO,MAAM,IAAI,CAAC,sBAAsB;QAC9C,OAAO,IAAI,CAAC,IAAI,CAAC,uBAAuB;YAAE,UAAU;QAAQ,GAAG;YAAE,QAAQ;gBAAE,IAAI,KAAK,IAAI,CAAC,EAAE;YAAC;QAAE;IAClG;IACA;;;;;KAKC,GACD,MAAM,eAAe,OAAO,EAAE;QAC1B,MAAM,OAAO,MAAM,IAAI,CAAC,sBAAsB;QAC9C,OAAO,IAAI,CAAC,MAAM,CAAC,iCAAiC,WAAW;YAAE,QAAQ;gBAAE,IAAI,KAAK,IAAI,CAAC,EAAE;gBAAE,UAAU;YAAQ;QAAE;IACrH;IACA,SAAS,GACT;;;;;;;;KAQC,GACD,OAAO,YAAY,EAAE,YAAY,EAAE;QAC/B,OAAO,IAAI,CAAC,IAAI,CAAC,uBAAuB;YAAE,gBAAgB;QAAa,GAAG;YAAE,QAAQ;gBAAE,IAAI;YAAa;QAAE;IAC7G;IACA;;;;;;;KAOC,GACD,SAAS,YAAY,EAAE,YAAY,EAAE;QACjC,OAAO,IAAI,CAAC,MAAM,CAAC,mDAAmD,WAAW;YAC7E,QAAQ;gBAAE,gBAAgB;gBAAc,gBAAgB;YAAa;QACzE;IACJ;IACA;;;;;;KAMC,GACD,MAAM,YAAY,EAAE,YAAY,EAAE;QAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,sBAAsB;YAAE,gBAAgB;QAAa,GAAG;YAAE,QAAQ;gBAAE,IAAI;YAAa;QAAE;IAC5G;IACA;;;;;KAKC,GACD,QAAQ,YAAY,EAAE,YAAY,EAAE;QAChC,OAAO,IAAI,CAAC,MAAM,CAAC,kDAAkD,WAAW;YAC5E,QAAQ;gBAAE,gBAAgB;gBAAc,gBAAgB;YAAa;QACzE;IACJ;IACA;;;;;KAKC,GACD,KAAK,YAAY,EAAE,YAAY,EAAE;QAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,oBAAoB;YAAE,gBAAgB;QAAa,GAAG;YAAE,QAAQ;gBAAE,IAAI;YAAa;QAAE;IAC1G;IACA;;;;;;KAMC,GACD,OAAO,YAAY,EAAE,YAAY,EAAE;QAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,gDAAgD,WAAW;YAC1E,QAAQ;gBAAE,gBAAgB;gBAAc,gBAAgB;YAAa;QACzE;IACJ;IACA,SAAS,GACT;;;KAGC,GACD,WAAW,OAAO,EAAE;QAChB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS;IAC9B;IACA;;;KAGC,GACD,WAAW,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE;QAC7B,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,SAAS;YAAE,QAAQ;gBAAE,IAAI;YAAO;QAAE;IACnE;IACA;;;KAGC,GACD,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,WAAW;YAAE,QAAQ;gBAAE,IAAI;YAAO;QAAE;IACxE;IACA;;;KAGC,GACD,cAAc,MAAM,EAAE,MAAM,EAAE;QAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,qBAAqB;YAAE,SAAS;QAAO,GAAG;YAAE,QAAQ;gBAAE,IAAI;YAAO;QAAE;IACxF;IACA;;;KAGC,GACD,iBAAiB,MAAM,EAAE,MAAM,EAAE;QAC7B,OAAO,IAAI,CAAC,MAAM,CAAC,8BAA8B,WAAW;YAAE,QAAQ;gBAAE,IAAI;gBAAQ,SAAS;YAAO;QAAE;IAC1G;IACA;;;KAGC,GACD,gBAAgB,YAAY,EAAE,MAAM,EAAE;QAClC,OAAO,IAAI,CAAC,IAAI,CAAC,4BAA4B;YAAE,SAAS;QAAO,GAAG;YAAE,QAAQ;gBAAE,IAAI;YAAa;QAAE;IACrG;IACA;;;KAGC,GACD,kBAAkB,YAAY,EAAE,MAAM,EAAE;QACpC,OAAO,IAAI,CAAC,MAAM,CAAC,qCAAqC,WAAW;YAAE,QAAQ;gBAAE,IAAI;gBAAc,SAAS;YAAO;QAAE;IACvH;IACA;;;KAGC,GACD,QAAQ,YAAY,EAAE,MAAM,EAAE;QAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,0BAA0B;YAAE,SAAS;QAAO,GAAG;YAAE,QAAQ;gBAAE,IAAI;YAAa;QAAE;IACnG;IACA;;;KAGC,GACD,UAAU,YAAY,EAAE,MAAM,EAAE;QAC5B,OAAO,IAAI,CAAC,MAAM,CAAC,mCAAmC,WAAW;YAAE,QAAQ;gBAAE,IAAI;gBAAc,SAAS;YAAO;QAAE;IACrH;IACA,mBAAmB,GACnB;;;KAGC,GACD,qBAAqB,cAAc,EAAE,OAAO,EAAE;QAC1C,OAAO,IAAI,CAAC,IAAI,CAAC,iDAAiD,SAAS;YAAE,QAAQ;gBAAE,oBAAoB;YAAe;QAAE;IAChI;IACA;;;;KAIC,GACD,oBAAoB,aAAa,EAAE,OAAO,EAAE;QACxC,OAAO,IAAI,CAAC,IAAI,CAAC,kDAAkD,SAAS;YAAE,QAAQ;gBAAE,gBAAgB;YAAc;QAAE;IAC5H;IACA;;;KAGC,GACD,qBAAqB,OAAO,EAAE;QAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,oBAAoB;IACzC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6714, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/v2-labs/client.v2.labs.js"], "sourcesContent": ["import { API_V2_LABS_PREFIX } from '../globals';\nimport TwitterApiv2LabsReadWrite from './client.v2.labs.write';\n/**\n * Twitter v2 labs client with all rights (read/write/DMs)\n */\nexport class TwitterApiv2Labs extends TwitterApiv2LabsReadWrite {\n    constructor() {\n        super(...arguments);\n        this._prefix = API_V2_LABS_PREFIX;\n    }\n    /**\n     * Get a client with read/write rights.\n     */\n    get readWrite() {\n        return this;\n    }\n}\nexport default TwitterApiv2Labs;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAIO,MAAM,yBAAyB,oMAAA,CAAA,UAAyB;IAC3D,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,OAAO,GAAG,gKAAA,CAAA,qBAAkB;IACrC;IACA;;KAEC,GACD,IAAI,YAAY;QACZ,OAAO,IAAI;IACf;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6740, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/v2/client.v2.js"], "sourcesContent": ["import { API_V2_PREFIX } from '../globals';\nimport TwitterApiv2ReadWrite from './client.v2.write';\nimport TwitterApiv2Labs from '../v2-labs/client.v2.labs';\n/**\n * Twitter v2 client with all rights (read/write/DMs)\n */\nexport class TwitterApiv2 extends TwitterApiv2ReadWrite {\n    constructor() {\n        super(...arguments);\n        this._prefix = API_V2_PREFIX;\n        /** API endpoints */\n    }\n    /* Sub-clients */\n    /**\n     * Get a client with read/write rights.\n     */\n    get readWrite() {\n        return this;\n    }\n    /**\n     * Get a client for v2 labs endpoints.\n     */\n    get labs() {\n        if (this._labs)\n            return this._labs;\n        return this._labs = new TwitterApiv2Labs(this);\n    }\n}\nexport default TwitterApiv2;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAIO,MAAM,qBAAqB,oLAAA,CAAA,UAAqB;IACnD,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,OAAO,GAAG,gKAAA,CAAA,gBAAa;IAC5B,kBAAkB,GACtB;IACA,eAAe,GACf;;KAEC,GACD,IAAI,YAAY;QACZ,OAAO,IAAI;IACf;IACA;;KAEC,GACD,IAAI,OAAO;QACP,IAAI,IAAI,CAAC,KAAK,EACV,OAAO,IAAI,CAAC,KAAK;QACrB,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,2LAAA,CAAA,UAAgB,CAAC,IAAI;IACjD;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6774, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/client/index.js"], "sourcesContent": ["import TwitterApiv1 from '../v1/client.v1';\nimport TwitterApiv2 from '../v2/client.v2';\nimport TwitterApiReadWrite from './readwrite';\n// \"Real\" exported client for usage of TwitterApi.\n/**\n * Twitter v1.1 and v2 API client.\n */\nexport class <PERSON><PERSON><PERSON> extends TwitterApiReadWrite {\n    /* Direct access to subclients */\n    get v1() {\n        if (this._v1)\n            return this._v1;\n        return this._v1 = new TwitterApiv1(this);\n    }\n    get v2() {\n        if (this._v2)\n            return this._v2;\n        return this._v2 = new TwitterApiv2(this);\n    }\n    /**\n     * Get a client with read/write rights.\n     */\n    get readWrite() {\n        return this;\n    }\n    /* Static helpers */\n    static getErrors(error) {\n        var _a;\n        if (typeof error !== 'object')\n            return [];\n        if (!('data' in error))\n            return [];\n        return (_a = error.data.errors) !== null && _a !== void 0 ? _a : [];\n    }\n    /** Extract another image size than obtained in a `profile_image_url` or `profile_image_url_https` field of a user object. */\n    static getProfileImageInSize(profileImageUrl, size) {\n        const lastPart = profileImageUrl.split('/').pop();\n        const sizes = ['normal', 'bigger', 'mini'];\n        let originalUrl = profileImageUrl;\n        for (const availableSize of sizes) {\n            if (lastPart.includes(`_${availableSize}`)) {\n                originalUrl = profileImageUrl.replace(`_${availableSize}`, '');\n                break;\n            }\n        }\n        if (size === 'original') {\n            return originalUrl;\n        }\n        const extPos = originalUrl.lastIndexOf('.');\n        if (extPos !== -1) {\n            const ext = originalUrl.slice(extPos + 1);\n            return originalUrl.slice(0, extPos) + '_' + size + '.' + ext;\n        }\n        else {\n            return originalUrl + '_' + size;\n        }\n    }\n}\nexport { default as TwitterApiReadWrite } from './readwrite';\nexport { default as TwitterApiReadOnly } from './readonly';\nexport default TwitterApi;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAyDA;;;;AApDO,MAAM,mBAAmB,4KAAA,CAAA,UAAmB;IAC/C,+BAA+B,GAC/B,IAAI,KAAK;QACL,IAAI,IAAI,CAAC,GAAG,EACR,OAAO,IAAI,CAAC,GAAG;QACnB,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,2KAAA,CAAA,UAAY,CAAC,IAAI;IAC3C;IACA,IAAI,KAAK;QACL,IAAI,IAAI,CAAC,GAAG,EACR,OAAO,IAAI,CAAC,GAAG;QACnB,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,2KAAA,CAAA,UAAY,CAAC,IAAI;IAC3C;IACA;;KAEC,GACD,IAAI,YAAY;QACZ,OAAO,IAAI;IACf;IACA,kBAAkB,GAClB,OAAO,UAAU,KAAK,EAAE;QACpB,IAAI;QACJ,IAAI,OAAO,UAAU,UACjB,OAAO,EAAE;QACb,IAAI,CAAC,CAAC,UAAU,KAAK,GACjB,OAAO,EAAE;QACb,OAAO,CAAC,KAAK,MAAM,IAAI,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;IACvE;IACA,2HAA2H,GAC3H,OAAO,sBAAsB,eAAe,EAAE,IAAI,EAAE;QAChD,MAAM,WAAW,gBAAgB,KAAK,CAAC,KAAK,GAAG;QAC/C,MAAM,QAAQ;YAAC;YAAU;YAAU;SAAO;QAC1C,IAAI,cAAc;QAClB,KAAK,MAAM,iBAAiB,MAAO;YAC/B,IAAI,SAAS,QAAQ,CAAC,CAAC,CAAC,EAAE,eAAe,GAAG;gBACxC,cAAc,gBAAgB,OAAO,CAAC,CAAC,CAAC,EAAE,eAAe,EAAE;gBAC3D;YACJ;QACJ;QACA,IAAI,SAAS,YAAY;YACrB,OAAO;QACX;QACA,MAAM,SAAS,YAAY,WAAW,CAAC;QACvC,IAAI,WAAW,CAAC,GAAG;YACf,MAAM,MAAM,YAAY,KAAK,CAAC,SAAS;YACvC,OAAO,YAAY,KAAK,CAAC,GAAG,UAAU,MAAM,OAAO,MAAM;QAC7D,OACK;YACD,OAAO,cAAc,MAAM;QAC/B;IACJ;AACJ;;;uCAGe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6840, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/client/readonly.js"], "sourcesContent": ["import TwitterA<PERSON> from '.';\nimport Twitter<PERSON><PERSON>B<PERSON> from '../client.base';\nimport TwitterApiv1ReadOnly from '../v1/client.v1.read';\nimport TwitterApiv2ReadOnly from '../v2/client.v2.read';\nimport { OAuth2Helper } from '../client-mixins/oauth2.helper';\nimport RequestParamHelpers from '../client-mixins/request-param.helper';\n/**\n * Twitter v1.1 and v2 API client.\n */\nexport default class Twitter<PERSON>piReadOnly extends TwitterApiBase {\n    /* Direct access to subclients */\n    get v1() {\n        if (this._v1)\n            return this._v1;\n        return this._v1 = new TwitterApiv1ReadOnly(this);\n    }\n    get v2() {\n        if (this._v2)\n            return this._v2;\n        return this._v2 = new TwitterApiv2ReadOnly(this);\n    }\n    /**\n     * Fetch and cache current user.\n     * This method can only be called with a OAuth 1.0a user authentication.\n     *\n     * You can use this method to test if authentication was successful.\n     * Next calls to this methods will use the cached user, unless `forceFetch: true` is given.\n     */\n    async currentUser(forceFetch = false) {\n        return await this.getCurrentUserObject(forceFetch);\n    }\n    /**\n     * Fetch and cache current user.\n     * This method can only be called with a OAuth 1.0a or OAuth2 user authentication.\n     *\n     * This can only be the slimest available `UserV2` object, with only id, name and username properties defined.\n     * To get a customized `UserV2Result`, use `.v2.me()`\n     *\n     * You can use this method to test if authentication was successful.\n     * Next calls to this methods will use the cached user, unless `forceFetch: true` is given.\n     *\n     * OAuth2 scopes: `tweet.read` & `users.read`\n     */\n    async currentUserV2(forceFetch = false) {\n        return await this.getCurrentUserV2Object(forceFetch);\n    }\n    /* Shortcuts to endpoints */\n    search(what, options) {\n        return this.v2.search(what, options);\n    }\n    /* Authentication */\n    /**\n     * Generate the OAuth request token link for user-based OAuth 1.0 auth.\n     *\n     * ```ts\n     * // Instantiate TwitterApi with consumer keys\n     * const client = new TwitterApi({ appKey: 'consumer_key', appSecret: 'consumer_secret' });\n     *\n     * const tokenRequest = await client.generateAuthLink('oob-or-your-callback-url');\n     * // redirect end-user to tokenRequest.url\n     *\n     * // Save tokenRequest.oauth_token_secret somewhere, it will be needed for next auth step.\n     * ```\n     */\n    async generateAuthLink(oauth_callback = 'oob', { authAccessType, linkMode = 'authenticate', forceLogin, screenName, } = {}) {\n        const oauthResult = await this.post('https://api.twitter.com/oauth/request_token', { oauth_callback, x_auth_access_type: authAccessType });\n        let url = `https://api.twitter.com/oauth/${linkMode}?oauth_token=${encodeURIComponent(oauthResult.oauth_token)}`;\n        if (forceLogin !== undefined) {\n            url += `&force_login=${encodeURIComponent(forceLogin)}`;\n        }\n        if (screenName !== undefined) {\n            url += `&screen_name=${encodeURIComponent(screenName)}`;\n        }\n        if (this._requestMaker.hasPlugins()) {\n            this._requestMaker.applyPluginMethod('onOAuth1RequestToken', {\n                client: this._requestMaker,\n                url,\n                oauthResult,\n            });\n        }\n        return {\n            url,\n            ...oauthResult,\n        };\n    }\n    /**\n     * Obtain access to user-based OAuth 1.0 auth.\n     *\n     * After user is redirect from your callback, use obtained oauth_token and oauth_verifier to\n     * instantiate the new TwitterApi instance.\n     *\n     * ```ts\n     * // Use the saved oauth_token_secret associated to oauth_token returned by callback\n     * const requestClient = new TwitterApi({\n     *  appKey: 'consumer_key',\n     *  appSecret: 'consumer_secret',\n     *  accessToken: 'oauth_token',\n     *  accessSecret: 'oauth_token_secret'\n     * });\n     *\n     * // Use oauth_verifier obtained from callback request\n     * const { client: userClient } = await requestClient.login('oauth_verifier');\n     *\n     * // {userClient} is a valid {TwitterApi} object you can use for future requests\n     * ```\n     */\n    async login(oauth_verifier) {\n        const tokens = this.getActiveTokens();\n        if (tokens.type !== 'oauth-1.0a')\n            throw new Error('You must setup TwitterApi instance with consumer keys to accept OAuth 1.0 login');\n        const oauth_result = await this.post('https://api.twitter.com/oauth/access_token', { oauth_token: tokens.accessToken, oauth_verifier });\n        const client = new TwitterApi({\n            appKey: tokens.appKey,\n            appSecret: tokens.appSecret,\n            accessToken: oauth_result.oauth_token,\n            accessSecret: oauth_result.oauth_token_secret,\n        }, this._requestMaker.clientSettings);\n        return {\n            accessToken: oauth_result.oauth_token,\n            accessSecret: oauth_result.oauth_token_secret,\n            userId: oauth_result.user_id,\n            screenName: oauth_result.screen_name,\n            client,\n        };\n    }\n    /**\n     * Enable application-only authentication.\n     *\n     * To make the request, instantiate TwitterApi with consumer and secret.\n     *\n     * ```ts\n     * const requestClient = new TwitterApi({ appKey: 'consumer', appSecret: 'secret' });\n     * const appClient = await requestClient.appLogin();\n     *\n     * // Use {appClient} to make requests\n     * ```\n     */\n    async appLogin() {\n        const tokens = this.getActiveTokens();\n        if (tokens.type !== 'oauth-1.0a')\n            throw new Error('You must setup TwitterApi instance with consumer keys to accept app-only login');\n        // Create a client with Basic authentication\n        const basicClient = new TwitterApi({ username: tokens.appKey, password: tokens.appSecret }, this._requestMaker.clientSettings);\n        const res = await basicClient.post('https://api.twitter.com/oauth2/token', { grant_type: 'client_credentials' });\n        // New object with Bearer token\n        return new TwitterApi(res.access_token, this._requestMaker.clientSettings);\n    }\n    /* OAuth 2 user authentication */\n    /**\n     * Generate the OAuth request token link for user-based OAuth 2.0 auth.\n     *\n     * - **You can only use v2 API endpoints with this authentication method.**\n     * - **You need to specify which scope you want to have when you create your auth link. Make sure it matches your needs.**\n     *\n     * See https://developer.twitter.com/en/docs/authentication/oauth-2-0/user-access-token for details.\n     *\n     * ```ts\n     * // Instantiate TwitterApi with client ID\n     * const client = new TwitterApi({ clientId: 'yourClientId' });\n     *\n     * // Generate a link to callback URL that will gives a token with tweet+user read access\n     * const link = client.generateOAuth2AuthLink('your-callback-url', { scope: ['tweet.read', 'users.read'] });\n     *\n     * // Extract props from generate link\n     * const { url, state, codeVerifier } = link;\n     *\n     * // redirect end-user to url\n     * // Save `state` and `codeVerifier` somewhere, it will be needed for next auth step.\n     * ```\n     */\n    generateOAuth2AuthLink(redirectUri, options = {}) {\n        var _a, _b;\n        if (!this._requestMaker.clientId) {\n            throw new Error('Twitter API instance is not initialized with client ID. You can find your client ID in Twitter Developer Portal. ' +\n                'Please build an instance with: new TwitterApi({ clientId: \\'<yourClientId>\\' })');\n        }\n        const state = (_a = options.state) !== null && _a !== void 0 ? _a : OAuth2Helper.generateRandomString(32);\n        const codeVerifier = OAuth2Helper.getCodeVerifier();\n        const codeChallenge = OAuth2Helper.getCodeChallengeFromVerifier(codeVerifier);\n        const rawScope = (_b = options.scope) !== null && _b !== void 0 ? _b : '';\n        const scope = Array.isArray(rawScope) ? rawScope.join(' ') : rawScope;\n        const url = new URL('https://twitter.com/i/oauth2/authorize');\n        const query = {\n            response_type: 'code',\n            client_id: this._requestMaker.clientId,\n            redirect_uri: redirectUri,\n            state,\n            code_challenge: codeChallenge,\n            code_challenge_method: 's256',\n            scope,\n        };\n        RequestParamHelpers.addQueryParamsToUrl(url, query);\n        const result = {\n            url: url.toString(),\n            state,\n            codeVerifier,\n            codeChallenge,\n        };\n        if (this._requestMaker.hasPlugins()) {\n            this._requestMaker.applyPluginMethod('onOAuth2RequestToken', {\n                client: this._requestMaker,\n                result,\n                redirectUri,\n            });\n        }\n        return result;\n    }\n    /**\n     * Obtain access to user-based OAuth 2.0 auth.\n     *\n     * After user is redirect from your callback, use obtained code to\n     * instantiate the new TwitterApi instance.\n     *\n     * You need to obtain `codeVerifier` from a call to `.generateOAuth2AuthLink`.\n     *\n     * ```ts\n     * // Use the saved codeVerifier associated to state (present in query string of callback)\n     * const requestClient = new TwitterApi({ clientId: 'yourClientId' });\n     *\n     * const { client: userClient, refreshToken } = await requestClient.loginWithOAuth2({\n     *  code: 'codeFromQueryString',\n     *  // the same URL given to generateOAuth2AuthLink\n     *  redirectUri,\n     *  // the verifier returned by generateOAuth2AuthLink\n     *  codeVerifier,\n     * });\n     *\n     * // {userClient} is a valid {TwitterApi} object you can use for future requests\n     * // {refreshToken} is defined if 'offline.access' is in scope.\n     * ```\n     */\n    async loginWithOAuth2({ code, codeVerifier, redirectUri }) {\n        if (!this._requestMaker.clientId) {\n            throw new Error('Twitter API instance is not initialized with client ID. ' +\n                'Please build an instance with: new TwitterApi({ clientId: \\'<yourClientId>\\' })');\n        }\n        const accessTokenResult = await this.post('https://api.twitter.com/2/oauth2/token', {\n            code,\n            code_verifier: codeVerifier,\n            redirect_uri: redirectUri,\n            grant_type: 'authorization_code',\n            client_id: this._requestMaker.clientId,\n            client_secret: this._requestMaker.clientSecret,\n        });\n        return this.parseOAuth2AccessTokenResult(accessTokenResult);\n    }\n    /**\n     * Obtain a new access token to user-based OAuth 2.0 auth from a refresh token.\n     *\n     * ```ts\n     * const requestClient = new TwitterApi({ clientId: 'yourClientId' });\n     *\n     * const { client: userClient } = await requestClient.refreshOAuth2Token('refreshToken');\n     * // {userClient} is a valid {TwitterApi} object you can use for future requests\n     * ```\n     */\n    async refreshOAuth2Token(refreshToken) {\n        if (!this._requestMaker.clientId) {\n            throw new Error('Twitter API instance is not initialized with client ID. ' +\n                'Please build an instance with: new TwitterApi({ clientId: \\'<yourClientId>\\' })');\n        }\n        const accessTokenResult = await this.post('https://api.twitter.com/2/oauth2/token', {\n            refresh_token: refreshToken,\n            grant_type: 'refresh_token',\n            client_id: this._requestMaker.clientId,\n            client_secret: this._requestMaker.clientSecret,\n        });\n        return this.parseOAuth2AccessTokenResult(accessTokenResult);\n    }\n    /**\n     * Revoke a single user-based OAuth 2.0 token.\n     *\n     * You must specify its source, access token (directly after login)\n     * or refresh token (if you've called `.refreshOAuth2Token` before).\n     */\n    async revokeOAuth2Token(token, tokenType = 'access_token') {\n        if (!this._requestMaker.clientId) {\n            throw new Error('Twitter API instance is not initialized with client ID. ' +\n                'Please build an instance with: new TwitterApi({ clientId: \\'<yourClientId>\\' })');\n        }\n        return await this.post('https://api.twitter.com/2/oauth2/revoke', {\n            client_id: this._requestMaker.clientId,\n            client_secret: this._requestMaker.clientSecret,\n            token,\n            token_type_hint: tokenType,\n        });\n    }\n    parseOAuth2AccessTokenResult(result) {\n        const client = new TwitterApi(result.access_token, this._requestMaker.clientSettings);\n        const scope = result.scope.split(' ').filter(e => e);\n        return {\n            client,\n            expiresIn: result.expires_in,\n            accessToken: result.access_token,\n            scope,\n            refreshToken: result.refresh_token,\n        };\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAIe,MAAM,2BAA2B,uKAAA,CAAA,UAAc;IAC1D,+BAA+B,GAC/B,IAAI,KAAK;QACL,IAAI,IAAI,CAAC,GAAG,EACR,OAAO,IAAI,CAAC,GAAG;QACnB,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,mLAAA,CAAA,UAAoB,CAAC,IAAI;IACnD;IACA,IAAI,KAAK;QACL,IAAI,IAAI,CAAC,GAAG,EACR,OAAO,IAAI,CAAC,GAAG;QACnB,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,mLAAA,CAAA,UAAoB,CAAC,IAAI;IACnD;IACA;;;;;;KAMC,GACD,MAAM,YAAY,aAAa,KAAK,EAAE;QAClC,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC;IAC3C;IACA;;;;;;;;;;;KAWC,GACD,MAAM,cAAc,aAAa,KAAK,EAAE;QACpC,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC;IAC7C;IACA,0BAA0B,GAC1B,OAAO,IAAI,EAAE,OAAO,EAAE;QAClB,OAAO,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM;IAChC;IACA,kBAAkB,GAClB;;;;;;;;;;;;KAYC,GACD,MAAM,iBAAiB,iBAAiB,KAAK,EAAE,EAAE,cAAc,EAAE,WAAW,cAAc,EAAE,UAAU,EAAE,UAAU,EAAG,GAAG,CAAC,CAAC,EAAE;QACxH,MAAM,cAAc,MAAM,IAAI,CAAC,IAAI,CAAC,+CAA+C;YAAE;YAAgB,oBAAoB;QAAe;QACxI,IAAI,MAAM,CAAC,8BAA8B,EAAE,SAAS,aAAa,EAAE,mBAAmB,YAAY,WAAW,GAAG;QAChH,IAAI,eAAe,WAAW;YAC1B,OAAO,CAAC,aAAa,EAAE,mBAAmB,aAAa;QAC3D;QACA,IAAI,eAAe,WAAW;YAC1B,OAAO,CAAC,aAAa,EAAE,mBAAmB,aAAa;QAC3D;QACA,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,IAAI;YACjC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,wBAAwB;gBACzD,QAAQ,IAAI,CAAC,aAAa;gBAC1B;gBACA;YACJ;QACJ;QACA,OAAO;YACH;YACA,GAAG,WAAW;QAClB;IACJ;IACA;;;;;;;;;;;;;;;;;;;;KAoBC,GACD,MAAM,MAAM,cAAc,EAAE;QACxB,MAAM,SAAS,IAAI,CAAC,eAAe;QACnC,IAAI,OAAO,IAAI,KAAK,cAChB,MAAM,IAAI,MAAM;QACpB,MAAM,eAAe,MAAM,IAAI,CAAC,IAAI,CAAC,8CAA8C;YAAE,aAAa,OAAO,WAAW;YAAE;QAAe;QACrI,MAAM,SAAS,IAAI,wLAAA,CAAA,UAAU,CAAC;YAC1B,QAAQ,OAAO,MAAM;YACrB,WAAW,OAAO,SAAS;YAC3B,aAAa,aAAa,WAAW;YACrC,cAAc,aAAa,kBAAkB;QACjD,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc;QACpC,OAAO;YACH,aAAa,aAAa,WAAW;YACrC,cAAc,aAAa,kBAAkB;YAC7C,QAAQ,aAAa,OAAO;YAC5B,YAAY,aAAa,WAAW;YACpC;QACJ;IACJ;IACA;;;;;;;;;;;KAWC,GACD,MAAM,WAAW;QACb,MAAM,SAAS,IAAI,CAAC,eAAe;QACnC,IAAI,OAAO,IAAI,KAAK,cAChB,MAAM,IAAI,MAAM;QACpB,4CAA4C;QAC5C,MAAM,cAAc,IAAI,wLAAA,CAAA,UAAU,CAAC;YAAE,UAAU,OAAO,MAAM;YAAE,UAAU,OAAO,SAAS;QAAC,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc;QAC7H,MAAM,MAAM,MAAM,YAAY,IAAI,CAAC,wCAAwC;YAAE,YAAY;QAAqB;QAC9G,+BAA+B;QAC/B,OAAO,IAAI,wLAAA,CAAA,UAAU,CAAC,IAAI,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc;IAC7E;IACA,+BAA+B,GAC/B;;;;;;;;;;;;;;;;;;;;;KAqBC,GACD,uBAAuB,WAAW,EAAE,UAAU,CAAC,CAAC,EAAE;QAC9C,IAAI,IAAI;QACR,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC9B,MAAM,IAAI,MAAM,sHACZ;QACR;QACA,MAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,6LAAA,CAAA,eAAY,CAAC,oBAAoB,CAAC;QACtG,MAAM,eAAe,6LAAA,CAAA,eAAY,CAAC,eAAe;QACjD,MAAM,gBAAgB,6LAAA,CAAA,eAAY,CAAC,4BAA4B,CAAC;QAChE,MAAM,WAAW,CAAC,KAAK,QAAQ,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QACvE,MAAM,QAAQ,MAAM,OAAO,CAAC,YAAY,SAAS,IAAI,CAAC,OAAO;QAC7D,MAAM,MAAM,IAAI,IAAI;QACpB,MAAM,QAAQ;YACV,eAAe;YACf,WAAW,IAAI,CAAC,aAAa,CAAC,QAAQ;YACtC,cAAc;YACd;YACA,gBAAgB;YAChB,uBAAuB;YACvB;QACJ;QACA,uMAAA,CAAA,UAAmB,CAAC,mBAAmB,CAAC,KAAK;QAC7C,MAAM,SAAS;YACX,KAAK,IAAI,QAAQ;YACjB;YACA;YACA;QACJ;QACA,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,IAAI;YACjC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,wBAAwB;gBACzD,QAAQ,IAAI,CAAC,aAAa;gBAC1B;gBACA;YACJ;QACJ;QACA,OAAO;IACX;IACA;;;;;;;;;;;;;;;;;;;;;;;KAuBC,GACD,MAAM,gBAAgB,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE,EAAE;QACvD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC9B,MAAM,IAAI,MAAM,6DACZ;QACR;QACA,MAAM,oBAAoB,MAAM,IAAI,CAAC,IAAI,CAAC,0CAA0C;YAChF;YACA,eAAe;YACf,cAAc;YACd,YAAY;YACZ,WAAW,IAAI,CAAC,aAAa,CAAC,QAAQ;YACtC,eAAe,IAAI,CAAC,aAAa,CAAC,YAAY;QAClD;QACA,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC7C;IACA;;;;;;;;;KASC,GACD,MAAM,mBAAmB,YAAY,EAAE;QACnC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC9B,MAAM,IAAI,MAAM,6DACZ;QACR;QACA,MAAM,oBAAoB,MAAM,IAAI,CAAC,IAAI,CAAC,0CAA0C;YAChF,eAAe;YACf,YAAY;YACZ,WAAW,IAAI,CAAC,aAAa,CAAC,QAAQ;YACtC,eAAe,IAAI,CAAC,aAAa,CAAC,YAAY;QAClD;QACA,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC7C;IACA;;;;;KAKC,GACD,MAAM,kBAAkB,KAAK,EAAE,YAAY,cAAc,EAAE;QACvD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC9B,MAAM,IAAI,MAAM,6DACZ;QACR;QACA,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,2CAA2C;YAC9D,WAAW,IAAI,CAAC,aAAa,CAAC,QAAQ;YACtC,eAAe,IAAI,CAAC,aAAa,CAAC,YAAY;YAC9C;YACA,iBAAiB;QACrB;IACJ;IACA,6BAA6B,MAAM,EAAE;QACjC,MAAM,SAAS,IAAI,wLAAA,CAAA,UAAU,CAAC,OAAO,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc;QACpF,MAAM,QAAQ,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA,IAAK;QAClD,OAAO;YACH;YACA,WAAW,OAAO,UAAU;YAC5B,aAAa,OAAO,YAAY;YAChC;YACA,cAAc,OAAO,aAAa;QACtC;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7142, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/client/readwrite.js"], "sourcesContent": ["import TwitterApiv1ReadWrite from '../v1/client.v1.write';\nimport TwitterApiv2ReadWrite from '../v2/client.v2.write';\nimport TwitterApiReadOnly from './readonly';\n/**\n * Twitter v1.1 and v2 API client.\n */\nexport default class Twitter<PERSON><PERSON>ReadWrite extends TwitterApiReadOnly {\n    /* Direct access to subclients */\n    get v1() {\n        if (this._v1)\n            return this._v1;\n        return this._v1 = new TwitterApiv1ReadWrite(this);\n    }\n    get v2() {\n        if (this._v2)\n            return this._v2;\n        return this._v2 = new TwitterApiv2ReadWrite(this);\n    }\n    /**\n     * Get a client with read only rights.\n     */\n    get readOnly() {\n        return this;\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAIe,MAAM,4BAA4B,2KAAA,CAAA,UAAkB;IAC/D,+BAA+B,GAC/B,IAAI,KAAK;QACL,IAAI,IAAI,CAAC,GAAG,EACR,OAAO,IAAI,CAAC,GAAG;QACnB,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,oLAAA,CAAA,UAAqB,CAAC,IAAI;IACpD;IACA,IAAI,KAAK;QACL,IAAI,IAAI,CAAC,GAAG,EACR,OAAO,IAAI,CAAC,GAAG;QACnB,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,oLAAA,CAAA,UAAqB,CAAC,IAAI;IACpD;IACA;;KAEC,GACD,IAAI,WAAW;QACX,OAAO,IAAI;IACf;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7184, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/twitter-api-v2/dist/esm/index.js"], "sourcesContent": ["export { default as default } from './client';\nexport * from './client';\nexport * from './v1/client.v1';\nexport * from './v2/client.v2';\nexport * from './v2/includes.v2.helper';\nexport * from './v2-labs/client.v2.labs';\nexport * from './types';\nexport * from './paginators';\nexport * from './stream/TweetStream';\nexport * from './settings';\n"], "names": [], "mappings": ";AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": [0], "debugId": null}}]}