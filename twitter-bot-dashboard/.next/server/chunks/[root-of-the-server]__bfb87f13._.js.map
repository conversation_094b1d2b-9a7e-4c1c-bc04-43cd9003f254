{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/twitter.ts"], "sourcesContent": ["import { TwitterApi } from 'twitter-api-v2'\n\n// Twitter client configuration - only initialize if keys are available\nfunction getTwitterClient() {\n  if (!process.env.TWITTER_APP_KEY || !process.env.TWITTER_APP_SECRET ||\n      !process.env.TWITTER_ACCESS_TOKEN || !process.env.TWITTER_ACCESS_SECRET) {\n    throw new Error('Twitter API keys not configured')\n  }\n\n  return new TwitterApi({\n    appKey: process.env.TWITTER_APP_KEY,\n    appSecret: process.env.TWITTER_APP_SECRET,\n    accessToken: process.env.TWITTER_ACCESS_TOKEN,\n    accessSecret: process.env.TWITTER_ACCESS_SECRET,\n  })\n}\n\n// Twitter API v1.1 client for additional features\nfunction getTwitterV1Client() {\n  if (!process.env.TWITTER_APP_KEY || !process.env.TWITTER_APP_SECRET ||\n      !process.env.TWITTER_ACCESS_TOKEN || !process.env.TWITTER_ACCESS_SECRET) {\n    throw new Error('Twitter API keys not configured')\n  }\n\n  return new TwitterApi({\n    appKey: process.env.TWITTER_APP_KEY,\n    appSecret: process.env.TWITTER_APP_SECRET,\n    accessToken: process.env.TWITTER_ACCESS_TOKEN,\n    accessSecret: process.env.TWITTER_ACCESS_SECRET,\n  })\n}\n\nexport interface TwitterUserData {\n  id: string\n  username: string\n  name: string\n  followers_count: number\n  following_count: number\n  tweet_count: number\n  verified: boolean\n  profile_image_url?: string\n}\n\nexport interface TweetData {\n  id: string\n  text: string\n  created_at: string\n  public_metrics?: {\n    retweet_count: number\n    like_count: number\n    reply_count: number\n    quote_count: number\n    impression_count?: number\n  }\n}\n\nexport const twitterOperations = {\n  // Get current user information\n  async getCurrentUser(): Promise<TwitterUserData> {\n    try {\n      const twitterClient = getTwitterClient()\n      const user = await twitterClient.v2.me({\n        'user.fields': ['public_metrics', 'verified', 'profile_image_url']\n      })\n\n      return {\n        id: user.data.id,\n        username: user.data.username,\n        name: user.data.name,\n        followers_count: user.data.public_metrics?.followers_count || 0,\n        following_count: user.data.public_metrics?.following_count || 0,\n        tweet_count: user.data.public_metrics?.tweet_count || 0,\n        verified: user.data.verified || false,\n        profile_image_url: user.data.profile_image_url\n      }\n    } catch (error) {\n      console.error('Error fetching current user:', error)\n      throw error\n    }\n  },\n\n  // Post a tweet\n  async postTweet(content: string): Promise<TweetData> {\n    try {\n      const twitterClient = getTwitterClient()\n      const tweet = await twitterClient.v2.tweet(content)\n\n      return {\n        id: tweet.data.id,\n        text: content,\n        created_at: new Date().toISOString()\n      }\n    } catch (error) {\n      console.error('Error posting tweet:', error)\n      throw error\n    }\n  },\n\n  // Get recent tweets with metrics\n  async getRecentTweets(): Promise<TweetData[]> {\n    try {\n      // For now, return empty array to avoid API complexity during build\n      // This will be populated with actual data when API keys are configured\n      return []\n    } catch (error) {\n      console.error('Error fetching recent tweets:', error)\n      return []\n    }\n  },\n\n  // Get tweet analytics\n  async getTweetAnalytics(tweetId: string): Promise<TweetData | null> {\n    try {\n      const twitterClient = getTwitterClient()\n      const tweet = await twitterClient.v2.singleTweet(tweetId, {\n        'tweet.fields': ['created_at', 'public_metrics']\n      })\n      \n      if (!tweet.data) return null\n      \n      return {\n        id: tweet.data.id,\n        text: tweet.data.text,\n        created_at: tweet.data.created_at || new Date().toISOString(),\n        public_metrics: tweet.data.public_metrics\n      }\n    } catch (error) {\n      console.error('Error fetching tweet analytics:', error)\n      return null\n    }\n  },\n\n  // Calculate total impressions from recent tweets\n  async getTotalImpressions(): Promise<number> {\n    try {\n      const tweets = await this.getRecentTweets() // Get more tweets for better calculation\n      \n      // Filter tweets from the last 30 days\n      const cutoffDate = new Date()\n      cutoffDate.setDate(cutoffDate.getDate() - 30)\n      \n      const recentTweets = tweets.filter(tweet => \n        new Date(tweet.created_at) >= cutoffDate\n      )\n      \n      // Sum up impressions (if available) or estimate based on engagement\n      let totalImpressions = 0\n      for (const tweet of recentTweets) {\n        if (tweet.public_metrics?.impression_count) {\n          totalImpressions += tweet.public_metrics.impression_count\n        } else if (tweet.public_metrics) {\n          // Estimate impressions based on engagement (rough calculation)\n          const engagement = (tweet.public_metrics.like_count || 0) + \n                           (tweet.public_metrics.retweet_count || 0) + \n                           (tweet.public_metrics.reply_count || 0)\n          totalImpressions += Math.max(engagement * 10, 100) // Rough estimate\n        }\n      }\n      \n      return totalImpressions\n    } catch (error) {\n      console.error('Error calculating total impressions:', error)\n      return 0\n    }\n  },\n\n  // Get authentication status\n  async getAuthStatus(): Promise<{ authenticated: boolean }> {\n    try {\n      await this.getCurrentUser()\n      return { authenticated: true }\n    } catch (error) {\n      console.error('Error checking auth status:', error)\n      return { authenticated: false }\n    }\n  }\n}\n\nexport { getTwitterClient, getTwitterV1Client }\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;;AAEA,uEAAuE;AACvE,SAAS;IACP,IAAI,CAAC,QAAQ,GAAG,CAAC,eAAe,IAAI,CAAC,QAAQ,GAAG,CAAC,kBAAkB,IAC/D,CAAC,QAAQ,GAAG,CAAC,oBAAoB,IAAI,CAAC,QAAQ,GAAG,CAAC,qBAAqB,EAAE;QAC3E,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,IAAI,wLAAA,CAAA,aAAU,CAAC;QACpB,QAAQ,QAAQ,GAAG,CAAC,eAAe;QACnC,WAAW,QAAQ,GAAG,CAAC,kBAAkB;QACzC,aAAa,QAAQ,GAAG,CAAC,oBAAoB;QAC7C,cAAc,QAAQ,GAAG,CAAC,qBAAqB;IACjD;AACF;AAEA,kDAAkD;AAClD,SAAS;IACP,IAAI,CAAC,QAAQ,GAAG,CAAC,eAAe,IAAI,CAAC,QAAQ,GAAG,CAAC,kBAAkB,IAC/D,CAAC,QAAQ,GAAG,CAAC,oBAAoB,IAAI,CAAC,QAAQ,GAAG,CAAC,qBAAqB,EAAE;QAC3E,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,IAAI,wLAAA,CAAA,aAAU,CAAC;QACpB,QAAQ,QAAQ,GAAG,CAAC,eAAe;QACnC,WAAW,QAAQ,GAAG,CAAC,kBAAkB;QACzC,aAAa,QAAQ,GAAG,CAAC,oBAAoB;QAC7C,cAAc,QAAQ,GAAG,CAAC,qBAAqB;IACjD;AACF;AA0BO,MAAM,oBAAoB;IAC/B,+BAA+B;IAC/B,MAAM;QACJ,IAAI;YACF,MAAM,gBAAgB;YACtB,MAAM,OAAO,MAAM,cAAc,EAAE,CAAC,EAAE,CAAC;gBACrC,eAAe;oBAAC;oBAAkB;oBAAY;iBAAoB;YACpE;YAEA,OAAO;gBACL,IAAI,KAAK,IAAI,CAAC,EAAE;gBAChB,UAAU,KAAK,IAAI,CAAC,QAAQ;gBAC5B,MAAM,KAAK,IAAI,CAAC,IAAI;gBACpB,iBAAiB,KAAK,IAAI,CAAC,cAAc,EAAE,mBAAmB;gBAC9D,iBAAiB,KAAK,IAAI,CAAC,cAAc,EAAE,mBAAmB;gBAC9D,aAAa,KAAK,IAAI,CAAC,cAAc,EAAE,eAAe;gBACtD,UAAU,KAAK,IAAI,CAAC,QAAQ,IAAI;gBAChC,mBAAmB,KAAK,IAAI,CAAC,iBAAiB;YAChD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;IAEA,eAAe;IACf,MAAM,WAAU,OAAe;QAC7B,IAAI;YACF,MAAM,gBAAgB;YACtB,MAAM,QAAQ,MAAM,cAAc,EAAE,CAAC,KAAK,CAAC;YAE3C,OAAO;gBACL,IAAI,MAAM,IAAI,CAAC,EAAE;gBACjB,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,iCAAiC;IACjC,MAAM;QACJ,IAAI;YACF,mEAAmE;YACnE,uEAAuE;YACvE,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,EAAE;QACX;IACF;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,OAAe;QACrC,IAAI;YACF,MAAM,gBAAgB;YACtB,MAAM,QAAQ,MAAM,cAAc,EAAE,CAAC,WAAW,CAAC,SAAS;gBACxD,gBAAgB;oBAAC;oBAAc;iBAAiB;YAClD;YAEA,IAAI,CAAC,MAAM,IAAI,EAAE,OAAO;YAExB,OAAO;gBACL,IAAI,MAAM,IAAI,CAAC,EAAE;gBACjB,MAAM,MAAM,IAAI,CAAC,IAAI;gBACrB,YAAY,MAAM,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;gBAC3D,gBAAgB,MAAM,IAAI,CAAC,cAAc;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;QACT;IACF;IAEA,iDAAiD;IACjD,MAAM;QACJ,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,eAAe,GAAG,yCAAyC;;YAErF,sCAAsC;YACtC,MAAM,aAAa,IAAI;YACvB,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;YAE1C,MAAM,eAAe,OAAO,MAAM,CAAC,CAAA,QACjC,IAAI,KAAK,MAAM,UAAU,KAAK;YAGhC,oEAAoE;YACpE,IAAI,mBAAmB;YACvB,KAAK,MAAM,SAAS,aAAc;gBAChC,IAAI,MAAM,cAAc,EAAE,kBAAkB;oBAC1C,oBAAoB,MAAM,cAAc,CAAC,gBAAgB;gBAC3D,OAAO,IAAI,MAAM,cAAc,EAAE;oBAC/B,+DAA+D;oBAC/D,MAAM,aAAa,CAAC,MAAM,cAAc,CAAC,UAAU,IAAI,CAAC,IACvC,CAAC,MAAM,cAAc,CAAC,aAAa,IAAI,CAAC,IACxC,CAAC,MAAM,cAAc,CAAC,WAAW,IAAI,CAAC;oBACvD,oBAAoB,KAAK,GAAG,CAAC,aAAa,IAAI,KAAK,iBAAiB;;gBACtE;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,MAAM;QACJ,IAAI;YACF,MAAM,IAAI,CAAC,cAAc;YACzB,OAAO;gBAAE,eAAe;YAAK;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;gBAAE,eAAe;YAAM;QAChC;IACF;AACF", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n\n// Client for browser/client-side operations\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Admin client for server-side operations\nexport const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey)\n\n// Database types\nexport interface RSSFeed {\n  id: string\n  name: string\n  url: string\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface PostedTweet {\n  id: string\n  tweet_id?: string\n  content: string\n  original_url?: string\n  original_title?: string\n  rss_feed_id?: string\n  impressions: number\n  retweets: number\n  likes: number\n  replies: number\n  posted_at: string\n  created_at: string\n}\n\nexport interface TwitterAnalytics {\n  id: string\n  followers_count: number\n  following_count: number\n  total_tweets: number\n  total_impressions: number\n  total_engagements: number\n  recorded_at: string\n}\n\nexport interface UserPreferences {\n  id: string\n  max_topics_to_select: number\n  posting_interval_minutes: number\n  ai_tone: string\n  include_personal_touch: boolean\n  auto_post_enabled: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface ContentQueue {\n  id: string\n  original_url: string\n  original_title?: string\n  original_content?: string\n  ai_generated_content?: string\n  short_hook?: string\n  long_hook?: string\n  personal_touch?: string\n  rss_feed_id?: string\n  is_selected: boolean\n  is_posted: boolean\n  priority_score: number\n  created_at: string\n  updated_at: string\n}\n\n// Database operations\nexport const dbOperations = {\n  // RSS Feeds\n  async getRSSFeeds() {\n    const { data, error } = await supabase\n      .from('rss_feeds')\n      .select('*')\n      .order('created_at', { ascending: false })\n    \n    if (error) throw error\n    return data as RSSFeed[]\n  },\n\n  async addRSSFeed(name: string, url: string) {\n    const { data, error } = await supabase\n      .from('rss_feeds')\n      .insert({ name, url })\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as RSSFeed\n  },\n\n  async updateRSSFeed(id: string, updates: Partial<RSSFeed>) {\n    const { data, error } = await supabase\n      .from('rss_feeds')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as RSSFeed\n  },\n\n  // Posted Tweets\n  async getPostedTweets(limit = 50) {\n    const { data, error } = await supabase\n      .from('posted_tweets')\n      .select('*')\n      .order('posted_at', { ascending: false })\n      .limit(limit)\n    \n    if (error) throw error\n    return data as PostedTweet[]\n  },\n\n  async addPostedTweet(tweet: Omit<PostedTweet, 'id' | 'created_at'>) {\n    const { data, error } = await supabase\n      .from('posted_tweets')\n      .insert(tweet)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as PostedTweet\n  },\n\n  // Twitter Analytics\n  async getLatestAnalytics() {\n    const { data, error } = await supabase\n      .from('twitter_analytics')\n      .select('*')\n      .order('recorded_at', { ascending: false })\n      .limit(1)\n      .single()\n    \n    if (error && error.code !== 'PGRST116') throw error\n    return data as TwitterAnalytics | null\n  },\n\n  async addAnalytics(analytics: Omit<TwitterAnalytics, 'id' | 'recorded_at'>) {\n    const { data, error } = await supabase\n      .from('twitter_analytics')\n      .insert(analytics)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as TwitterAnalytics\n  },\n\n  // User Preferences\n  async getUserPreferences() {\n    const { data, error } = await supabase\n      .from('user_preferences')\n      .select('*')\n      .limit(1)\n      .single()\n    \n    if (error && error.code !== 'PGRST116') throw error\n    return data as UserPreferences | null\n  },\n\n  async updateUserPreferences(updates: Partial<UserPreferences>) {\n    const { data, error } = await supabase\n      .from('user_preferences')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as UserPreferences\n  },\n\n  // Content Queue\n  async getContentQueue(limit = 20) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .select('*')\n      .order('created_at', { ascending: false })\n      .limit(limit)\n    \n    if (error) throw error\n    return data as ContentQueue[]\n  },\n\n  async addToContentQueue(content: Omit<ContentQueue, 'id' | 'created_at' | 'updated_at'>) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .insert(content)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as ContentQueue\n  },\n\n  async updateContentQueue(id: string, updates: Partial<ContentQueue>) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data as ContentQueue\n  },\n\n  async getSelectedContent() {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .select('*')\n      .eq('is_selected', true)\n      .eq('is_posted', false)\n      .order('priority_score', { ascending: false })\n\n    if (error) throw error\n    return data as ContentQueue[]\n  },\n\n  async markContentAsPosted(ids: string[]) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .update({\n        is_posted: true,\n        is_selected: false,\n        updated_at: new Date().toISOString()\n      })\n      .in('id', ids)\n      .select()\n\n    if (error) throw error\n    return data as ContentQueue[]\n  },\n\n  // Delete RSS Feed\n  async deleteRSSFeed(id: string) {\n    const { error } = await supabase\n      .from('rss_feeds')\n      .delete()\n      .eq('id', id)\n\n    if (error) throw error\n    return true\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM;AACN,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAGzD,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAkEhD,MAAM,eAAe;IAC1B,YAAY;IACZ,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,YAAW,IAAY,EAAE,GAAW;QACxC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC;YAAE;YAAM;QAAI,GACnB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,eAAc,EAAU,EAAE,OAAyB;QACvD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,gBAAgB;IAChB,MAAM,iBAAgB,QAAQ,EAAE;QAC9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,KAAK,CAAC,aAAa;YAAE,WAAW;QAAM,GACtC,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,gBAAe,KAA6C;QAChE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,OACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,oBAAoB;IACpB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,KAAK,CAAC,eAAe;YAAE,WAAW;QAAM,GACxC,KAAK,CAAC,GACN,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY,MAAM;QAC9C,OAAO;IACT;IAEA,MAAM,cAAa,SAAuD;QACxE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,WACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,mBAAmB;IACnB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,KAAK,CAAC,GACN,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY,MAAM;QAC9C,OAAO;IACT;IAEA,MAAM,uBAAsB,OAAiC;QAC3D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,oBACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,gBAAgB;IAChB,MAAM,iBAAgB,QAAQ,EAAE;QAC9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,mBAAkB,OAA+D;QACrF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,SACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,oBAAmB,EAAU,EAAE,OAA8B;QACjE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,MAClB,EAAE,CAAC,aAAa,OAChB,KAAK,CAAC,kBAAkB;YAAE,WAAW;QAAM;QAE9C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,qBAAoB,GAAa;QACrC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,WAAW;YACX,aAAa;YACb,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,KACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,eAAc,EAAU;QAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,aACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/twitter/stats/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport { twitterOperations } from '@/lib/twitter'\nimport { dbOperations } from '@/lib/supabase'\n\nexport async function GET() {\n  try {\n    // Get current user data from Twitter\n    const userData = await twitterOperations.getCurrentUser()\n    \n    // Get recent tweets for engagement calculation\n    const recentTweets = await twitterOperations.getRecentTweets()\n    \n    // Calculate total engagements from recent tweets\n    let totalEngagements = 0\n    for (const tweet of recentTweets) {\n      if (tweet.public_metrics) {\n        totalEngagements += (tweet.public_metrics.like_count || 0) +\n                           (tweet.public_metrics.retweet_count || 0) +\n                           (tweet.public_metrics.reply_count || 0) +\n                           (tweet.public_metrics.quote_count || 0)\n      }\n    }\n    \n    // Get total impressions (estimated)\n    const totalImpressions = await twitterOperations.getTotalImpressions()\n    \n    // Get posted tweets count from database\n    const postedTweets = await dbOperations.getPostedTweets(1000)\n    \n    // Calculate database-stored impressions\n    const dbImpressions = postedTweets.reduce((sum, tweet) => sum + tweet.impressions, 0)\n    \n    // Store current analytics in database\n    await dbOperations.addAnalytics({\n      followers_count: userData.followers_count,\n      following_count: userData.following_count,\n      total_tweets: userData.tweet_count,\n      total_impressions: Math.max(totalImpressions, dbImpressions),\n      total_engagements: totalEngagements\n    })\n    \n    const stats = {\n      followers: userData.followers_count,\n      totalTweets: userData.tweet_count,\n      totalImpressions: Math.max(totalImpressions, dbImpressions),\n      totalEngagements: totalEngagements,\n      recentTweets: recentTweets.length,\n      username: userData.username,\n      name: userData.name,\n      verified: userData.verified,\n      profileImage: userData.profile_image_url\n    }\n    \n    return NextResponse.json(stats)\n  } catch (error) {\n    console.error('Error fetching Twitter stats:', error)\n    \n    // Return fallback data from database if Twitter API fails\n    try {\n      const latestAnalytics = await dbOperations.getLatestAnalytics()\n      const postedTweets = await dbOperations.getPostedTweets(1000)\n      \n      if (latestAnalytics) {\n        return NextResponse.json({\n          followers: latestAnalytics.followers_count,\n          totalTweets: latestAnalytics.total_tweets,\n          totalImpressions: latestAnalytics.total_impressions,\n          totalEngagements: latestAnalytics.total_engagements,\n          recentTweets: postedTweets.length,\n          username: 'N/A',\n          name: 'N/A',\n          verified: false,\n          profileImage: null\n        })\n      }\n    } catch (dbError) {\n      console.error('Error fetching fallback data:', dbError)\n    }\n    \n    return NextResponse.json(\n      { error: 'Failed to fetch Twitter stats' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST() {\n  try {\n    // Force refresh of Twitter stats\n    const userData = await twitterOperations.getCurrentUser()\n    const totalImpressions = await twitterOperations.getTotalImpressions()\n    \n    // Update analytics in database\n    await dbOperations.addAnalytics({\n      followers_count: userData.followers_count,\n      following_count: userData.following_count,\n      total_tweets: userData.tweet_count,\n      total_impressions: totalImpressions,\n      total_engagements: 0 // Will be calculated separately\n    })\n    \n    return NextResponse.json({ success: true })\n  } catch (error) {\n    console.error('Error refreshing Twitter stats:', error)\n    return NextResponse.json(\n      { error: 'Failed to refresh Twitter stats' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe;IACpB,IAAI;QACF,qCAAqC;QACrC,MAAM,WAAW,MAAM,uHAAA,CAAA,oBAAiB,CAAC,cAAc;QAEvD,+CAA+C;QAC/C,MAAM,eAAe,MAAM,uHAAA,CAAA,oBAAiB,CAAC,eAAe;QAE5D,iDAAiD;QACjD,IAAI,mBAAmB;QACvB,KAAK,MAAM,SAAS,aAAc;YAChC,IAAI,MAAM,cAAc,EAAE;gBACxB,oBAAoB,CAAC,MAAM,cAAc,CAAC,UAAU,IAAI,CAAC,IACtC,CAAC,MAAM,cAAc,CAAC,aAAa,IAAI,CAAC,IACxC,CAAC,MAAM,cAAc,CAAC,WAAW,IAAI,CAAC,IACtC,CAAC,MAAM,cAAc,CAAC,WAAW,IAAI,CAAC;YAC3D;QACF;QAEA,oCAAoC;QACpC,MAAM,mBAAmB,MAAM,uHAAA,CAAA,oBAAiB,CAAC,mBAAmB;QAEpE,wCAAwC;QACxC,MAAM,eAAe,MAAM,wHAAA,CAAA,eAAY,CAAC,eAAe,CAAC;QAExD,wCAAwC;QACxC,MAAM,gBAAgB,aAAa,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,WAAW,EAAE;QAEnF,sCAAsC;QACtC,MAAM,wHAAA,CAAA,eAAY,CAAC,YAAY,CAAC;YAC9B,iBAAiB,SAAS,eAAe;YACzC,iBAAiB,SAAS,eAAe;YACzC,cAAc,SAAS,WAAW;YAClC,mBAAmB,KAAK,GAAG,CAAC,kBAAkB;YAC9C,mBAAmB;QACrB;QAEA,MAAM,QAAQ;YACZ,WAAW,SAAS,eAAe;YACnC,aAAa,SAAS,WAAW;YACjC,kBAAkB,KAAK,GAAG,CAAC,kBAAkB;YAC7C,kBAAkB;YAClB,cAAc,aAAa,MAAM;YACjC,UAAU,SAAS,QAAQ;YAC3B,MAAM,SAAS,IAAI;YACnB,UAAU,SAAS,QAAQ;YAC3B,cAAc,SAAS,iBAAiB;QAC1C;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAE/C,0DAA0D;QAC1D,IAAI;YACF,MAAM,kBAAkB,MAAM,wHAAA,CAAA,eAAY,CAAC,kBAAkB;YAC7D,MAAM,eAAe,MAAM,wHAAA,CAAA,eAAY,CAAC,eAAe,CAAC;YAExD,IAAI,iBAAiB;gBACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,WAAW,gBAAgB,eAAe;oBAC1C,aAAa,gBAAgB,YAAY;oBACzC,kBAAkB,gBAAgB,iBAAiB;oBACnD,kBAAkB,gBAAgB,iBAAiB;oBACnD,cAAc,aAAa,MAAM;oBACjC,UAAU;oBACV,MAAM;oBACN,UAAU;oBACV,cAAc;gBAChB;YACF;QACF,EAAE,OAAO,SAAS;YAChB,QAAQ,KAAK,CAAC,iCAAiC;QACjD;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgC,GACzC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,IAAI;QACF,iCAAiC;QACjC,MAAM,WAAW,MAAM,uHAAA,CAAA,oBAAiB,CAAC,cAAc;QACvD,MAAM,mBAAmB,MAAM,uHAAA,CAAA,oBAAiB,CAAC,mBAAmB;QAEpE,+BAA+B;QAC/B,MAAM,wHAAA,CAAA,eAAY,CAAC,YAAY,CAAC;YAC9B,iBAAiB,SAAS,eAAe;YACzC,iBAAiB,SAAS,eAAe;YACzC,cAAc,SAAS,WAAW;YAClC,mBAAmB;YACnB,mBAAmB,EAAE,gCAAgC;QACvD;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAkC,GAC3C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}