{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n\n// Client for browser/client-side operations\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Admin client for server-side operations\nexport const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey)\n\n// Database types\nexport interface RSSFeed {\n  id: string\n  name: string\n  url: string\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface PostedTweet {\n  id: string\n  tweet_id?: string\n  content: string\n  original_url?: string\n  original_title?: string\n  rss_feed_id?: string\n  impressions: number\n  retweets: number\n  likes: number\n  replies: number\n  posted_at: string\n  created_at: string\n}\n\nexport interface TwitterAnalytics {\n  id: string\n  followers_count: number\n  following_count: number\n  total_tweets: number\n  total_impressions: number\n  total_engagements: number\n  recorded_at: string\n}\n\nexport interface UserPreferences {\n  id: string\n  max_topics_to_select: number\n  posting_interval_minutes: number\n  ai_tone: string\n  include_personal_touch: boolean\n  auto_post_enabled: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface ContentQueue {\n  id: string\n  original_url: string\n  original_title?: string\n  original_content?: string\n  ai_generated_content?: string\n  short_hook?: string\n  long_hook?: string\n  personal_touch?: string\n  rss_feed_id?: string\n  is_selected: boolean\n  is_posted: boolean\n  priority_score: number\n  created_at: string\n  updated_at: string\n}\n\n// Database operations\nexport const dbOperations = {\n  // RSS Feeds\n  async getRSSFeeds() {\n    const { data, error } = await supabase\n      .from('rss_feeds')\n      .select('*')\n      .order('created_at', { ascending: false })\n    \n    if (error) throw error\n    return data as RSSFeed[]\n  },\n\n  async addRSSFeed(name: string, url: string) {\n    const { data, error } = await supabase\n      .from('rss_feeds')\n      .insert({ name, url })\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as RSSFeed\n  },\n\n  async updateRSSFeed(id: string, updates: Partial<RSSFeed>) {\n    const { data, error } = await supabase\n      .from('rss_feeds')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as RSSFeed\n  },\n\n  // Posted Tweets\n  async getPostedTweets(limit = 50) {\n    const { data, error } = await supabase\n      .from('posted_tweets')\n      .select('*')\n      .order('posted_at', { ascending: false })\n      .limit(limit)\n    \n    if (error) throw error\n    return data as PostedTweet[]\n  },\n\n  async addPostedTweet(tweet: Omit<PostedTweet, 'id' | 'created_at'>) {\n    const { data, error } = await supabase\n      .from('posted_tweets')\n      .insert(tweet)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as PostedTweet\n  },\n\n  // Twitter Analytics\n  async getLatestAnalytics() {\n    const { data, error } = await supabase\n      .from('twitter_analytics')\n      .select('*')\n      .order('recorded_at', { ascending: false })\n      .limit(1)\n      .single()\n    \n    if (error && error.code !== 'PGRST116') throw error\n    return data as TwitterAnalytics | null\n  },\n\n  async addAnalytics(analytics: Omit<TwitterAnalytics, 'id' | 'recorded_at'>) {\n    const { data, error } = await supabase\n      .from('twitter_analytics')\n      .insert(analytics)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as TwitterAnalytics\n  },\n\n  // User Preferences\n  async getUserPreferences() {\n    const { data, error } = await supabase\n      .from('user_preferences')\n      .select('*')\n      .limit(1)\n      .single()\n    \n    if (error && error.code !== 'PGRST116') throw error\n    return data as UserPreferences | null\n  },\n\n  async updateUserPreferences(updates: Partial<UserPreferences>) {\n    const { data, error } = await supabase\n      .from('user_preferences')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as UserPreferences\n  },\n\n  // Content Queue\n  async getContentQueue(limit = 20) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .select('*')\n      .order('created_at', { ascending: false })\n      .limit(limit)\n    \n    if (error) throw error\n    return data as ContentQueue[]\n  },\n\n  async addToContentQueue(content: Omit<ContentQueue, 'id' | 'created_at' | 'updated_at'>) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .insert(content)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as ContentQueue\n  },\n\n  async updateContentQueue(id: string, updates: Partial<ContentQueue>) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data as ContentQueue\n  },\n\n  async getSelectedContent() {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .select('*')\n      .eq('is_selected', true)\n      .eq('is_posted', false)\n      .order('priority_score', { ascending: false })\n\n    if (error) throw error\n    return data as ContentQueue[]\n  },\n\n  async markContentAsPosted(ids: string[]) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .update({\n        is_posted: true,\n        is_selected: false,\n        updated_at: new Date().toISOString()\n      })\n      .in('id', ids)\n      .select()\n\n    if (error) throw error\n    return data as ContentQueue[]\n  },\n\n  // Delete RSS Feed\n  async deleteRSSFeed(id: string) {\n    const { error } = await supabase\n      .from('rss_feeds')\n      .delete()\n      .eq('id', id)\n\n    if (error) throw error\n    return true\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM;AACN,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAGzD,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAkEhD,MAAM,eAAe;IAC1B,YAAY;IACZ,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,YAAW,IAAY,EAAE,GAAW;QACxC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC;YAAE;YAAM;QAAI,GACnB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,eAAc,EAAU,EAAE,OAAyB;QACvD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,gBAAgB;IAChB,MAAM,iBAAgB,QAAQ,EAAE;QAC9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,KAAK,CAAC,aAAa;YAAE,WAAW;QAAM,GACtC,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,gBAAe,KAA6C;QAChE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,OACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,oBAAoB;IACpB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,KAAK,CAAC,eAAe;YAAE,WAAW;QAAM,GACxC,KAAK,CAAC,GACN,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY,MAAM;QAC9C,OAAO;IACT;IAEA,MAAM,cAAa,SAAuD;QACxE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,WACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,mBAAmB;IACnB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,KAAK,CAAC,GACN,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY,MAAM;QAC9C,OAAO;IACT;IAEA,MAAM,uBAAsB,OAAiC;QAC3D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,oBACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,gBAAgB;IAChB,MAAM,iBAAgB,QAAQ,EAAE;QAC9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,mBAAkB,OAA+D;QACrF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,SACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,oBAAmB,EAAU,EAAE,OAA8B;QACjE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,MAClB,EAAE,CAAC,aAAa,OAChB,KAAK,CAAC,kBAAkB;YAAE,WAAW;QAAM;QAE9C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,qBAAoB,GAAa;QACrC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,WAAW;YACX,aAAa;YACb,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,KACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,eAAc,EAAU;QAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,aACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/rss.ts"], "sourcesContent": ["import Parser from 'rss-parser'\nimport { extract } from '@extractus/article-extractor'\n\nconst parser = new Parser()\n\nexport interface RSSItem {\n  title: string\n  link: string\n  pubDate?: string\n  contentSnippet?: string\n  content?: string\n  guid?: string\n  categories?: string[]\n  author?: string\n}\n\nexport interface ProcessedContent {\n  title: string\n  url: string\n  content: string\n  publishedAt: string\n  categories: string[]\n  author?: string\n  guid: string\n}\n\nexport const rssOperations = {\n  // Parse RSS feed and return items\n  async parseFeed(feedUrl: string): Promise<RSSItem[]> {\n    try {\n      const feed = await parser.parseURL(feedUrl)\n      return feed.items.map(item => ({\n        title: item.title || '',\n        link: item.link || '',\n        pubDate: item.pubDate,\n        contentSnippet: item.contentSnippet,\n        content: item.content,\n        guid: item.guid || item.link || '',\n        categories: item.categories || [],\n        author: item.author\n      }))\n    } catch (error) {\n      console.error(`Error parsing RSS feed ${feedUrl}:`, error)\n      throw error\n    }\n  },\n\n  // Extract full content from article URL\n  async extractContent(url: string): Promise<string | null> {\n    try {\n      const article = await extract(url)\n      return article?.content || null\n    } catch (error) {\n      console.error(`Error extracting content from ${url}:`, error)\n      return null\n    }\n  },\n\n  // Process RSS items and extract full content\n  async processRSSItems(items: RSSItem[]): Promise<ProcessedContent[]> {\n    const processedItems: ProcessedContent[] = []\n    \n    for (const item of items) {\n      try {\n        const fullContent = await this.extractContent(item.link)\n        \n        if (fullContent) {\n          processedItems.push({\n            title: item.title,\n            url: item.link,\n            content: fullContent,\n            publishedAt: item.pubDate || new Date().toISOString(),\n            categories: item.categories || [],\n            author: item.author,\n            guid: item.guid || item.link\n          })\n        }\n      } catch (error) {\n        console.error(`Error processing item ${item.title}:`, error)\n        // Continue with next item\n      }\n    }\n    \n    return processedItems\n  },\n\n  // Deduplicate content based on URL and title similarity\n  deduplicateContent(items: ProcessedContent[]): ProcessedContent[] {\n    const seen = new Set<string>()\n    const deduplicated: ProcessedContent[] = []\n    \n    for (const item of items) {\n      // Create a key based on URL and normalized title\n      const normalizedTitle = item.title.toLowerCase().replace(/[^\\w\\s]/g, '').trim()\n      const key = `${item.url}|${normalizedTitle}`\n      \n      if (!seen.has(key)) {\n        seen.add(key)\n        deduplicated.push(item)\n      }\n    }\n    \n    return deduplicated\n  },\n\n  // Get content from multiple RSS feeds and deduplicate\n  async getAggregatedContent(feedUrls: string[]): Promise<ProcessedContent[]> {\n    const allItems: ProcessedContent[] = []\n    \n    for (const feedUrl of feedUrls) {\n      try {\n        const items = await this.parseFeed(feedUrl)\n        const processedItems = await this.processRSSItems(items)\n        allItems.push(...processedItems)\n      } catch (error) {\n        console.error(`Error processing feed ${feedUrl}:`, error)\n        // Continue with other feeds\n      }\n    }\n    \n    // Deduplicate and sort by publication date\n    const deduplicated = this.deduplicateContent(allItems)\n    return deduplicated.sort((a, b) => \n      new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()\n    )\n  },\n\n  // Filter content by keywords or categories\n  filterContent(items: ProcessedContent[], keywords: string[] = []): ProcessedContent[] {\n    if (keywords.length === 0) return items\n    \n    return items.filter(item => {\n      const searchText = `${item.title} ${item.content} ${item.categories.join(' ')}`.toLowerCase()\n      return keywords.some(keyword => \n        searchText.includes(keyword.toLowerCase())\n      )\n    })\n  },\n\n  // Score content based on various factors\n  scoreContent(items: ProcessedContent[]): (ProcessedContent & { score: number })[] {\n    return items.map(item => {\n      let score = 0\n      \n      // Recency score (newer content gets higher score)\n      const ageInHours = (Date.now() - new Date(item.publishedAt).getTime()) / (1000 * 60 * 60)\n      score += Math.max(0, 100 - ageInHours) // Max 100 points for very recent content\n      \n      // Title quality score\n      const titleWords = item.title.split(' ').length\n      if (titleWords >= 5 && titleWords <= 15) score += 20 // Optimal title length\n      \n      // Content length score\n      const contentWords = item.content.split(' ').length\n      if (contentWords >= 200 && contentWords <= 2000) score += 30 // Good content length\n      \n      // Category relevance (tech-related categories get bonus)\n      const techKeywords = ['tech', 'technology', 'ai', 'startup', 'innovation', 'software', 'app']\n      const hasRelevantCategory = item.categories.some(cat => \n        techKeywords.some(keyword => cat.toLowerCase().includes(keyword))\n      )\n      if (hasRelevantCategory) score += 25\n      \n      return { ...item, score }\n    }).sort((a, b) => b.score - a.score)\n  }\n}\n\nexport default rssOperations\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAEA,MAAM,SAAS,IAAI,wIAAA,CAAA,UAAM;AAuBlB,MAAM,gBAAgB;IAC3B,kCAAkC;IAClC,MAAM,WAAU,OAAe;QAC7B,IAAI;YACF,MAAM,OAAO,MAAM,OAAO,QAAQ,CAAC;YACnC,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC7B,OAAO,KAAK,KAAK,IAAI;oBACrB,MAAM,KAAK,IAAI,IAAI;oBACnB,SAAS,KAAK,OAAO;oBACrB,gBAAgB,KAAK,cAAc;oBACnC,SAAS,KAAK,OAAO;oBACrB,MAAM,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI;oBAChC,YAAY,KAAK,UAAU,IAAI,EAAE;oBACjC,QAAQ,KAAK,MAAM;gBACrB,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAC,EAAE;YACpD,MAAM;QACR;IACF;IAEA,wCAAwC;IACxC,MAAM,gBAAe,GAAW;QAC9B,IAAI;YACF,MAAM,UAAU,MAAM,CAAA,GAAA,oLAAA,CAAA,UAAO,AAAD,EAAE;YAC9B,OAAO,SAAS,WAAW;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,IAAI,CAAC,CAAC,EAAE;YACvD,OAAO;QACT;IACF;IAEA,6CAA6C;IAC7C,MAAM,iBAAgB,KAAgB;QACpC,MAAM,iBAAqC,EAAE;QAE7C,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI;gBACF,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI;gBAEvD,IAAI,aAAa;oBACf,eAAe,IAAI,CAAC;wBAClB,OAAO,KAAK,KAAK;wBACjB,KAAK,KAAK,IAAI;wBACd,SAAS;wBACT,aAAa,KAAK,OAAO,IAAI,IAAI,OAAO,WAAW;wBACnD,YAAY,KAAK,UAAU,IAAI,EAAE;wBACjC,QAAQ,KAAK,MAAM;wBACnB,MAAM,KAAK,IAAI,IAAI,KAAK,IAAI;oBAC9B;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;YACtD,0BAA0B;YAC5B;QACF;QAEA,OAAO;IACT;IAEA,wDAAwD;IACxD,oBAAmB,KAAyB;QAC1C,MAAM,OAAO,IAAI;QACjB,MAAM,eAAmC,EAAE;QAE3C,KAAK,MAAM,QAAQ,MAAO;YACxB,iDAAiD;YACjD,MAAM,kBAAkB,KAAK,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,YAAY,IAAI,IAAI;YAC7E,MAAM,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC,EAAE,iBAAiB;YAE5C,IAAI,CAAC,KAAK,GAAG,CAAC,MAAM;gBAClB,KAAK,GAAG,CAAC;gBACT,aAAa,IAAI,CAAC;YACpB;QACF;QAEA,OAAO;IACT;IAEA,sDAAsD;IACtD,MAAM,sBAAqB,QAAkB;QAC3C,MAAM,WAA+B,EAAE;QAEvC,KAAK,MAAM,WAAW,SAAU;YAC9B,IAAI;gBACF,MAAM,QAAQ,MAAM,IAAI,CAAC,SAAS,CAAC;gBACnC,MAAM,iBAAiB,MAAM,IAAI,CAAC,eAAe,CAAC;gBAClD,SAAS,IAAI,IAAI;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC,EAAE;YACnD,4BAA4B;YAC9B;QACF;QAEA,2CAA2C;QAC3C,MAAM,eAAe,IAAI,CAAC,kBAAkB,CAAC;QAC7C,OAAO,aAAa,IAAI,CAAC,CAAC,GAAG,IAC3B,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO;IAEvE;IAEA,2CAA2C;IAC3C,eAAc,KAAyB,EAAE,WAAqB,EAAE;QAC9D,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO;QAElC,OAAO,MAAM,MAAM,CAAC,CAAA;YAClB,MAAM,aAAa,GAAG,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC,EAAE,KAAK,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW;YAC3F,OAAO,SAAS,IAAI,CAAC,CAAA,UACnB,WAAW,QAAQ,CAAC,QAAQ,WAAW;QAE3C;IACF;IAEA,yCAAyC;IACzC,cAAa,KAAyB;QACpC,OAAO,MAAM,GAAG,CAAC,CAAA;YACf,IAAI,QAAQ;YAEZ,kDAAkD;YAClD,MAAM,aAAa,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,WAAW,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;YACxF,SAAS,KAAK,GAAG,CAAC,GAAG,MAAM,YAAY,yCAAyC;;YAEhF,sBAAsB;YACtB,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK,CAAC,KAAK,MAAM;YAC/C,IAAI,cAAc,KAAK,cAAc,IAAI,SAAS,GAAG,uBAAuB;;YAE5E,uBAAuB;YACvB,MAAM,eAAe,KAAK,OAAO,CAAC,KAAK,CAAC,KAAK,MAAM;YACnD,IAAI,gBAAgB,OAAO,gBAAgB,MAAM,SAAS,GAAG,sBAAsB;;YAEnF,yDAAyD;YACzD,MAAM,eAAe;gBAAC;gBAAQ;gBAAc;gBAAM;gBAAW;gBAAc;gBAAY;aAAM;YAC7F,MAAM,sBAAsB,KAAK,UAAU,CAAC,IAAI,CAAC,CAAA,MAC/C,aAAa,IAAI,CAAC,CAAA,UAAW,IAAI,WAAW,GAAG,QAAQ,CAAC;YAE1D,IAAI,qBAAqB,SAAS;YAElC,OAAO;gBAAE,GAAG,IAAI;gBAAE;YAAM;QAC1B,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;IACrC;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 453, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/items/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { dbOperations } from '@/lib/supabase'\nimport rssOperations from '@/lib/rss'\n\nexport async function GET() {\n  try {\n    // Get active RSS feeds from database\n    const feeds = await dbOperations.getRSSFeeds()\n    const activeFeeds = feeds.filter(feed => feed.is_active)\n    \n    if (activeFeeds.length === 0) {\n      return NextResponse.json([])\n    }\n    \n    // Get aggregated content from all active feeds\n    const feedUrls = activeFeeds.map(feed => feed.url)\n    const content = await rssOperations.getAggregatedContent(feedUrls)\n    \n    // Score and sort content\n    const scoredContent = rssOperations.scoreContent(content)\n    \n    // Return top 50 items\n    return NextResponse.json(scoredContent.slice(0, 50))\n  } catch (error) {\n    console.error('Error fetching RSS items:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch RSS items' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { feedUrl, limit = 10 } = await request.json()\n    \n    if (!feedUrl) {\n      return NextResponse.json(\n        { error: 'Feed URL is required' },\n        { status: 400 }\n      )\n    }\n    \n    // Parse single feed\n    const items = await rssOperations.parseFeed(feedUrl)\n    const processedItems = await rssOperations.processRSSItems(items.slice(0, limit))\n    const scoredItems = rssOperations.scoreContent(processedItems)\n    \n    return NextResponse.json(scoredItems)\n  } catch (error) {\n    console.error('Error processing RSS feed:', error)\n    return NextResponse.json(\n      { error: 'Failed to process RSS feed' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe;IACpB,IAAI;QACF,qCAAqC;QACrC,MAAM,QAAQ,MAAM,wHAAA,CAAA,eAAY,CAAC,WAAW;QAC5C,MAAM,cAAc,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS;QAEvD,IAAI,YAAY,MAAM,KAAK,GAAG;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,EAAE;QAC7B;QAEA,+CAA+C;QAC/C,MAAM,WAAW,YAAY,GAAG,CAAC,CAAA,OAAQ,KAAK,GAAG;QACjD,MAAM,UAAU,MAAM,mHAAA,CAAA,UAAa,CAAC,oBAAoB,CAAC;QAEzD,yBAAyB;QACzB,MAAM,gBAAgB,mHAAA,CAAA,UAAa,CAAC,YAAY,CAAC;QAEjD,sBAAsB;QACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,cAAc,KAAK,CAAC,GAAG;IAClD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,GAAG,MAAM,QAAQ,IAAI;QAElD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,oBAAoB;QACpB,MAAM,QAAQ,MAAM,mHAAA,CAAA,UAAa,CAAC,SAAS,CAAC;QAC5C,MAAM,iBAAiB,MAAM,mHAAA,CAAA,UAAa,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,GAAG;QAC1E,MAAM,cAAc,mHAAA,CAAA,UAAa,CAAC,YAAY,CAAC;QAE/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA6B,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}