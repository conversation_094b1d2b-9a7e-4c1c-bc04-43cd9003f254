module.exports = {

"[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// used in Attr to signal changes
__turbopack_context__.s({
    "CHANGED": (()=>CHANGED),
    "CLASS_LIST": (()=>CLASS_LIST),
    "CONTENT": (()=>CONTENT),
    "CUSTOM_ELEMENTS": (()=>CUSTOM_ELEMENTS),
    "DATASET": (()=>DATASET),
    "DOCTYPE": (()=>DOCTYPE),
    "DOM_PARSER": (()=>DOM_PARSER),
    "END": (()=>END),
    "EVENT_TARGET": (()=>EVENT_TARGET),
    "GLOBALS": (()=>GL<PERSON>BALS),
    "IMAGE": (()=>IMAGE),
    "MIME": (()=>MIME),
    "MUTATION_OBSERVER": (()=>MUTATION_OBSERVER),
    "NEXT": (()=>NEXT),
    "OWNER_ELEMENT": (()=>OWNER_ELEMENT),
    "PREV": (()=>PREV),
    "PRIVATE": (()=>PRIVATE),
    "SHEET": (()=>SHEET),
    "START": (()=>START),
    "STYLE": (()=>STYLE),
    "UPGRADE": (()=>UPGRADE),
    "VALUE": (()=>VALUE)
});
const CHANGED = Symbol('changed');
const CLASS_LIST = Symbol('classList');
const CUSTOM_ELEMENTS = Symbol('CustomElements');
const CONTENT = Symbol('content');
const DATASET = Symbol('dataset');
const DOCTYPE = Symbol('doctype');
const DOM_PARSER = Symbol('DOMParser');
const END = Symbol('end');
const EVENT_TARGET = Symbol('EventTarget');
const GLOBALS = Symbol('globals');
const IMAGE = Symbol('image');
const MIME = Symbol('mime');
const MUTATION_OBSERVER = Symbol('MutationObserver');
const NEXT = Symbol('next');
const OWNER_ELEMENT = Symbol('ownerElement');
const PREV = Symbol('prev');
const PRIVATE = Symbol('private');
const SHEET = Symbol('sheet');
const START = Symbol('start');
const STYLE = Symbol('style');
const UPGRADE = Symbol('upgrade');
const VALUE = Symbol('value');
}}),
"[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Internal
__turbopack_context__.s({
    "ATTRIBUTE_NODE": (()=>ATTRIBUTE_NODE),
    "BLOCK_ELEMENTS": (()=>BLOCK_ELEMENTS),
    "CDATA_SECTION_NODE": (()=>CDATA_SECTION_NODE),
    "COMMENT_NODE": (()=>COMMENT_NODE),
    "DOCUMENT_FRAGMENT_NODE": (()=>DOCUMENT_FRAGMENT_NODE),
    "DOCUMENT_NODE": (()=>DOCUMENT_NODE),
    "DOCUMENT_POSITION_CONTAINED_BY": (()=>DOCUMENT_POSITION_CONTAINED_BY),
    "DOCUMENT_POSITION_CONTAINS": (()=>DOCUMENT_POSITION_CONTAINS),
    "DOCUMENT_POSITION_DISCONNECTED": (()=>DOCUMENT_POSITION_DISCONNECTED),
    "DOCUMENT_POSITION_FOLLOWING": (()=>DOCUMENT_POSITION_FOLLOWING),
    "DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC": (()=>DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC),
    "DOCUMENT_POSITION_PRECEDING": (()=>DOCUMENT_POSITION_PRECEDING),
    "DOCUMENT_TYPE_NODE": (()=>DOCUMENT_TYPE_NODE),
    "ELEMENT_NODE": (()=>ELEMENT_NODE),
    "NODE_END": (()=>NODE_END),
    "SHOW_ALL": (()=>SHOW_ALL),
    "SHOW_CDATA_SECTION": (()=>SHOW_CDATA_SECTION),
    "SHOW_COMMENT": (()=>SHOW_COMMENT),
    "SHOW_ELEMENT": (()=>SHOW_ELEMENT),
    "SHOW_TEXT": (()=>SHOW_TEXT),
    "SVG_NAMESPACE": (()=>SVG_NAMESPACE),
    "TEXT_NODE": (()=>TEXT_NODE)
});
const NODE_END = -1;
const ELEMENT_NODE = 1;
const ATTRIBUTE_NODE = 2;
const TEXT_NODE = 3;
const CDATA_SECTION_NODE = 4;
const COMMENT_NODE = 8;
const DOCUMENT_NODE = 9;
const DOCUMENT_TYPE_NODE = 10;
const DOCUMENT_FRAGMENT_NODE = 11;
const BLOCK_ELEMENTS = new Set([
    'ARTICLE',
    'ASIDE',
    'BLOCKQUOTE',
    'BODY',
    'BR',
    'BUTTON',
    'CANVAS',
    'CAPTION',
    'COL',
    'COLGROUP',
    'DD',
    'DIV',
    'DL',
    'DT',
    'EMBED',
    'FIELDSET',
    'FIGCAPTION',
    'FIGURE',
    'FOOTER',
    'FORM',
    'H1',
    'H2',
    'H3',
    'H4',
    'H5',
    'H6',
    'LI',
    'UL',
    'OL',
    'P'
]);
const SHOW_ALL = -1;
const SHOW_ELEMENT = 1;
const SHOW_TEXT = 4;
const SHOW_CDATA_SECTION = 8;
const SHOW_COMMENT = 128;
const DOCUMENT_POSITION_DISCONNECTED = 0x01;
const DOCUMENT_POSITION_PRECEDING = 0x02;
const DOCUMENT_POSITION_FOLLOWING = 0x04;
const DOCUMENT_POSITION_CONTAINS = 0x08;
const DOCUMENT_POSITION_CONTAINED_BY = 0x10;
const DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC = 0x20;
const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';
}}),
"[project]/node_modules/linkedom/esm/shared/object.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "assign": (()=>assign),
    "create": (()=>create),
    "defineProperties": (()=>defineProperties),
    "entries": (()=>entries),
    "getOwnPropertyDescriptors": (()=>getOwnPropertyDescriptors),
    "keys": (()=>keys),
    "setPrototypeOf": (()=>setPrototypeOf)
});
const { assign, create, defineProperties, entries, getOwnPropertyDescriptors, keys, setPrototypeOf } = Object;
;
}}),
"[project]/node_modules/linkedom/esm/shared/utils.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "String": (()=>$String),
    "getEnd": (()=>getEnd),
    "htmlToFragment": (()=>htmlToFragment),
    "ignoreCase": (()=>ignoreCase),
    "knownAdjacent": (()=>knownAdjacent),
    "knownBoundaries": (()=>knownBoundaries),
    "knownSegment": (()=>knownSegment),
    "knownSiblings": (()=>knownSiblings),
    "localCase": (()=>localCase),
    "setAdjacent": (()=>setAdjacent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
;
;
const $String = String;
;
const getEnd = (node)=>node.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"] ? node[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]] : node;
const ignoreCase = ({ ownerDocument })=>ownerDocument[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MIME"]].ignoreCase;
const knownAdjacent = (prev, next)=>{
    prev[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]] = next;
    next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]] = prev;
};
const knownBoundaries = (prev, current, next)=>{
    knownAdjacent(prev, current);
    knownAdjacent(getEnd(current), next);
};
const knownSegment = (prev, start, end, next)=>{
    knownAdjacent(prev, start);
    knownAdjacent(getEnd(end), next);
};
const knownSiblings = (prev, current, next)=>{
    knownAdjacent(prev, current);
    knownAdjacent(current, next);
};
const localCase = ({ localName, ownerDocument })=>{
    return ownerDocument[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MIME"]].ignoreCase ? localName.toUpperCase() : localName;
};
const setAdjacent = (prev, next)=>{
    if (prev) prev[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]] = next;
    if (next) next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]] = prev;
};
const htmlToFragment = (ownerDocument, html)=>{
    const fragment = ownerDocument.createDocumentFragment();
    const elem = ownerDocument.createElement('');
    elem.innerHTML = html;
    const { firstChild, lastChild } = elem;
    if (firstChild) {
        knownSegment(fragment, firstChild, lastChild, fragment[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]);
        let child = firstChild;
        do {
            child.parentNode = fragment;
        }while (child !== lastChild && (child = getEnd(child)[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]))
    }
    return fragment;
};
}}),
"[project]/node_modules/linkedom/esm/shared/shadow-roots.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "shadowRoots": (()=>shadowRoots)
});
const shadowRoots = new WeakMap;
}}),
"[project]/node_modules/linkedom/esm/interface/custom-element-registry.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Classes": (()=>Classes),
    "CustomElementRegistry": (()=>CustomElementRegistry),
    "attributeChangedCallback": (()=>attributeChangedCallback),
    "connectedCallback": (()=>connectedCallback),
    "customElements": (()=>customElements),
    "disconnectedCallback": (()=>disconnectedCallback)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/object.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$shadow$2d$roots$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/shadow-roots.js [app-route] (ecmascript)");
;
;
;
;
let reactive = false;
const Classes = new WeakMap;
const customElements = new WeakMap;
const attributeChangedCallback = (element, attributeName, oldValue, newValue)=>{
    if (reactive && customElements.has(element) && element.attributeChangedCallback && element.constructor.observedAttributes.includes(attributeName)) {
        element.attributeChangedCallback(attributeName, oldValue, newValue);
    }
};
const createTrigger = (method, isConnected)=>(element)=>{
        if (customElements.has(element)) {
            const info = customElements.get(element);
            if (info.connected !== isConnected && element.isConnected === isConnected) {
                info.connected = isConnected;
                if (method in element) element[method]();
            }
        }
    };
const triggerConnected = createTrigger('connectedCallback', true);
const connectedCallback = (element)=>{
    if (reactive) {
        triggerConnected(element);
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$shadow$2d$roots$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["shadowRoots"].has(element)) element = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$shadow$2d$roots$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["shadowRoots"].get(element).shadowRoot;
        let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = element;
        while(next !== end){
            if (next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]) triggerConnected(next);
            next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        }
    }
};
const triggerDisconnected = createTrigger('disconnectedCallback', false);
const disconnectedCallback = (element)=>{
    if (reactive) {
        triggerDisconnected(element);
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$shadow$2d$roots$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["shadowRoots"].has(element)) element = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$shadow$2d$roots$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["shadowRoots"].get(element).shadowRoot;
        let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = element;
        while(next !== end){
            if (next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]) triggerDisconnected(next);
            next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        }
    }
};
class CustomElementRegistry {
    /**
   * @param {Document} ownerDocument
   */ constructor(ownerDocument){
        /**
     * @private
     */ this.ownerDocument = ownerDocument;
        /**
     * @private
     */ this.registry = new Map;
        /**
     * @private
     */ this.waiting = new Map;
        /**
     * @private
     */ this.active = false;
    }
    /**
   * @param {string} localName the custom element definition name
   * @param {Function} Class the custom element **Class** definition
   * @param {object?} options the optional object with an `extends` property
   */ define(localName, Class, options = {}) {
        const { ownerDocument, registry, waiting } = this;
        if (registry.has(localName)) throw new Error('unable to redefine ' + localName);
        if (Classes.has(Class)) throw new Error('unable to redefine the same class: ' + Class);
        this.active = reactive = true;
        const { extends: extend } = options;
        Classes.set(Class, {
            ownerDocument,
            options: {
                is: extend ? localName : ''
            },
            localName: extend || localName
        });
        const check = extend ? (element)=>{
            return element.localName === extend && element.getAttribute('is') === localName;
        } : (element)=>element.localName === localName;
        registry.set(localName, {
            Class,
            check
        });
        if (waiting.has(localName)) {
            for (const resolve of waiting.get(localName))resolve(Class);
            waiting.delete(localName);
        }
        ownerDocument.querySelectorAll(extend ? `${extend}[is="${localName}"]` : localName).forEach(this.upgrade, this);
    }
    /**
   * @param {Element} element
   */ upgrade(element) {
        if (customElements.has(element)) return;
        const { ownerDocument, registry } = this;
        const ce = element.getAttribute('is') || element.localName;
        if (registry.has(ce)) {
            const { Class, check } = registry.get(ce);
            if (check(element)) {
                const { attributes, isConnected } = element;
                for (const attr of attributes)element.removeAttributeNode(attr);
                const values = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["entries"])(element);
                for (const [key] of values)delete element[key];
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setPrototypeOf"])(element, Class.prototype);
                ownerDocument[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UPGRADE"]] = {
                    element,
                    values
                };
                new Class(ownerDocument, ce);
                customElements.set(element, {
                    connected: isConnected
                });
                for (const attr of attributes)element.setAttributeNode(attr);
                if (isConnected && element.connectedCallback) element.connectedCallback();
            }
        }
    }
    /**
   * @param {string} localName the custom element definition name
   */ whenDefined(localName) {
        const { registry, waiting } = this;
        return new Promise((resolve)=>{
            if (registry.has(localName)) resolve(registry.get(localName).Class);
            else {
                if (!waiting.has(localName)) waiting.set(localName, []);
                waiting.get(localName).push(resolve);
            }
        });
    }
    /**
   * @param {string} localName the custom element definition name
   * @returns {Function?} the custom element **Class**, if any
   */ get(localName) {
        const info = this.registry.get(localName);
        return info && info.Class;
    }
    /**
   * @param {Function} Class **Class** of custom element
   * @returns {string?} found tag name or null
   */ getName(Class) {
        if (Classes.has(Class)) {
            const { localName } = Classes.get(Class);
            return localName;
        }
        return null;
    }
}
}}),
"[project]/node_modules/linkedom/esm/shared/parse-from-string.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isNotParsing": (()=>isNotParsing),
    "parseFromString": (()=>parseFromString)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$htmlparser2$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/htmlparser2/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$htmlparser2$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/htmlparser2/dist/esm/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/object.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/utils.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/custom-element-registry.js [app-route] (ecmascript)");
;
;
;
;
;
;
const { Parser } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$htmlparser2$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__;
// import {Mime} from './mime.js';
// const VOID_SOURCE = Mime['text/html'].voidElements.source.slice(4, -2);
// const VOID_ELEMENTS = new RegExp(`<(${VOID_SOURCE})([^>]*?)>`, 'gi');
// const VOID_SANITIZER = (_, $1, $2) => `<${$1}${$2}${/\/$/.test($2) ? '' : ' /'}>`;
// const voidSanitizer = html => html.replace(VOID_ELEMENTS, VOID_SANITIZER);
let notParsing = true;
const append = (self, node, active)=>{
    const end = self[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]];
    node.parentNode = self;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["knownBoundaries"])(end[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]], node, end);
    if (active && node.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]) (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["connectedCallback"])(node);
    return node;
};
const attribute = (element, end, attribute, value, active)=>{
    attribute[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]] = value;
    attribute.ownerElement = element;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["knownSiblings"])(end[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]], attribute, end);
    if (attribute.name === 'class') element.className = value;
    if (active) (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["attributeChangedCallback"])(element, attribute.name, null, value);
};
const isNotParsing = ()=>notParsing;
const parseFromString = (document, isHTML, markupLanguage)=>{
    const { active, registry } = document[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CUSTOM_ELEMENTS"]];
    let node = document;
    let ownerSVGElement = null;
    let parsingCData = false;
    notParsing = false;
    const content = new Parser({
        // <!DOCTYPE ...>
        onprocessinginstruction (name, data) {
            if (name.toLowerCase() === '!doctype') document.doctype = data.slice(name.length).trim();
        },
        // <tagName>
        onopentag (name, attributes) {
            let create = true;
            if (isHTML) {
                if (ownerSVGElement) {
                    node = append(node, document.createElementNS(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SVG_NAMESPACE"], name), active);
                    node.ownerSVGElement = ownerSVGElement;
                    create = false;
                } else if (name === 'svg' || name === 'SVG') {
                    ownerSVGElement = document.createElementNS(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SVG_NAMESPACE"], name);
                    node = append(node, ownerSVGElement, active);
                    create = false;
                } else if (active) {
                    const ce = name.includes('-') ? name : attributes.is || '';
                    if (ce && registry.has(ce)) {
                        const { Class } = registry.get(ce);
                        node = append(node, new Class, active);
                        delete attributes.is;
                        create = false;
                    }
                }
            }
            if (create) node = append(node, document.createElement(name), false);
            let end = node[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]];
            for (const name of (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["keys"])(attributes))attribute(node, end, document.createAttribute(name), attributes[name], active);
        },
        // #text, #comment
        oncomment (data) {
            append(node, document.createComment(data), active);
        },
        ontext (text) {
            if (parsingCData) {
                append(node, document.createCDATASection(text), active);
            } else {
                append(node, document.createTextNode(text), active);
            }
        },
        // #cdata
        oncdatastart () {
            parsingCData = true;
        },
        oncdataend () {
            parsingCData = false;
        },
        // </tagName>
        onclosetag () {
            if (isHTML && node === ownerSVGElement) ownerSVGElement = null;
            node = node.parentNode;
        }
    }, {
        lowerCaseAttributeNames: false,
        decodeEntities: true,
        xmlMode: !isHTML
    });
    content.write(markupLanguage);
    content.end();
    notParsing = true;
    return document;
};
}}),
"[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "htmlClasses": (()=>htmlClasses),
    "registerHTMLClass": (()=>registerHTMLClass)
});
const htmlClasses = new Map;
const registerHTMLClass = (names, Class)=>{
    for (const name of [].concat(names)){
        htmlClasses.set(name, Class);
        htmlClasses.set(name.toUpperCase(), Class);
    }
};
}}),
"[project]/node_modules/linkedom/commonjs/perf_hooks.cjs [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* c8 ignore start */ try {
    const { performance } = __turbopack_context__.r("[externals]/perf_hooks [external] (perf_hooks, cjs)");
    exports.performance = performance;
} catch (fallback) {
    exports.performance = {
        now () {
            return +new Date;
        }
    };
} /* c8 ignore stop */ 
}}),
"[project]/node_modules/linkedom/esm/shared/jsdon.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "attrAsJSON": (()=>attrAsJSON),
    "characterDataAsJSON": (()=>characterDataAsJSON),
    "documentTypeAsJSON": (()=>documentTypeAsJSON),
    "elementAsJSON": (()=>elementAsJSON),
    "nonElementAsJSON": (()=>nonElementAsJSON)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/utils.js [app-route] (ecmascript)");
;
;
;
const loopSegment = ({ [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end }, json)=>{
    while(next !== end){
        switch(next.nodeType){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ATTRIBUTE_NODE"]:
                attrAsJSON(next, json);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TEXT_NODE"]:
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["COMMENT_NODE"]:
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CDATA_SECTION_NODE"]:
                characterDataAsJSON(next, json);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]:
                elementAsJSON(next, json);
                next = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnd"])(next);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_TYPE_NODE"]:
                documentTypeAsJSON(next, json);
                break;
        }
        next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
    }
    const last = json.length - 1;
    const value = json[last];
    if (typeof value === 'number' && value < 0) json[last] += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NODE_END"];
    else json.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NODE_END"]);
};
const attrAsJSON = (attr, json)=>{
    json.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ATTRIBUTE_NODE"], attr.name);
    const value = attr[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]].trim();
    if (value) json.push(value);
};
const characterDataAsJSON = (node, json)=>{
    const value = node[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]];
    if (value.trim()) json.push(node.nodeType, value);
};
const nonElementAsJSON = (node, json)=>{
    json.push(node.nodeType);
    loopSegment(node, json);
};
const documentTypeAsJSON = ({ name, publicId, systemId }, json)=>{
    json.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_TYPE_NODE"], name);
    if (publicId) json.push(publicId);
    if (systemId) json.push(systemId);
};
const elementAsJSON = (element, json)=>{
    json.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"], element.localName);
    loopSegment(element, json);
};
}}),
"[project]/node_modules/linkedom/esm/interface/mutation-observer.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MutationObserverClass": (()=>MutationObserverClass),
    "attributeChangedCallback": (()=>attributeChangedCallback),
    "moCallback": (()=>moCallback)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
;
const createRecord = (type, target, element, addedNodes, removedNodes, attributeName, oldValue)=>({
        type,
        target,
        addedNodes,
        removedNodes,
        attributeName,
        oldValue,
        previousSibling: element?.previousSibling || null,
        nextSibling: element?.nextSibling || null
    });
const queueAttribute = (observer, target, attributeName, attributeFilter, attributeOldValue, oldValue)=>{
    if (!attributeFilter || attributeFilter.includes(attributeName)) {
        const { callback, records, scheduled } = observer;
        records.push(createRecord('attributes', target, null, [], [], attributeName, attributeOldValue ? oldValue : void 0));
        if (!scheduled) {
            observer.scheduled = true;
            Promise.resolve().then(()=>{
                observer.scheduled = false;
                callback(records.splice(0), observer);
            });
        }
    }
};
const attributeChangedCallback = (element, attributeName, oldValue)=>{
    const { ownerDocument } = element;
    const { active, observers } = ownerDocument[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MUTATION_OBSERVER"]];
    if (active) {
        for (const observer of observers){
            for (const [target, { childList, subtree, attributes, attributeFilter, attributeOldValue }] of observer.nodes){
                if (childList) {
                    if (subtree && (target === ownerDocument || target.contains(element)) || !subtree && target.children.includes(element)) {
                        queueAttribute(observer, element, attributeName, attributeFilter, attributeOldValue, oldValue);
                        break;
                    }
                } else if (attributes && target === element) {
                    queueAttribute(observer, element, attributeName, attributeFilter, attributeOldValue, oldValue);
                    break;
                }
            }
        }
    }
};
const moCallback = (element, parentNode)=>{
    const { ownerDocument } = element;
    const { active, observers } = ownerDocument[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MUTATION_OBSERVER"]];
    if (active) {
        for (const observer of observers){
            for (const [target, { subtree, childList, characterData }] of observer.nodes){
                if (childList) {
                    if (parentNode && (target === parentNode || subtree && target.contains(parentNode)) || !parentNode && (subtree && (target === ownerDocument || /* c8 ignore next */ target.contains(element)) || !subtree && target[characterData ? 'childNodes' : 'children'].includes(element))) {
                        const { callback, records, scheduled } = observer;
                        records.push(createRecord('childList', target, element, parentNode ? [] : [
                            element
                        ], parentNode ? [
                            element
                        ] : []));
                        if (!scheduled) {
                            observer.scheduled = true;
                            Promise.resolve().then(()=>{
                                observer.scheduled = false;
                                callback(records.splice(0), observer);
                            });
                        }
                        break;
                    }
                }
            }
        }
    }
};
class MutationObserverClass {
    constructor(ownerDocument){
        const observers = new Set;
        this.observers = observers;
        this.active = false;
        /**
     * @implements globalThis.MutationObserver
     */ this.class = class MutationObserver {
            constructor(callback){
                /**
         * @private
         */ this.callback = callback;
                /**
         * @private
         */ this.nodes = new Map;
                /**
         * @private
         */ this.records = [];
                /**
         * @private
         */ this.scheduled = false;
            }
            disconnect() {
                this.records.splice(0);
                this.nodes.clear();
                observers.delete(this);
                ownerDocument[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MUTATION_OBSERVER"]].active = !!observers.size;
            }
            /**
       * @param {Element} target
       * @param {MutationObserverInit} options
       */ observe(target, options = {
                subtree: false,
                childList: false,
                attributes: false,
                attributeFilter: null,
                attributeOldValue: false,
                characterData: false
            }) {
                if ('attributeOldValue' in options || 'attributeFilter' in options) options.attributes = true;
                // if ('characterDataOldValue' in options)
                //   options.characterData = true;
                options.childList = !!options.childList;
                options.subtree = !!options.subtree;
                this.nodes.set(target, options);
                observers.add(this);
                ownerDocument[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MUTATION_OBSERVER"]].active = true;
            }
            /**
       * @returns {MutationRecord[]}
       */ takeRecords() {
                return this.records.splice(0);
            }
        };
    }
}
}}),
"[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "booleanAttribute": (()=>booleanAttribute),
    "emptyAttributes": (()=>emptyAttributes),
    "numericAttribute": (()=>numericAttribute),
    "removeAttribute": (()=>removeAttribute),
    "setAttribute": (()=>setAttribute),
    "stringAttribute": (()=>stringAttribute)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/utils.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/custom-element-registry.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$mutation$2d$observer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/mutation-observer.js [app-route] (ecmascript)");
;
;
;
;
const emptyAttributes = new Set([
    'allowfullscreen',
    'allowpaymentrequest',
    'async',
    'autofocus',
    'autoplay',
    'checked',
    'class',
    'contenteditable',
    'controls',
    'default',
    'defer',
    'disabled',
    'draggable',
    'formnovalidate',
    'hidden',
    'id',
    'ismap',
    'itemscope',
    'loop',
    'multiple',
    'muted',
    'nomodule',
    'novalidate',
    'open',
    'playsinline',
    'readonly',
    'required',
    'reversed',
    'selected',
    'style',
    'truespeed'
]);
const setAttribute = (element, attribute)=>{
    const { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]]: value, name } = attribute;
    attribute.ownerElement = element;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["knownSiblings"])(element, attribute, element[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]);
    if (name === 'class') element.className = value;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$mutation$2d$observer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["attributeChangedCallback"])(element, name, null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["attributeChangedCallback"])(element, name, null, value);
};
const removeAttribute = (element, attribute)=>{
    const { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]]: value, name } = attribute;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["knownAdjacent"])(attribute[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]], attribute[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]);
    attribute.ownerElement = attribute[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]] = attribute[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]] = null;
    if (name === 'class') element[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLASS_LIST"]] = null;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$mutation$2d$observer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["attributeChangedCallback"])(element, name, value);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["attributeChangedCallback"])(element, name, value, null);
};
const booleanAttribute = {
    get (element, name) {
        return element.hasAttribute(name);
    },
    set (element, name, value) {
        if (value) element.setAttribute(name, '');
        else element.removeAttribute(name);
    }
};
const numericAttribute = {
    get (element, name) {
        return parseFloat(element.getAttribute(name) || 0);
    },
    set (element, name, value) {
        element.setAttribute(name, value);
    }
};
const stringAttribute = {
    get (element, name) {
        return element.getAttribute(name) || '';
    },
    set (element, name, value) {
        element.setAttribute(name, value);
    }
}; /* oddly enough, this apparently is not a thing
export const nullableAttribute = {
  get(element, name) {
    return element.getAttribute(name);
  },
  set(element, name, value) {
    if (value === null)
      element.removeAttribute(name);
    else
      element.setAttribute(name, value);
  }
};
*/ 
}}),
"[project]/node_modules/linkedom/esm/interface/event-target.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// https://dom.spec.whatwg.org/#interface-eventtarget
__turbopack_context__.s({
    "EventTarget": (()=>DOMEventTarget)
});
const wm = new WeakMap();
function dispatch(event, listener) {
    if (typeof listener === 'function') listener.call(event.target, event);
    else listener.handleEvent(event);
    return event._stopImmediatePropagationFlag;
}
function invokeListeners({ currentTarget, target }) {
    const map = wm.get(currentTarget);
    if (map && map.has(this.type)) {
        const listeners = map.get(this.type);
        if (currentTarget === target) {
            this.eventPhase = this.AT_TARGET;
        } else {
            this.eventPhase = this.BUBBLING_PHASE;
        }
        this.currentTarget = currentTarget;
        this.target = target;
        for (const [listener, options] of listeners){
            if (options && options.once) listeners.delete(listener);
            if (dispatch(this, listener)) break;
        }
        delete this.currentTarget;
        delete this.target;
        return this.cancelBubble;
    }
}
/**
 * @implements globalThis.EventTarget
 */ class DOMEventTarget {
    constructor(){
        wm.set(this, new Map);
    }
    /**
   * @protected
   */ _getParent() {
        return null;
    }
    addEventListener(type, listener, options) {
        const map = wm.get(this);
        if (!map.has(type)) map.set(type, new Map);
        map.get(type).set(listener, options);
    }
    removeEventListener(type, listener) {
        const map = wm.get(this);
        if (map.has(type)) {
            const listeners = map.get(type);
            if (listeners.delete(listener) && !listeners.size) map.delete(type);
        }
    }
    dispatchEvent(event) {
        let node = this;
        event.eventPhase = event.CAPTURING_PHASE;
        // intentionally simplified, specs imply way more code: https://dom.spec.whatwg.org/#event-path
        while(node){
            if (node.dispatchEvent) event._path.push({
                currentTarget: node,
                target: this
            });
            node = event.bubbles && node._getParent && node._getParent();
        }
        event._path.some(invokeListeners, event);
        event._path = [];
        event.eventPhase = event.NONE;
        return !event.defaultPrevented;
    }
}
;
}}),
"[project]/node_modules/linkedom/esm/interface/node-list.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// https://dom.spec.whatwg.org/#interface-nodelist
/**
 * @implements globalThis.NodeList
 */ __turbopack_context__.s({
    "NodeList": (()=>NodeList)
});
class NodeList extends Array {
    item(i) {
        return i < this.length ? this[i] : null;
    }
}
}}),
"[project]/node_modules/linkedom/esm/interface/node.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// https://dom.spec.whatwg.org/#node
__turbopack_context__.s({
    "Node": (()=>Node)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2d$target$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/event-target.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/node-list.js [app-route] (ecmascript)");
;
;
;
;
const getParentNodeCount = ({ parentNode })=>{
    let count = 0;
    while(parentNode){
        count++;
        parentNode = parentNode.parentNode;
    }
    return count;
};
class Node extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2d$target$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventTarget"] {
    static get ELEMENT_NODE() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"];
    }
    static get ATTRIBUTE_NODE() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ATTRIBUTE_NODE"];
    }
    static get TEXT_NODE() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TEXT_NODE"];
    }
    static get CDATA_SECTION_NODE() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CDATA_SECTION_NODE"];
    }
    static get COMMENT_NODE() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["COMMENT_NODE"];
    }
    static get DOCUMENT_NODE() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_NODE"];
    }
    static get DOCUMENT_FRAGMENT_NODE() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_FRAGMENT_NODE"];
    }
    static get DOCUMENT_TYPE_NODE() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_TYPE_NODE"];
    }
    constructor(ownerDocument, localName, nodeType){
        super();
        this.ownerDocument = ownerDocument;
        this.localName = localName;
        this.nodeType = nodeType;
        this.parentNode = null;
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]] = null;
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]] = null;
    }
    get ELEMENT_NODE() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"];
    }
    get ATTRIBUTE_NODE() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ATTRIBUTE_NODE"];
    }
    get TEXT_NODE() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TEXT_NODE"];
    }
    get CDATA_SECTION_NODE() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CDATA_SECTION_NODE"];
    }
    get COMMENT_NODE() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["COMMENT_NODE"];
    }
    get DOCUMENT_NODE() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_NODE"];
    }
    get DOCUMENT_FRAGMENT_NODE() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_FRAGMENT_NODE"];
    }
    get DOCUMENT_TYPE_NODE() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_TYPE_NODE"];
    }
    get baseURI() {
        const ownerDocument = this.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_NODE"] ? this : this.ownerDocument;
        if (ownerDocument) {
            const base = ownerDocument.querySelector('base');
            if (base) return base.getAttribute('href');
            const { location } = ownerDocument.defaultView;
            if (location) return location.href;
        }
        return null;
    }
    /* c8 ignore start */ // mixin: node
    get isConnected() {
        return false;
    }
    get nodeName() {
        return this.localName;
    }
    get parentElement() {
        return null;
    }
    get previousSibling() {
        return null;
    }
    get previousElementSibling() {
        return null;
    }
    get nextSibling() {
        return null;
    }
    get nextElementSibling() {
        return null;
    }
    get childNodes() {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NodeList"];
    }
    get firstChild() {
        return null;
    }
    get lastChild() {
        return null;
    }
    // default values
    get nodeValue() {
        return null;
    }
    set nodeValue(value) {}
    get textContent() {
        return null;
    }
    set textContent(value) {}
    normalize() {}
    cloneNode() {
        return null;
    }
    contains() {
        return false;
    }
    /**
   * Inserts a node before a reference node as a child of this parent node.
   * @param {Node} newNode The node to be inserted.
   * @param {Node} referenceNode The node before which newNode is inserted. If this is null, then newNode is inserted at the end of node's child nodes.
   * @returns The added child
   */ // eslint-disable-next-line no-unused-vars
    insertBefore(newNode, referenceNode) {
        return newNode;
    }
    /**
   * Adds a node to the end of the list of children of this node.
   * @param {Node} child The node to append to the given parent node.
   * @returns The appended child.
   */ appendChild(child) {
        return child;
    }
    /**
   * Replaces a child node within this node
   * @param {Node} newChild The new node to replace oldChild.
   * @param {Node} oldChild The child to be replaced.
   * @returns The replaced Node. This is the same node as oldChild.
   */ replaceChild(newChild, oldChild) {
        return oldChild;
    }
    /**
   * Removes a child node from the DOM.
   * @param {Node} child A Node that is the child node to be removed from the DOM.
   * @returns The removed node.
   */ removeChild(child) {
        return child;
    }
    toString() {
        return '';
    }
    /* c8 ignore stop */ hasChildNodes() {
        return !!this.lastChild;
    }
    isSameNode(node) {
        return this === node;
    }
    // TODO: attributes?
    compareDocumentPosition(target) {
        let result = 0;
        if (this !== target) {
            let self = getParentNodeCount(this);
            let other = getParentNodeCount(target);
            if (self < other) {
                result += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_POSITION_FOLLOWING"];
                if (this.contains(target)) result += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_POSITION_CONTAINED_BY"];
            } else if (other < self) {
                result += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_POSITION_PRECEDING"];
                if (target.contains(this)) result += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_POSITION_CONTAINS"];
            } else if (self && other) {
                const { childNodes } = this.parentNode;
                if (childNodes.indexOf(this) < childNodes.indexOf(target)) result += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_POSITION_FOLLOWING"];
                else result += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_POSITION_PRECEDING"];
            }
            if (!self || !other) {
                result += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC"];
                result += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_POSITION_DISCONNECTED"];
            }
        }
        return result;
    }
    isEqualNode(node) {
        if (this === node) return true;
        if (this.nodeType === node.nodeType) {
            switch(this.nodeType){
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_NODE"]:
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_FRAGMENT_NODE"]:
                    {
                        const aNodes = this.childNodes;
                        const bNodes = node.childNodes;
                        return aNodes.length === bNodes.length && aNodes.every((node, i)=>node.isEqualNode(bNodes[i]));
                    }
            }
            return this.toString() === node.toString();
        }
        return false;
    }
    /**
   * @protected
   */ _getParent() {
        return this.parentNode;
    }
    /**
   * Calling it on an element inside a standard web page will return an HTMLDocument object representing the entire page (or <iframe>).
   * Calling it on an element inside a shadow DOM will return the associated ShadowRoot.
   * @return {ShadowRoot | HTMLDocument}
   */ getRootNode() {
        let root = this;
        while(root.parentNode)root = root.parentNode;
        return root;
    }
}
}}),
"[project]/node_modules/linkedom/esm/shared/text-escaper.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "escape": (()=>escape)
});
const { replace } = '';
// escape
const ca = /[<>&\xA0]/g;
const esca = {
    '\xA0': '&#160;',
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;'
};
const pe = (m)=>esca[m];
const escape = (es)=>replace.call(es, ca, pe);
}}),
"[project]/node_modules/linkedom/esm/interface/attr.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Attr": (()=>Attr)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/utils.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$jsdon$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/jsdon.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$mutation$2d$observer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/mutation-observer.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/custom-element-registry.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/node.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$text$2d$escaper$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/text-escaper.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
const QUOTE = /"/g;
class Attr extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Node"] {
    constructor(ownerDocument, name, value = ''){
        super(ownerDocument, name, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ATTRIBUTE_NODE"]);
        this.ownerElement = null;
        this.name = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["String"])(name);
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["String"])(value);
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CHANGED"]] = false;
    }
    get value() {
        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]];
    }
    set value(newValue) {
        const { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]]: oldValue, name, ownerElement } = this;
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["String"])(newValue);
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CHANGED"]] = true;
        if (ownerElement) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$mutation$2d$observer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["attributeChangedCallback"])(ownerElement, name, oldValue);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["attributeChangedCallback"])(ownerElement, name, oldValue, this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]]);
        }
    }
    cloneNode() {
        const { ownerDocument, name, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]]: value } = this;
        return new Attr(ownerDocument, name, value);
    }
    toString() {
        const { name, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]]: value } = this;
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["emptyAttributes"].has(name) && !value) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ignoreCase"])(this) ? name : `${name}=""`;
        }
        const escapedValue = ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ignoreCase"])(this) ? value : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$text$2d$escaper$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["escape"])(value)).replace(QUOTE, '&quot;');
        return `${name}="${escapedValue}"`;
    }
    toJSON() {
        const json = [];
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$jsdon$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["attrAsJSON"])(this, json);
        return json;
    }
}
}}),
"[project]/node_modules/linkedom/esm/shared/node.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isConnected": (()=>isConnected),
    "nextSibling": (()=>nextSibling),
    "parentElement": (()=>parentElement),
    "previousSibling": (()=>previousSibling)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/utils.js [app-route] (ecmascript)");
;
;
;
const isConnected = ({ ownerDocument, parentNode })=>{
    while(parentNode){
        if (parentNode === ownerDocument) return true;
        parentNode = parentNode.parentNode || parentNode.host;
    }
    return false;
};
const parentElement = ({ parentNode })=>{
    if (parentNode) {
        switch(parentNode.nodeType){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_NODE"]:
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_FRAGMENT_NODE"]:
                return null;
        }
    }
    return parentNode;
};
const previousSibling = ({ [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]]: prev })=>{
    switch(prev ? prev.nodeType : 0){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NODE_END"]:
            return prev[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["START"]];
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TEXT_NODE"]:
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["COMMENT_NODE"]:
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CDATA_SECTION_NODE"]:
            return prev;
    }
    return null;
};
const nextSibling = (node)=>{
    const next = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnd"])(node)[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
    return next && (next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NODE_END"] ? null : next);
};
}}),
"[project]/node_modules/linkedom/esm/mixin/non-document-type-child-node.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// https://dom.spec.whatwg.org/#nondocumenttypechildnode
// CharacterData, Element
__turbopack_context__.s({
    "nextElementSibling": (()=>nextElementSibling),
    "previousElementSibling": (()=>previousElementSibling)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/node.js [app-route] (ecmascript)");
;
;
const nextElementSibling = (node)=>{
    let next = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["nextSibling"])(node);
    while(next && next.nodeType !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"])next = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["nextSibling"])(next);
    return next;
};
const previousElementSibling = (node)=>{
    let prev = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["previousSibling"])(node);
    while(prev && prev.nodeType !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"])prev = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["previousSibling"])(prev);
    return prev;
};
}}),
"[project]/node_modules/linkedom/esm/mixin/child-node.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// https://dom.spec.whatwg.org/#childnode
// CharacterData, DocumentType, Element
__turbopack_context__.s({
    "after": (()=>after),
    "before": (()=>before),
    "remove": (()=>remove),
    "replaceWith": (()=>replaceWith)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/utils.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$mutation$2d$observer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/mutation-observer.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/custom-element-registry.js [app-route] (ecmascript)");
;
;
;
;
;
const asFragment = (ownerDocument, nodes)=>{
    const fragment = ownerDocument.createDocumentFragment();
    fragment.append(...nodes);
    return fragment;
};
const before = (node, nodes)=>{
    const { ownerDocument, parentNode } = node;
    if (parentNode) parentNode.insertBefore(asFragment(ownerDocument, nodes), node);
};
const after = (node, nodes)=>{
    const { ownerDocument, parentNode } = node;
    if (parentNode) parentNode.insertBefore(asFragment(ownerDocument, nodes), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnd"])(node)[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]);
};
const replaceWith = (node, nodes)=>{
    const { ownerDocument, parentNode } = node;
    if (parentNode) {
        if (nodes.includes(node)) replaceWith(node, [
            node = node.cloneNode()
        ]);
        parentNode.insertBefore(asFragment(ownerDocument, nodes), node);
        node.remove();
    }
};
const remove = (prev, current, next)=>{
    const { parentNode, nodeType } = current;
    if (prev || next) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setAdjacent"])(prev, next);
        current[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]] = null;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnd"])(current)[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]] = null;
    }
    if (parentNode) {
        current.parentNode = null;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$mutation$2d$observer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["moCallback"])(current, parentNode);
        if (nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]) (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["disconnectedCallback"])(current);
    }
};
}}),
"[project]/node_modules/linkedom/esm/interface/character-data.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// https://dom.spec.whatwg.org/#interface-characterdata
__turbopack_context__.s({
    "CharacterData": (()=>CharacterData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/utils.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/node.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$jsdon$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/jsdon.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$non$2d$document$2d$type$2d$child$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/mixin/non-document-type-child-node.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$child$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/mixin/child-node.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/node.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$mutation$2d$observer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/mutation-observer.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
class CharacterData extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Node"] {
    constructor(ownerDocument, localName, nodeType, data){
        super(ownerDocument, localName, nodeType);
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["String"])(data);
    }
    // <Mixins>
    get isConnected() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isConnected"])(this);
    }
    get parentElement() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parentElement"])(this);
    }
    get previousSibling() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["previousSibling"])(this);
    }
    get nextSibling() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["nextSibling"])(this);
    }
    get previousElementSibling() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$non$2d$document$2d$type$2d$child$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["previousElementSibling"])(this);
    }
    get nextElementSibling() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$non$2d$document$2d$type$2d$child$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["nextElementSibling"])(this);
    }
    before(...nodes) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$child$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["before"])(this, nodes);
    }
    after(...nodes) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$child$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["after"])(this, nodes);
    }
    replaceWith(...nodes) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$child$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["replaceWith"])(this, nodes);
    }
    remove() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$child$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["remove"])(this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]], this, this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]);
    }
    // </Mixins>
    // CharacterData only
    /* c8 ignore start */ get data() {
        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]];
    }
    set data(value) {
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["String"])(value);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$mutation$2d$observer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["moCallback"])(this, this.parentNode);
    }
    get nodeValue() {
        return this.data;
    }
    set nodeValue(value) {
        this.data = value;
    }
    get textContent() {
        return this.data;
    }
    set textContent(value) {
        this.data = value;
    }
    get length() {
        return this.data.length;
    }
    substringData(offset, count) {
        return this.data.substr(offset, count);
    }
    appendData(data) {
        this.data += data;
    }
    insertData(offset, data) {
        const { data: t } = this;
        this.data = t.slice(0, offset) + data + t.slice(offset);
    }
    deleteData(offset, count) {
        const { data: t } = this;
        this.data = t.slice(0, offset) + t.slice(offset + count);
    }
    replaceData(offset, count, data) {
        const { data: t } = this;
        this.data = t.slice(0, offset) + data + t.slice(offset + count);
    }
    /* c8 ignore stop */ toJSON() {
        const json = [];
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$jsdon$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["characterDataAsJSON"])(this, json);
        return json;
    }
}
}}),
"[project]/node_modules/linkedom/esm/interface/cdata-section.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CDATASection": (()=>CDATASection)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$character$2d$data$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/character-data.js [app-route] (ecmascript)");
;
;
;
class CDATASection extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$character$2d$data$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CharacterData"] {
    constructor(ownerDocument, data = ''){
        super(ownerDocument, '#cdatasection', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CDATA_SECTION_NODE"], data);
    }
    cloneNode() {
        const { ownerDocument, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]]: data } = this;
        return new CDATASection(ownerDocument, data);
    }
    toString() {
        return `<![CDATA[${this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]]}]]>`;
    }
}
}}),
"[project]/node_modules/linkedom/esm/interface/comment.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Comment": (()=>Comment)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$character$2d$data$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/character-data.js [app-route] (ecmascript)");
;
;
;
class Comment extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$character$2d$data$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CharacterData"] {
    constructor(ownerDocument, data = ''){
        super(ownerDocument, '#comment', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["COMMENT_NODE"], data);
    }
    cloneNode() {
        const { ownerDocument, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]]: data } = this;
        return new Comment(ownerDocument, data);
    }
    toString() {
        return `<!--${this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]]}-->`;
    }
}
}}),
"[project]/node_modules/linkedom/esm/shared/matches.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "matches": (()=>matches),
    "prepareMatch": (()=>prepareMatch)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$css$2d$select$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/css-select/lib/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$css$2d$select$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/css-select/lib/esm/index.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/utils.js [app-route] (ecmascript)");
;
;
;
const { isArray } = Array;
/* c8 ignore start */ const isTag = ({ nodeType })=>nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"];
const existsOne = (test, elements)=>elements.some((element)=>isTag(element) && (test(element) || existsOne(test, getChildren(element))));
const getAttributeValue = (element, name)=>name === 'class' ? element.classList.value : element.getAttribute(name);
const getChildren = ({ childNodes })=>childNodes;
const getName = (element)=>{
    const { localName } = element;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ignoreCase"])(element) ? localName.toLowerCase() : localName;
};
const getParent = ({ parentNode })=>parentNode;
const getSiblings = (element)=>{
    const { parentNode } = element;
    return parentNode ? getChildren(parentNode) : element;
};
const getText = (node)=>{
    if (isArray(node)) return node.map(getText).join('');
    if (isTag(node)) return getText(getChildren(node));
    if (node.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TEXT_NODE"]) return node.data;
    return '';
};
const hasAttrib = (element, name)=>element.hasAttribute(name);
const removeSubsets = (nodes)=>{
    let { length } = nodes;
    while(length--){
        const node = nodes[length];
        if (length && -1 < nodes.lastIndexOf(node, length - 1)) {
            nodes.splice(length, 1);
            continue;
        }
        for(let { parentNode } = node; parentNode; parentNode = parentNode.parentNode){
            if (nodes.includes(parentNode)) {
                nodes.splice(length, 1);
                break;
            }
        }
    }
    return nodes;
};
const findAll = (test, nodes)=>{
    const matches = [];
    for (const node of nodes){
        if (isTag(node)) {
            if (test(node)) matches.push(node);
            matches.push(...findAll(test, getChildren(node)));
        }
    }
    return matches;
};
const findOne = (test, nodes)=>{
    for (let node of nodes)if (test(node) || (node = findOne(test, getChildren(node)))) return node;
    return null;
};
/* c8 ignore stop */ const adapter = {
    isTag,
    existsOne,
    getAttributeValue,
    getChildren,
    getName,
    getParent,
    getSiblings,
    getText,
    hasAttrib,
    removeSubsets,
    findAll,
    findOne
};
const prepareMatch = (element, selectors)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$css$2d$select$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["compile"])(selectors, {
        context: selectors.includes(':scope') ? element : void 0,
        xmlMode: !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ignoreCase"])(element),
        adapter
    });
const matches = (element, selectors)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$css$2d$select$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"])(element, selectors, {
        strict: true,
        context: selectors.includes(':scope') ? element : void 0,
        xmlMode: !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ignoreCase"])(element),
        adapter
    });
}}),
"[project]/node_modules/linkedom/esm/interface/text.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Text": (()=>Text)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$text$2d$escaper$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/text-escaper.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$character$2d$data$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/character-data.js [app-route] (ecmascript)");
;
;
;
;
class Text extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$character$2d$data$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CharacterData"] {
    constructor(ownerDocument, data = ''){
        super(ownerDocument, '#text', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TEXT_NODE"], data);
    }
    get wholeText() {
        const text = [];
        let { previousSibling, nextSibling } = this;
        while(previousSibling){
            if (previousSibling.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TEXT_NODE"]) text.unshift(previousSibling[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]]);
            else break;
            previousSibling = previousSibling.previousSibling;
        }
        text.push(this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]]);
        while(nextSibling){
            if (nextSibling.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TEXT_NODE"]) text.push(nextSibling[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]]);
            else break;
            nextSibling = nextSibling.nextSibling;
        }
        return text.join('');
    }
    cloneNode() {
        const { ownerDocument, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]]: data } = this;
        return new Text(ownerDocument, data);
    }
    toString() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$text$2d$escaper$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["escape"])(this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]]);
    }
}
}}),
"[project]/node_modules/linkedom/esm/mixin/parent-node.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// https://dom.spec.whatwg.org/#interface-parentnode
// Document, DocumentFragment, Element
__turbopack_context__.s({
    "ParentNode": (()=>ParentNode)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$matches$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/matches.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/node.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/utils.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/node.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/text.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/node-list.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$mutation$2d$observer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/mutation-observer.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/custom-element-registry.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$non$2d$document$2d$type$2d$child$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/mixin/non-document-type-child-node.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
const isNode = (node)=>node instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Node"];
const insert = (parentNode, child, nodes)=>{
    const { ownerDocument } = parentNode;
    for (const node of nodes)parentNode.insertBefore(isNode(node) ? node : new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Text"](ownerDocument, node), child);
};
class ParentNode extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Node"] {
    constructor(ownerDocument, localName, nodeType){
        super(ownerDocument, localName, nodeType);
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRIVATE"]] = null;
        /** @type {NodeStruct} */ this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]] = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]] = {
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: null,
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]]: this,
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["START"]]: this,
            nodeType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NODE_END"],
            ownerDocument: this.ownerDocument,
            parentNode: null
        };
    }
    get childNodes() {
        const childNodes = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NodeList"];
        let { firstChild } = this;
        while(firstChild){
            childNodes.push(firstChild);
            firstChild = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["nextSibling"])(firstChild);
        }
        return childNodes;
    }
    get children() {
        const children = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NodeList"];
        let { firstElementChild } = this;
        while(firstElementChild){
            children.push(firstElementChild);
            firstElementChild = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$non$2d$document$2d$type$2d$child$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["nextElementSibling"])(firstElementChild);
        }
        return children;
    }
    /**
   * @returns {NodeStruct | null}
   */ get firstChild() {
        let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = this;
        while(next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ATTRIBUTE_NODE"])next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        return next === end ? null : next;
    }
    /**
   * @returns {NodeStruct | null}
   */ get firstElementChild() {
        let { firstChild } = this;
        while(firstChild){
            if (firstChild.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]) return firstChild;
            firstChild = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["nextSibling"])(firstChild);
        }
        return null;
    }
    get lastChild() {
        const prev = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]];
        switch(prev.nodeType){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NODE_END"]:
                return prev[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["START"]];
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ATTRIBUTE_NODE"]:
                return null;
        }
        return prev === this ? null : prev;
    }
    get lastElementChild() {
        let { lastChild } = this;
        while(lastChild){
            if (lastChild.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]) return lastChild;
            lastChild = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["previousSibling"])(lastChild);
        }
        return null;
    }
    get childElementCount() {
        return this.children.length;
    }
    prepend(...nodes) {
        insert(this, this.firstChild, nodes);
    }
    append(...nodes) {
        insert(this, this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]], nodes);
    }
    replaceChildren(...nodes) {
        let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = this;
        while(next !== end && next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ATTRIBUTE_NODE"])next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        while(next !== end){
            const after = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnd"])(next)[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
            next.remove();
            next = after;
        }
        if (nodes.length) insert(this, end, nodes);
    }
    getElementsByClassName(className) {
        const elements = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NodeList"];
        let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = this;
        while(next !== end){
            if (next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"] && next.hasAttribute('class') && next.classList.has(className)) elements.push(next);
            next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        }
        return elements;
    }
    getElementsByTagName(tagName) {
        const elements = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NodeList"];
        let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = this;
        while(next !== end){
            if (next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"] && (next.localName === tagName || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["localCase"])(next) === tagName)) elements.push(next);
            next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        }
        return elements;
    }
    querySelector(selectors) {
        const matches = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$matches$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prepareMatch"])(this, selectors);
        let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = this;
        while(next !== end){
            if (next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"] && matches(next)) return next;
            next = next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"] && next.localName === 'template' ? next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]] : next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        }
        return null;
    }
    querySelectorAll(selectors) {
        const matches = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$matches$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prepareMatch"])(this, selectors);
        const elements = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NodeList"];
        let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = this;
        while(next !== end){
            if (next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"] && matches(next)) elements.push(next);
            next = next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"] && next.localName === 'template' ? next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]] : next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        }
        return elements;
    }
    appendChild(node) {
        return this.insertBefore(node, this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]);
    }
    contains(node) {
        let parentNode = node;
        while(parentNode && parentNode !== this)parentNode = parentNode.parentNode;
        return parentNode === this;
    }
    insertBefore(node, before = null) {
        if (node === before) return node;
        if (node === this) throw new Error('unable to append a node to itself');
        const next = before || this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]];
        switch(node.nodeType){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]:
                node.remove();
                node.parentNode = this;
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["knownBoundaries"])(next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]], node, next);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$mutation$2d$observer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["moCallback"])(node, null);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["connectedCallback"])(node);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_FRAGMENT_NODE"]:
                {
                    let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRIVATE"]]: parentNode, firstChild, lastChild } = node;
                    if (firstChild) {
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["knownSegment"])(next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]], firstChild, lastChild, next);
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["knownAdjacent"])(node, node[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]);
                        if (parentNode) parentNode.replaceChildren();
                        do {
                            firstChild.parentNode = this;
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$mutation$2d$observer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["moCallback"])(firstChild, null);
                            if (firstChild.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]) (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["connectedCallback"])(firstChild);
                        }while (firstChild !== lastChild && (firstChild = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["nextSibling"])(firstChild)))
                    }
                    break;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TEXT_NODE"]:
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["COMMENT_NODE"]:
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CDATA_SECTION_NODE"]:
                node.remove();
            /* eslint no-fallthrough:0 */ // this covers DOCUMENT_TYPE_NODE too
            default:
                node.parentNode = this;
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["knownSiblings"])(next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]], node, next);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$mutation$2d$observer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["moCallback"])(node, null);
                break;
        }
        return node;
    }
    normalize() {
        let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = this;
        while(next !== end){
            const { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: $next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]]: $prev, nodeType } = next;
            if (nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TEXT_NODE"]) {
                if (!next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]]) next.remove();
                else if ($prev && $prev.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TEXT_NODE"]) {
                    $prev.textContent += next.textContent;
                    next.remove();
                }
            }
            next = $next;
        }
    }
    removeChild(node) {
        if (node.parentNode !== this) throw new Error('node is not a child');
        node.remove();
        return node;
    }
    replaceChild(node, replaced) {
        const next = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnd"])(replaced)[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        replaced.remove();
        this.insertBefore(node, next);
        return replaced;
    }
}
}}),
"[project]/node_modules/linkedom/esm/mixin/non-element-parent-node.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// https://dom.spec.whatwg.org/#interface-nonelementparentnode
// Document, DocumentFragment
__turbopack_context__.s({
    "NonElementParentNode": (()=>NonElementParentNode)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$jsdon$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/jsdon.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$parent$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/mixin/parent-node.js [app-route] (ecmascript)");
;
;
;
;
class NonElementParentNode extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$parent$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ParentNode"] {
    getElementById(id) {
        let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = this;
        while(next !== end){
            if (next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"] && next.id === id) return next;
            next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        }
        return null;
    }
    cloneNode(deep) {
        const { ownerDocument, constructor } = this;
        const nonEPN = new constructor(ownerDocument);
        if (deep) {
            const { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = nonEPN;
            for (const node of this.childNodes)nonEPN.insertBefore(node.cloneNode(deep), end);
        }
        return nonEPN;
    }
    toString() {
        const { childNodes, localName } = this;
        return `<${localName}>${childNodes.join('')}</${localName}>`;
    }
    toJSON() {
        const json = [];
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$jsdon$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["nonElementAsJSON"])(this, json);
        return json;
    }
}
}}),
"[project]/node_modules/linkedom/esm/interface/document-fragment.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DocumentFragment": (()=>DocumentFragment)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$non$2d$element$2d$parent$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/mixin/non-element-parent-node.js [app-route] (ecmascript)");
;
;
class DocumentFragment extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$non$2d$element$2d$parent$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NonElementParentNode"] {
    constructor(ownerDocument){
        super(ownerDocument, '#document-fragment', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_FRAGMENT_NODE"]);
    }
}
}}),
"[project]/node_modules/linkedom/esm/interface/document-type.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DocumentType": (()=>DocumentType)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$jsdon$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/jsdon.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/node.js [app-route] (ecmascript)");
;
;
;
class DocumentType extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Node"] {
    constructor(ownerDocument, name, publicId = '', systemId = ''){
        super(ownerDocument, '#document-type', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_TYPE_NODE"]);
        this.name = name;
        this.publicId = publicId;
        this.systemId = systemId;
    }
    cloneNode() {
        const { ownerDocument, name, publicId, systemId } = this;
        return new DocumentType(ownerDocument, name, publicId, systemId);
    }
    toString() {
        const { name, publicId, systemId } = this;
        const hasPublic = 0 < publicId.length;
        const str = [
            name
        ];
        if (hasPublic) str.push('PUBLIC', `"${publicId}"`);
        if (systemId.length) {
            if (!hasPublic) str.push('SYSTEM');
            str.push(`"${systemId}"`);
        }
        return `<!DOCTYPE ${str.join(' ')}>`;
    }
    toJSON() {
        const json = [];
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$jsdon$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["documentTypeAsJSON"])(this, json);
        return json;
    }
}
}}),
"[project]/node_modules/linkedom/esm/mixin/inner-html.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getInnerHtml": (()=>getInnerHtml),
    "setInnerHtml": (()=>setInnerHtml)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$parse$2d$from$2d$string$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/parse-from-string.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/utils.js [app-route] (ecmascript)");
;
;
;
;
const getInnerHtml = (node)=>node.childNodes.join('');
const setInnerHtml = (node, html)=>{
    const { ownerDocument } = node;
    const { constructor } = ownerDocument;
    const document = new constructor;
    document[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CUSTOM_ELEMENTS"]] = ownerDocument[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CUSTOM_ELEMENTS"]];
    const { childNodes } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$parse$2d$from$2d$string$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseFromString"])(document, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ignoreCase"])(node), html);
    node.replaceChildren(...childNodes.map(setOwnerDocument, ownerDocument));
};
function setOwnerDocument(node) {
    node.ownerDocument = this;
    switch(node.nodeType){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]:
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_FRAGMENT_NODE"]:
            node.childNodes.forEach(setOwnerDocument, this);
            break;
    }
    return node;
}
}}),
"[project]/node_modules/linkedom/esm/dom/string-map.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DOMStringMap": (()=>DOMStringMap)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uhyphen$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/uhyphen/esm/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/object.js [app-route] (ecmascript)");
;
;
const refs = new WeakMap;
const key = (name)=>`data-${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uhyphen$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(name)}`;
const prop = (name)=>name.slice(5).replace(/-([a-z])/g, (_, $1)=>$1.toUpperCase());
const handler = {
    get (dataset, name) {
        if (name in dataset) return refs.get(dataset).getAttribute(key(name));
    },
    set (dataset, name, value) {
        dataset[name] = value;
        refs.get(dataset).setAttribute(key(name), value);
        return true;
    },
    deleteProperty (dataset, name) {
        if (name in dataset) refs.get(dataset).removeAttribute(key(name));
        return delete dataset[name];
    }
};
class DOMStringMap {
    /**
   * @param {Element} ref
   */ constructor(ref){
        for (const { name, value } of ref.attributes){
            if (/^data-/.test(name)) this[prop(name)] = value;
        }
        refs.set(this, ref);
        return new Proxy(this, handler);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setPrototypeOf"])(DOMStringMap.prototype, null);
}}),
"[project]/node_modules/linkedom/esm/dom/token-list.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DOMTokenList": (()=>DOMTokenList)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$attr$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/attr.js [app-route] (ecmascript)");
;
;
;
const { add } = Set.prototype;
const addTokens = (self, tokens)=>{
    for (const token of tokens){
        if (token) add.call(self, token);
    }
};
const update = ({ [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OWNER_ELEMENT"]]: ownerElement, value })=>{
    const attribute = ownerElement.getAttributeNode('class');
    if (attribute) attribute.value = value;
    else (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setAttribute"])(ownerElement, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$attr$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Attr"](ownerElement.ownerDocument, 'class', value));
};
class DOMTokenList extends Set {
    constructor(ownerElement){
        super();
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OWNER_ELEMENT"]] = ownerElement;
        const attribute = ownerElement.getAttributeNode('class');
        if (attribute) addTokens(this, attribute.value.split(/\s+/));
    }
    get length() {
        return this.size;
    }
    get value() {
        return [
            ...this
        ].join(' ');
    }
    /**
   * @param  {...string} tokens
   */ add(...tokens) {
        addTokens(this, tokens);
        update(this);
    }
    /**
   * @param {string} token
   */ contains(token) {
        return this.has(token);
    }
    /**
   * @param  {...string} tokens
   */ remove(...tokens) {
        for (const token of tokens)this.delete(token);
        update(this);
    }
    /**
   * @param {string} token
   * @param {boolean?} force
   */ toggle(token, force) {
        if (this.has(token)) {
            if (force) return true;
            this.delete(token);
            update(this);
        } else if (force || arguments.length === 1) {
            super.add(token);
            update(this);
            return true;
        }
        return false;
    }
    /**
   * @param {string} token
   * @param {string} newToken
   */ replace(token, newToken) {
        if (this.has(token)) {
            this.delete(token);
            super.add(newToken);
            update(this);
            return true;
        }
        return false;
    }
    /**
   * @param {string} token
   */ supports() {
        return true;
    }
}
}}),
"[project]/node_modules/linkedom/esm/interface/css-style-declaration.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CSSStyleDeclaration": (()=>CSSStyleDeclaration)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uhyphen$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/uhyphen/esm/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
;
;
const refs = new WeakMap;
const getKeys = (style)=>[
        ...style.keys()
    ].filter((key)=>key !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRIVATE"]);
const updateKeys = (style)=>{
    const attr = refs.get(style).getAttributeNode('style');
    if (!attr || attr[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CHANGED"]] || style.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRIVATE"]) !== attr) {
        style.clear();
        if (attr) {
            style.set(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRIVATE"], attr);
            for (const rule of attr[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]].split(/\s*;\s*/)){
                let [key, ...rest] = rule.split(':');
                if (rest.length > 0) {
                    key = key.trim();
                    const value = rest.join(':').trim();
                    if (key && value) style.set(key, value);
                }
            }
        }
    }
    return attr;
};
const handler = {
    get (style, name) {
        if (name in prototype) return style[name];
        updateKeys(style);
        if (name === 'length') return getKeys(style).length;
        if (/^\d+$/.test(name)) return getKeys(style)[name];
        return style.get((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uhyphen$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(name));
    },
    set (style, name, value) {
        if (name === 'cssText') style[name] = value;
        else {
            let attr = updateKeys(style);
            if (value == null) style.delete((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uhyphen$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(name));
            else style.set((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uhyphen$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(name), value);
            if (!attr) {
                const element = refs.get(style);
                attr = element.ownerDocument.createAttribute('style');
                element.setAttributeNode(attr);
                style.set(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRIVATE"], attr);
            }
            attr[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CHANGED"]] = false;
            attr[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VALUE"]] = style.toString();
        }
        return true;
    }
};
class CSSStyleDeclaration extends Map {
    constructor(element){
        super();
        refs.set(this, element);
        /* c8 ignore start */ return new Proxy(this, handler);
    /* c8 ignore stop */ }
    get cssText() {
        return this.toString();
    }
    set cssText(value) {
        refs.get(this).setAttribute('style', value);
    }
    getPropertyValue(name) {
        const self = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRIVATE"]];
        return handler.get(self, name);
    }
    setProperty(name, value) {
        const self = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRIVATE"]];
        handler.set(self, name, value);
    }
    removeProperty(name) {
        const self = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRIVATE"]];
        handler.set(self, name, null);
    }
    [Symbol.iterator]() {
        const self = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRIVATE"]];
        updateKeys(self);
        const keys = getKeys(self);
        const { length } = keys;
        let i = 0;
        return {
            next () {
                const done = i === length;
                return {
                    done,
                    value: done ? null : keys[i++]
                };
            }
        };
    }
    get [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRIVATE"]]() {
        return this;
    }
    toString() {
        const self = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRIVATE"]];
        updateKeys(self);
        const cssText = [];
        self.forEach(push, cssText);
        return cssText.join(';');
    }
}
const { prototype } = CSSStyleDeclaration;
function push(value, key) {
    if (key !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRIVATE"]) this.push(`${key}:${value}`);
}
}}),
"[project]/node_modules/linkedom/esm/interface/event.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// https://dom.spec.whatwg.org/#interface-event
/* c8 ignore start */ // Node 15 has Event but 14 and 12 don't
__turbopack_context__.s({
    "Event": (()=>GlobalEvent)
});
const BUBBLING_PHASE = 3;
const AT_TARGET = 2;
const CAPTURING_PHASE = 1;
const NONE = 0;
function getCurrentTarget(ev) {
    return ev.currentTarget;
}
/**
 * @implements globalThis.Event
 */ class GlobalEvent {
    static get BUBBLING_PHASE() {
        return BUBBLING_PHASE;
    }
    static get AT_TARGET() {
        return AT_TARGET;
    }
    static get CAPTURING_PHASE() {
        return CAPTURING_PHASE;
    }
    static get NONE() {
        return NONE;
    }
    constructor(type, eventInitDict = {}){
        this.type = type;
        this.bubbles = !!eventInitDict.bubbles;
        this.cancelBubble = false;
        this._stopImmediatePropagationFlag = false;
        this.cancelable = !!eventInitDict.cancelable;
        this.eventPhase = this.NONE;
        this.timeStamp = Date.now();
        this.defaultPrevented = false;
        this.originalTarget = null;
        this.returnValue = null;
        this.srcElement = null;
        this.target = null;
        this._path = [];
    }
    get BUBBLING_PHASE() {
        return BUBBLING_PHASE;
    }
    get AT_TARGET() {
        return AT_TARGET;
    }
    get CAPTURING_PHASE() {
        return CAPTURING_PHASE;
    }
    get NONE() {
        return NONE;
    }
    preventDefault() {
        this.defaultPrevented = true;
    }
    // simplified implementation, should be https://dom.spec.whatwg.org/#dom-event-composedpath
    composedPath() {
        return this._path.map(getCurrentTarget);
    }
    stopPropagation() {
        this.cancelBubble = true;
    }
    stopImmediatePropagation() {
        this.stopPropagation();
        this._stopImmediatePropagationFlag = true;
    }
}
;
 /* c8 ignore stop */ }}),
"[project]/node_modules/linkedom/esm/interface/named-node-map.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @implements globalThis.NamedNodeMap
 */ __turbopack_context__.s({
    "NamedNodeMap": (()=>NamedNodeMap)
});
class NamedNodeMap extends Array {
    constructor(ownerElement){
        super();
        this.ownerElement = ownerElement;
    }
    getNamedItem(name) {
        return this.ownerElement.getAttributeNode(name);
    }
    setNamedItem(attr) {
        this.ownerElement.setAttributeNode(attr);
        this.unshift(attr);
    }
    removeNamedItem(name) {
        const item = this.getNamedItem(name);
        this.ownerElement.removeAttribute(name);
        this.splice(this.indexOf(item), 1);
    }
    item(index) {
        return index < this.length ? this[index] : null;
    }
    /* c8 ignore start */ getNamedItemNS(_, name) {
        return this.getNamedItem(name);
    }
    setNamedItemNS(_, attr) {
        return this.setNamedItem(attr);
    }
    removeNamedItemNS(_, name) {
        return this.removeNamedItem(name);
    }
}
}}),
"[project]/node_modules/linkedom/esm/interface/shadow-root.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ShadowRoot": (()=>ShadowRoot)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$inner$2d$html$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/mixin/inner-html.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$non$2d$element$2d$parent$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/mixin/non-element-parent-node.js [app-route] (ecmascript)");
;
;
;
class ShadowRoot extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$non$2d$element$2d$parent$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NonElementParentNode"] {
    constructor(host){
        super(host.ownerDocument, '#shadow-root', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_FRAGMENT_NODE"]);
        this.host = host;
    }
    get innerHTML() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$inner$2d$html$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getInnerHtml"])(this);
    }
    set innerHTML(html) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$inner$2d$html$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setInnerHtml"])(this, html);
    }
}
}}),
"[project]/node_modules/linkedom/esm/interface/element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// https://dom.spec.whatwg.org/#interface-element
__turbopack_context__.s({
    "Element": (()=>Element)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/utils.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$jsdon$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/jsdon.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$matches$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/matches.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$shadow$2d$roots$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/shadow-roots.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/node.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$non$2d$document$2d$type$2d$child$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/mixin/non-document-type-child-node.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$child$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/mixin/child-node.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$inner$2d$html$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/mixin/inner-html.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$parent$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/mixin/parent-node.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$dom$2f$string$2d$map$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/dom/string-map.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$dom$2f$token$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/dom/token-list.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$css$2d$style$2d$declaration$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/css-style-declaration.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/event.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$named$2d$node$2d$map$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/named-node-map.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$shadow$2d$root$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/shadow-root.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/node-list.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$attr$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/attr.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/text.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$text$2d$escaper$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/text-escaper.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// <utils>
const attributesHandler = {
    get (target, key) {
        return key in target ? target[key] : target.find(({ name })=>name === key);
    }
};
const create = (ownerDocument, element, localName)=>{
    if ('ownerSVGElement' in element) {
        const svg = ownerDocument.createElementNS(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SVG_NAMESPACE"], localName);
        svg.ownerSVGElement = element.ownerSVGElement;
        return svg;
    }
    return ownerDocument.createElement(localName);
};
const isVoid = ({ localName, ownerDocument })=>{
    return ownerDocument[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MIME"]].voidElements.test(localName);
};
class Element extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$parent$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ParentNode"] {
    constructor(ownerDocument, localName){
        super(ownerDocument, localName, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]);
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLASS_LIST"]] = null;
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DATASET"]] = null;
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["STYLE"]] = null;
    }
    // <Mixins>
    get isConnected() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isConnected"])(this);
    }
    get parentElement() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parentElement"])(this);
    }
    get previousSibling() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["previousSibling"])(this);
    }
    get nextSibling() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["nextSibling"])(this);
    }
    get namespaceURI() {
        return 'http://www.w3.org/1999/xhtml';
    }
    get previousElementSibling() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$non$2d$document$2d$type$2d$child$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["previousElementSibling"])(this);
    }
    get nextElementSibling() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$non$2d$document$2d$type$2d$child$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["nextElementSibling"])(this);
    }
    before(...nodes) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$child$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["before"])(this, nodes);
    }
    after(...nodes) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$child$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["after"])(this, nodes);
    }
    replaceWith(...nodes) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$child$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["replaceWith"])(this, nodes);
    }
    remove() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$child$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["remove"])(this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]], this, this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]);
    }
    // </Mixins>
    // <specialGetters>
    get id() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'id');
    }
    set id(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'id', value);
    }
    get className() {
        return this.classList.value;
    }
    set className(value) {
        const { classList } = this;
        classList.clear();
        classList.add(...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["String"])(value).split(/\s+/));
    }
    get nodeName() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["localCase"])(this);
    }
    get tagName() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["localCase"])(this);
    }
    get classList() {
        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLASS_LIST"]] || (this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLASS_LIST"]] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$dom$2f$token$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOMTokenList"](this));
    }
    get dataset() {
        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DATASET"]] || (this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DATASET"]] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$dom$2f$string$2d$map$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOMStringMap"](this));
    }
    getBoundingClientRect() {
        return {
            x: 0,
            y: 0,
            bottom: 0,
            height: 0,
            left: 0,
            right: 0,
            top: 0,
            width: 0
        };
    }
    get nonce() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'nonce');
    }
    set nonce(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'nonce', value);
    }
    get style() {
        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["STYLE"]] || (this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["STYLE"]] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$css$2d$style$2d$declaration$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CSSStyleDeclaration"](this));
    }
    get tabIndex() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["numericAttribute"].get(this, 'tabindex') || -1;
    }
    set tabIndex(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["numericAttribute"].set(this, 'tabindex', value);
    }
    get slot() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'slot');
    }
    set slot(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'slot', value);
    }
    // </specialGetters>
    // <contentRelated>
    get innerText() {
        const text = [];
        let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = this;
        while(next !== end){
            if (next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TEXT_NODE"]) {
                text.push(next.textContent.replace(/\s+/g, ' '));
            } else if (text.length && next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]] != end && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BLOCK_ELEMENTS"].has(next.tagName)) {
                text.push('\n');
            }
            next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        }
        return text.join('');
    }
    /**
   * @returns {String}
   */ get textContent() {
        const text = [];
        let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = this;
        while(next !== end){
            const nodeType = next.nodeType;
            if (nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TEXT_NODE"] || nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CDATA_SECTION_NODE"]) text.push(next.textContent);
            next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        }
        return text.join('');
    }
    set textContent(text) {
        this.replaceChildren();
        if (text != null && text !== '') this.appendChild(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Text"](this.ownerDocument, text));
    }
    get innerHTML() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$inner$2d$html$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getInnerHtml"])(this);
    }
    set innerHTML(html) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$inner$2d$html$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setInnerHtml"])(this, html);
    }
    get outerHTML() {
        return this.toString();
    }
    set outerHTML(html) {
        const template = this.ownerDocument.createElement('');
        template.innerHTML = html;
        this.replaceWith(...template.childNodes);
    }
    // </contentRelated>
    // <attributes>
    get attributes() {
        const attributes = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$named$2d$node$2d$map$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NamedNodeMap"](this);
        let next = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        while(next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ATTRIBUTE_NODE"]){
            attributes.push(next);
            next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        }
        return new Proxy(attributes, attributesHandler);
    }
    focus() {
        this.dispatchEvent(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Event"]('focus'));
    }
    getAttribute(name) {
        if (name === 'class') return this.className;
        const attribute = this.getAttributeNode(name);
        return attribute && ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ignoreCase"])(this) ? attribute.value : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$text$2d$escaper$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["escape"])(attribute.value));
    }
    getAttributeNode(name) {
        let next = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        while(next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ATTRIBUTE_NODE"]){
            if (next.name === name) return next;
            next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        }
        return null;
    }
    getAttributeNames() {
        const attributes = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NodeList"];
        let next = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        while(next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ATTRIBUTE_NODE"]){
            attributes.push(next.name);
            next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        }
        return attributes;
    }
    hasAttribute(name) {
        return !!this.getAttributeNode(name);
    }
    hasAttributes() {
        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]].nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ATTRIBUTE_NODE"];
    }
    removeAttribute(name) {
        if (name === 'class' && this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLASS_LIST"]]) this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLASS_LIST"]].clear();
        let next = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        while(next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ATTRIBUTE_NODE"]){
            if (next.name === name) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["removeAttribute"])(this, next);
                return;
            }
            next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        }
    }
    removeAttributeNode(attribute) {
        let next = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        while(next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ATTRIBUTE_NODE"]){
            if (next === attribute) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["removeAttribute"])(this, next);
                return;
            }
            next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        }
    }
    setAttribute(name, value) {
        if (name === 'class') this.className = value;
        else {
            const attribute = this.getAttributeNode(name);
            if (attribute) attribute.value = value;
            else (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setAttribute"])(this, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$attr$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Attr"](this.ownerDocument, name, value));
        }
    }
    setAttributeNode(attribute) {
        const { name } = attribute;
        const previously = this.getAttributeNode(name);
        if (previously !== attribute) {
            if (previously) this.removeAttributeNode(previously);
            const { ownerElement } = attribute;
            if (ownerElement) ownerElement.removeAttributeNode(attribute);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setAttribute"])(this, attribute);
        }
        return previously;
    }
    toggleAttribute(name, force) {
        if (this.hasAttribute(name)) {
            if (!force) {
                this.removeAttribute(name);
                return false;
            }
            return true;
        } else if (force || arguments.length === 1) {
            this.setAttribute(name, '');
            return true;
        }
        return false;
    }
    // </attributes>
    // <ShadowDOM>
    get shadowRoot() {
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$shadow$2d$roots$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["shadowRoots"].has(this)) {
            const { mode, shadowRoot } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$shadow$2d$roots$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["shadowRoots"].get(this);
            if (mode === 'open') return shadowRoot;
        }
        return null;
    }
    attachShadow(init) {
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$shadow$2d$roots$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["shadowRoots"].has(this)) throw new Error('operation not supported');
        // TODO: shadowRoot should be likely a specialized class that extends DocumentFragment
        //       but until DSD is out, I am not sure I should spend time on this.
        const shadowRoot = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$shadow$2d$root$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ShadowRoot"](this);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$shadow$2d$roots$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["shadowRoots"].set(this, {
            mode: init.mode,
            shadowRoot
        });
        return shadowRoot;
    }
    // </ShadowDOM>
    // <selectors>
    matches(selectors) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$matches$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["matches"])(this, selectors);
    }
    closest(selectors) {
        let parentElement = this;
        const matches = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$matches$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prepareMatch"])(parentElement, selectors);
        while(parentElement && !matches(parentElement))parentElement = parentElement.parentElement;
        return parentElement;
    }
    // </selectors>
    // <insertAdjacent>
    insertAdjacentElement(position, element) {
        const { parentElement } = this;
        switch(position){
            case 'beforebegin':
                if (parentElement) {
                    parentElement.insertBefore(element, this);
                    break;
                }
                return null;
            case 'afterbegin':
                this.insertBefore(element, this.firstChild);
                break;
            case 'beforeend':
                this.insertBefore(element, null);
                break;
            case 'afterend':
                if (parentElement) {
                    parentElement.insertBefore(element, this.nextSibling);
                    break;
                }
                return null;
        }
        return element;
    }
    insertAdjacentHTML(position, html) {
        this.insertAdjacentElement(position, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["htmlToFragment"])(this.ownerDocument, html));
    }
    insertAdjacentText(position, text) {
        const node = this.ownerDocument.createTextNode(text);
        this.insertAdjacentElement(position, node);
    }
    // </insertAdjacent>
    cloneNode(deep = false) {
        const { ownerDocument, localName } = this;
        const addNext = (next)=>{
            next.parentNode = parentNode;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["knownAdjacent"])($next, next);
            $next = next;
        };
        const clone = create(ownerDocument, this, localName);
        let parentNode = clone, $next = clone;
        let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: prev } = this;
        while(next !== prev && (deep || next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ATTRIBUTE_NODE"])){
            switch(next.nodeType){
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NODE_END"]:
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["knownAdjacent"])($next, parentNode[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]);
                    $next = parentNode[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]];
                    parentNode = parentNode.parentNode;
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]:
                    {
                        const node = create(ownerDocument, next, next.localName);
                        addNext(node);
                        parentNode = node;
                        break;
                    }
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ATTRIBUTE_NODE"]:
                    {
                        const attr = next.cloneNode(deep);
                        attr.ownerElement = parentNode;
                        addNext(attr);
                        break;
                    }
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TEXT_NODE"]:
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["COMMENT_NODE"]:
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CDATA_SECTION_NODE"]:
                    addNext(next.cloneNode(deep));
                    break;
            }
            next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["knownAdjacent"])($next, clone[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]);
        return clone;
    }
    // <custom>
    toString() {
        const out = [];
        const { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = this;
        let next = {
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: this
        };
        let isOpened = false;
        do {
            next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
            switch(next.nodeType){
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ATTRIBUTE_NODE"]:
                    {
                        const attr = ' ' + next;
                        switch(attr){
                            case ' id':
                            case ' class':
                            case ' style':
                                break;
                            default:
                                out.push(attr);
                        }
                        break;
                    }
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NODE_END"]:
                    {
                        const start = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["START"]];
                        if (isOpened) {
                            if ('ownerSVGElement' in start) out.push(' />');
                            else if (isVoid(start)) out.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ignoreCase"])(start) ? '>' : ' />');
                            else out.push(`></${start.localName}>`);
                            isOpened = false;
                        } else out.push(`</${start.localName}>`);
                        break;
                    }
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]:
                    if (isOpened) out.push('>');
                    if (next.toString !== this.toString) {
                        out.push(next.toString());
                        next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]];
                        isOpened = false;
                    } else {
                        out.push(`<${next.localName}`);
                        isOpened = true;
                    }
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TEXT_NODE"]:
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["COMMENT_NODE"]:
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CDATA_SECTION_NODE"]:
                    out.push((isOpened ? '>' : '') + next);
                    isOpened = false;
                    break;
            }
        }while (next !== end)
        return out.join('');
    }
    toJSON() {
        const json = [];
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$jsdon$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["elementAsJSON"])(this, json);
        return json;
    }
    // </custom>
    /* c8 ignore start */ getAttributeNS(_, name) {
        return this.getAttribute(name);
    }
    getElementsByTagNameNS(_, name) {
        return this.getElementsByTagName(name);
    }
    hasAttributeNS(_, name) {
        return this.hasAttribute(name);
    }
    removeAttributeNS(_, name) {
        this.removeAttribute(name);
    }
    setAttributeNS(_, name, value) {
        this.setAttribute(name, value);
    }
    setAttributeNodeNS(attr) {
        return this.setAttributeNode(attr);
    }
}
}}),
"[project]/node_modules/linkedom/esm/svg/element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SVGElement": (()=>SVGElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/utils.js [app-route] (ecmascript)");
;
;
const classNames = new WeakMap;
const handler = {
    get (target, name) {
        return target[name];
    },
    set (target, name, value) {
        target[name] = value;
        return true;
    }
};
class SVGElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Element"] {
    constructor(ownerDocument, localName, ownerSVGElement = null){
        super(ownerDocument, localName);
        this.ownerSVGElement = ownerSVGElement;
    }
    get className() {
        if (!classNames.has(this)) classNames.set(this, new Proxy({
            baseVal: '',
            animVal: ''
        }, handler));
        return classNames.get(this);
    }
    /* c8 ignore start */ set className(value) {
        const { classList } = this;
        classList.clear();
        classList.add(...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["String"])(value).split(/\s+/));
    }
    /* c8 ignore stop */ get namespaceURI() {
        return 'http://www.w3.org/2000/svg';
    }
    getAttribute(name) {
        return name === 'class' ? [
            ...this.classList
        ].join(' ') : super.getAttribute(name);
    }
    setAttribute(name, value) {
        if (name === 'class') this.className = value;
        else if (name === 'style') {
            const { className } = this;
            className.baseVal = className.animVal = value;
        }
        super.setAttribute(name, value);
    }
}
}}),
"[project]/node_modules/linkedom/esm/shared/facades.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Attr": (()=>Attr),
    "CDATASection": (()=>CDATASection),
    "CharacterData": (()=>CharacterData),
    "Comment": (()=>Comment),
    "DocumentFragment": (()=>DocumentFragment),
    "DocumentType": (()=>DocumentType),
    "Element": (()=>Element),
    "Facades": (()=>Facades),
    "Node": (()=>Node),
    "SVGElement": (()=>SVGElement),
    "ShadowRoot": (()=>ShadowRoot),
    "Text": (()=>Text),
    "illegalConstructor": (()=>illegalConstructor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$attr$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/attr.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$character$2d$data$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/character-data.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$cdata$2d$section$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/cdata-section.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$comment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/comment.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2d$fragment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/document-fragment.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2d$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/document-type.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/node.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$shadow$2d$root$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/shadow-root.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/text.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$svg$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/svg/element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/object.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
const illegalConstructor = ()=>{
    throw new TypeError('Illegal constructor');
};
function Attr() {
    illegalConstructor();
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setPrototypeOf"])(Attr, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$attr$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Attr"]);
Attr.prototype = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$attr$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Attr"].prototype;
function CDATASection() {
    illegalConstructor();
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setPrototypeOf"])(CDATASection, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$cdata$2d$section$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CDATASection"]);
CDATASection.prototype = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$cdata$2d$section$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CDATASection"].prototype;
function CharacterData() {
    illegalConstructor();
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setPrototypeOf"])(CharacterData, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$character$2d$data$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CharacterData"]);
CharacterData.prototype = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$character$2d$data$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CharacterData"].prototype;
function Comment() {
    illegalConstructor();
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setPrototypeOf"])(Comment, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$comment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Comment"]);
Comment.prototype = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$comment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Comment"].prototype;
function DocumentFragment() {
    illegalConstructor();
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setPrototypeOf"])(DocumentFragment, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2d$fragment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DocumentFragment"]);
DocumentFragment.prototype = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2d$fragment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DocumentFragment"].prototype;
function DocumentType() {
    illegalConstructor();
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setPrototypeOf"])(DocumentType, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2d$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DocumentType"]);
DocumentType.prototype = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2d$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DocumentType"].prototype;
function Element() {
    illegalConstructor();
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setPrototypeOf"])(Element, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Element"]);
Element.prototype = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Element"].prototype;
function Node() {
    illegalConstructor();
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setPrototypeOf"])(Node, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Node"]);
Node.prototype = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Node"].prototype;
function ShadowRoot() {
    illegalConstructor();
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setPrototypeOf"])(ShadowRoot, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$shadow$2d$root$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ShadowRoot"]);
ShadowRoot.prototype = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$shadow$2d$root$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ShadowRoot"].prototype;
function Text() {
    illegalConstructor();
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setPrototypeOf"])(Text, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Text"]);
Text.prototype = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Text"].prototype;
function SVGElement() {
    illegalConstructor();
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setPrototypeOf"])(SVGElement, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$svg$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SVGElement"]);
SVGElement.prototype = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$svg$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SVGElement"].prototype;
const Facades = {
    Attr,
    CDATASection,
    CharacterData,
    Comment,
    DocumentFragment,
    DocumentType,
    Element,
    Node,
    ShadowRoot,
    Text,
    SVGElement
};
}}),
"[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLElement": (()=>HTMLElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/event.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/custom-element-registry.js [app-route] (ecmascript)");
;
;
;
;
;
const Level0 = new WeakMap;
const level0 = {
    get (element, name) {
        return Level0.has(element) && Level0.get(element)[name] || null;
    },
    set (element, name, value) {
        if (!Level0.has(element)) Level0.set(element, {});
        const handlers = Level0.get(element);
        const type = name.slice(2);
        if (handlers[name]) element.removeEventListener(type, handlers[name], false);
        if (handlers[name] = value) element.addEventListener(type, value, false);
    }
};
class HTMLElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Element"] {
    static get observedAttributes() {
        return [];
    }
    constructor(ownerDocument = null, localName = ''){
        super(ownerDocument, localName);
        const ownerLess = !ownerDocument;
        let options;
        if (ownerLess) {
            const { constructor: Class } = this;
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Classes"].has(Class)) throw new Error('unable to initialize this Custom Element');
            ({ ownerDocument, localName, options } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Classes"].get(Class));
        }
        if (ownerDocument[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UPGRADE"]]) {
            const { element, values } = ownerDocument[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UPGRADE"]];
            ownerDocument[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UPGRADE"]] = null;
            for (const [key, value] of values)element[key] = value;
            return element;
        }
        if (ownerLess) {
            this.ownerDocument = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]].ownerDocument = ownerDocument;
            this.localName = localName;
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customElements"].set(this, {
                connected: false
            });
            if (options.is) this.setAttribute('is', options.is);
        }
    }
    /* c8 ignore start */ /* TODO: what about these?
  offsetHeight
  offsetLeft
  offsetParent
  offsetTop
  offsetWidth
  */ blur() {
        this.dispatchEvent(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Event"]('blur'));
    }
    click() {
        const clickEvent = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Event"]('click', {
            bubbles: true,
            cancelable: true
        });
        clickEvent.button = 0;
        this.dispatchEvent(clickEvent);
    }
    // Boolean getters
    get accessKeyLabel() {
        const { accessKey } = this;
        return accessKey && `Alt+Shift+${accessKey}`;
    }
    get isContentEditable() {
        return this.hasAttribute('contenteditable');
    }
    // Boolean Accessors
    get contentEditable() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].get(this, 'contenteditable');
    }
    set contentEditable(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].set(this, 'contenteditable', value);
    }
    get draggable() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].get(this, 'draggable');
    }
    set draggable(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].set(this, 'draggable', value);
    }
    get hidden() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].get(this, 'hidden');
    }
    set hidden(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].set(this, 'hidden', value);
    }
    get spellcheck() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].get(this, 'spellcheck');
    }
    set spellcheck(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].set(this, 'spellcheck', value);
    }
    // String Accessors
    get accessKey() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'accesskey');
    }
    set accessKey(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'accesskey', value);
    }
    get dir() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'dir');
    }
    set dir(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'dir', value);
    }
    get lang() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'lang');
    }
    set lang(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'lang', value);
    }
    get title() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'title');
    }
    set title(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'title', value);
    }
    // DOM Level 0
    get onabort() {
        return level0.get(this, 'onabort');
    }
    set onabort(value) {
        level0.set(this, 'onabort', value);
    }
    get onblur() {
        return level0.get(this, 'onblur');
    }
    set onblur(value) {
        level0.set(this, 'onblur', value);
    }
    get oncancel() {
        return level0.get(this, 'oncancel');
    }
    set oncancel(value) {
        level0.set(this, 'oncancel', value);
    }
    get oncanplay() {
        return level0.get(this, 'oncanplay');
    }
    set oncanplay(value) {
        level0.set(this, 'oncanplay', value);
    }
    get oncanplaythrough() {
        return level0.get(this, 'oncanplaythrough');
    }
    set oncanplaythrough(value) {
        level0.set(this, 'oncanplaythrough', value);
    }
    get onchange() {
        return level0.get(this, 'onchange');
    }
    set onchange(value) {
        level0.set(this, 'onchange', value);
    }
    get onclick() {
        return level0.get(this, 'onclick');
    }
    set onclick(value) {
        level0.set(this, 'onclick', value);
    }
    get onclose() {
        return level0.get(this, 'onclose');
    }
    set onclose(value) {
        level0.set(this, 'onclose', value);
    }
    get oncontextmenu() {
        return level0.get(this, 'oncontextmenu');
    }
    set oncontextmenu(value) {
        level0.set(this, 'oncontextmenu', value);
    }
    get oncuechange() {
        return level0.get(this, 'oncuechange');
    }
    set oncuechange(value) {
        level0.set(this, 'oncuechange', value);
    }
    get ondblclick() {
        return level0.get(this, 'ondblclick');
    }
    set ondblclick(value) {
        level0.set(this, 'ondblclick', value);
    }
    get ondrag() {
        return level0.get(this, 'ondrag');
    }
    set ondrag(value) {
        level0.set(this, 'ondrag', value);
    }
    get ondragend() {
        return level0.get(this, 'ondragend');
    }
    set ondragend(value) {
        level0.set(this, 'ondragend', value);
    }
    get ondragenter() {
        return level0.get(this, 'ondragenter');
    }
    set ondragenter(value) {
        level0.set(this, 'ondragenter', value);
    }
    get ondragleave() {
        return level0.get(this, 'ondragleave');
    }
    set ondragleave(value) {
        level0.set(this, 'ondragleave', value);
    }
    get ondragover() {
        return level0.get(this, 'ondragover');
    }
    set ondragover(value) {
        level0.set(this, 'ondragover', value);
    }
    get ondragstart() {
        return level0.get(this, 'ondragstart');
    }
    set ondragstart(value) {
        level0.set(this, 'ondragstart', value);
    }
    get ondrop() {
        return level0.get(this, 'ondrop');
    }
    set ondrop(value) {
        level0.set(this, 'ondrop', value);
    }
    get ondurationchange() {
        return level0.get(this, 'ondurationchange');
    }
    set ondurationchange(value) {
        level0.set(this, 'ondurationchange', value);
    }
    get onemptied() {
        return level0.get(this, 'onemptied');
    }
    set onemptied(value) {
        level0.set(this, 'onemptied', value);
    }
    get onended() {
        return level0.get(this, 'onended');
    }
    set onended(value) {
        level0.set(this, 'onended', value);
    }
    get onerror() {
        return level0.get(this, 'onerror');
    }
    set onerror(value) {
        level0.set(this, 'onerror', value);
    }
    get onfocus() {
        return level0.get(this, 'onfocus');
    }
    set onfocus(value) {
        level0.set(this, 'onfocus', value);
    }
    get oninput() {
        return level0.get(this, 'oninput');
    }
    set oninput(value) {
        level0.set(this, 'oninput', value);
    }
    get oninvalid() {
        return level0.get(this, 'oninvalid');
    }
    set oninvalid(value) {
        level0.set(this, 'oninvalid', value);
    }
    get onkeydown() {
        return level0.get(this, 'onkeydown');
    }
    set onkeydown(value) {
        level0.set(this, 'onkeydown', value);
    }
    get onkeypress() {
        return level0.get(this, 'onkeypress');
    }
    set onkeypress(value) {
        level0.set(this, 'onkeypress', value);
    }
    get onkeyup() {
        return level0.get(this, 'onkeyup');
    }
    set onkeyup(value) {
        level0.set(this, 'onkeyup', value);
    }
    get onload() {
        return level0.get(this, 'onload');
    }
    set onload(value) {
        level0.set(this, 'onload', value);
    }
    get onloadeddata() {
        return level0.get(this, 'onloadeddata');
    }
    set onloadeddata(value) {
        level0.set(this, 'onloadeddata', value);
    }
    get onloadedmetadata() {
        return level0.get(this, 'onloadedmetadata');
    }
    set onloadedmetadata(value) {
        level0.set(this, 'onloadedmetadata', value);
    }
    get onloadstart() {
        return level0.get(this, 'onloadstart');
    }
    set onloadstart(value) {
        level0.set(this, 'onloadstart', value);
    }
    get onmousedown() {
        return level0.get(this, 'onmousedown');
    }
    set onmousedown(value) {
        level0.set(this, 'onmousedown', value);
    }
    get onmouseenter() {
        return level0.get(this, 'onmouseenter');
    }
    set onmouseenter(value) {
        level0.set(this, 'onmouseenter', value);
    }
    get onmouseleave() {
        return level0.get(this, 'onmouseleave');
    }
    set onmouseleave(value) {
        level0.set(this, 'onmouseleave', value);
    }
    get onmousemove() {
        return level0.get(this, 'onmousemove');
    }
    set onmousemove(value) {
        level0.set(this, 'onmousemove', value);
    }
    get onmouseout() {
        return level0.get(this, 'onmouseout');
    }
    set onmouseout(value) {
        level0.set(this, 'onmouseout', value);
    }
    get onmouseover() {
        return level0.get(this, 'onmouseover');
    }
    set onmouseover(value) {
        level0.set(this, 'onmouseover', value);
    }
    get onmouseup() {
        return level0.get(this, 'onmouseup');
    }
    set onmouseup(value) {
        level0.set(this, 'onmouseup', value);
    }
    get onmousewheel() {
        return level0.get(this, 'onmousewheel');
    }
    set onmousewheel(value) {
        level0.set(this, 'onmousewheel', value);
    }
    get onpause() {
        return level0.get(this, 'onpause');
    }
    set onpause(value) {
        level0.set(this, 'onpause', value);
    }
    get onplay() {
        return level0.get(this, 'onplay');
    }
    set onplay(value) {
        level0.set(this, 'onplay', value);
    }
    get onplaying() {
        return level0.get(this, 'onplaying');
    }
    set onplaying(value) {
        level0.set(this, 'onplaying', value);
    }
    get onprogress() {
        return level0.get(this, 'onprogress');
    }
    set onprogress(value) {
        level0.set(this, 'onprogress', value);
    }
    get onratechange() {
        return level0.get(this, 'onratechange');
    }
    set onratechange(value) {
        level0.set(this, 'onratechange', value);
    }
    get onreset() {
        return level0.get(this, 'onreset');
    }
    set onreset(value) {
        level0.set(this, 'onreset', value);
    }
    get onresize() {
        return level0.get(this, 'onresize');
    }
    set onresize(value) {
        level0.set(this, 'onresize', value);
    }
    get onscroll() {
        return level0.get(this, 'onscroll');
    }
    set onscroll(value) {
        level0.set(this, 'onscroll', value);
    }
    get onseeked() {
        return level0.get(this, 'onseeked');
    }
    set onseeked(value) {
        level0.set(this, 'onseeked', value);
    }
    get onseeking() {
        return level0.get(this, 'onseeking');
    }
    set onseeking(value) {
        level0.set(this, 'onseeking', value);
    }
    get onselect() {
        return level0.get(this, 'onselect');
    }
    set onselect(value) {
        level0.set(this, 'onselect', value);
    }
    get onshow() {
        return level0.get(this, 'onshow');
    }
    set onshow(value) {
        level0.set(this, 'onshow', value);
    }
    get onstalled() {
        return level0.get(this, 'onstalled');
    }
    set onstalled(value) {
        level0.set(this, 'onstalled', value);
    }
    get onsubmit() {
        return level0.get(this, 'onsubmit');
    }
    set onsubmit(value) {
        level0.set(this, 'onsubmit', value);
    }
    get onsuspend() {
        return level0.get(this, 'onsuspend');
    }
    set onsuspend(value) {
        level0.set(this, 'onsuspend', value);
    }
    get ontimeupdate() {
        return level0.get(this, 'ontimeupdate');
    }
    set ontimeupdate(value) {
        level0.set(this, 'ontimeupdate', value);
    }
    get ontoggle() {
        return level0.get(this, 'ontoggle');
    }
    set ontoggle(value) {
        level0.set(this, 'ontoggle', value);
    }
    get onvolumechange() {
        return level0.get(this, 'onvolumechange');
    }
    set onvolumechange(value) {
        level0.set(this, 'onvolumechange', value);
    }
    get onwaiting() {
        return level0.get(this, 'onwaiting');
    }
    set onwaiting(value) {
        level0.set(this, 'onwaiting', value);
    }
    get onauxclick() {
        return level0.get(this, 'onauxclick');
    }
    set onauxclick(value) {
        level0.set(this, 'onauxclick', value);
    }
    get ongotpointercapture() {
        return level0.get(this, 'ongotpointercapture');
    }
    set ongotpointercapture(value) {
        level0.set(this, 'ongotpointercapture', value);
    }
    get onlostpointercapture() {
        return level0.get(this, 'onlostpointercapture');
    }
    set onlostpointercapture(value) {
        level0.set(this, 'onlostpointercapture', value);
    }
    get onpointercancel() {
        return level0.get(this, 'onpointercancel');
    }
    set onpointercancel(value) {
        level0.set(this, 'onpointercancel', value);
    }
    get onpointerdown() {
        return level0.get(this, 'onpointerdown');
    }
    set onpointerdown(value) {
        level0.set(this, 'onpointerdown', value);
    }
    get onpointerenter() {
        return level0.get(this, 'onpointerenter');
    }
    set onpointerenter(value) {
        level0.set(this, 'onpointerenter', value);
    }
    get onpointerleave() {
        return level0.get(this, 'onpointerleave');
    }
    set onpointerleave(value) {
        level0.set(this, 'onpointerleave', value);
    }
    get onpointermove() {
        return level0.get(this, 'onpointermove');
    }
    set onpointermove(value) {
        level0.set(this, 'onpointermove', value);
    }
    get onpointerout() {
        return level0.get(this, 'onpointerout');
    }
    set onpointerout(value) {
        level0.set(this, 'onpointerout', value);
    }
    get onpointerover() {
        return level0.get(this, 'onpointerover');
    }
    set onpointerover(value) {
        level0.set(this, 'onpointerover', value);
    }
    get onpointerup() {
        return level0.get(this, 'onpointerup');
    }
    set onpointerup(value) {
        level0.set(this, 'onpointerup', value);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/template-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLTemplateElement": (()=>HTMLTemplateElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
;
;
const tagName = 'template';
/**
 * @implements globalThis.HTMLTemplateElement
 */ class HTMLTemplateElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument){
        super(ownerDocument, tagName);
        const content = this.ownerDocument.createDocumentFragment();
        (this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CONTENT"]] = content)[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRIVATE"]] = this;
    }
    get content() {
        if (this.hasChildNodes() && !this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CONTENT"]].hasChildNodes()) {
            for (const node of this.childNodes)this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CONTENT"]].appendChild(node.cloneNode(true));
        }
        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CONTENT"]];
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])(tagName, HTMLTemplateElement);
;
}}),
"[project]/node_modules/linkedom/esm/html/html-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLHtmlElement": (()=>HTMLHtmlElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLHtmlElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'html'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/text-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TextElement": (()=>TextElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
const { toString } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"].prototype;
class TextElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    get innerHTML() {
        return this.textContent;
    }
    set innerHTML(html) {
        this.textContent = html;
    }
    toString() {
        const outerHTML = toString.call(this.cloneNode());
        return outerHTML.replace('><', ()=>`>${this.textContent}<`);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/script-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLScriptElement": (()=>HTMLScriptElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$text$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/text-element.js [app-route] (ecmascript)");
;
;
;
const tagName = 'script';
/**
 * @implements globalThis.HTMLScriptElement
 */ class HTMLScriptElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$text$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextElement"] {
    constructor(ownerDocument, localName = tagName){
        super(ownerDocument, localName);
    }
    get type() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'type');
    }
    set type(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'type', value);
    }
    get src() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'src');
    }
    set src(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'src', value);
    }
    get defer() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].get(this, 'defer');
    }
    set defer(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].set(this, 'defer', value);
    }
    get crossOrigin() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'crossorigin');
    }
    set crossOrigin(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'crossorigin', value);
    }
    get nomodule() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].get(this, 'nomodule');
    }
    set nomodule(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].set(this, 'nomodule', value);
    }
    get referrerPolicy() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'referrerpolicy');
    }
    set referrerPolicy(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'referrerpolicy', value);
    }
    get nonce() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'nonce');
    }
    set nonce(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'nonce', value);
    }
    get async() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].get(this, 'async');
    }
    set async(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].set(this, 'async', value);
    }
    get text() {
        return this.textContent;
    }
    set text(content) {
        this.textContent = content;
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])(tagName, HTMLScriptElement);
;
}}),
"[project]/node_modules/linkedom/esm/html/frame-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLFrameElement": (()=>HTMLFrameElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLFrameElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'frame'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/i-frame-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLIFrameElement": (()=>HTMLIFrameElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
;
;
const tagName = 'iframe';
/**
 * @implements globalThis.HTMLIFrameElement
 */ class HTMLIFrameElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = tagName){
        super(ownerDocument, localName);
    }
    /* c8 ignore start */ get src() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'src');
    }
    set src(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'src', value);
    }
    get srcdoc() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, "srcdoc");
    }
    set srcdoc(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, "srcdoc", value);
    }
    get name() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, "name");
    }
    set name(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, "name", value);
    }
    get allow() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, "allow");
    }
    set allow(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, "allow", value);
    }
    get allowFullscreen() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].get(this, "allowfullscreen");
    }
    set allowFullscreen(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].set(this, "allowfullscreen", value);
    }
    get referrerPolicy() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, "referrerpolicy");
    }
    set referrerPolicy(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, "referrerpolicy", value);
    }
    get loading() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, "loading");
    }
    set loading(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, "loading", value);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])(tagName, HTMLIFrameElement);
;
}}),
"[project]/node_modules/linkedom/esm/html/object-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLObjectElement": (()=>HTMLObjectElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLObjectElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'object'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/head-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLHeadElement": (()=>HTMLHeadElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLHeadElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'head'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/body-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLBodyElement": (()=>HTMLBodyElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLBodyElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'body'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/style-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLStyleElement": (()=>HTMLStyleElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cssom$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/cssom/lib/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$text$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/text-element.js [app-route] (ecmascript)");
;
;
;
;
const tagName = 'style';
/**
 * @implements globalThis.HTMLStyleElement
 */ class HTMLStyleElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$text$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextElement"] {
    constructor(ownerDocument, localName = tagName){
        super(ownerDocument, localName);
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SHEET"]] = null;
    }
    get sheet() {
        const sheet = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SHEET"]];
        if (sheet !== null) {
            return sheet;
        }
        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SHEET"]] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cssom$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(this.textContent);
    }
    get innerHTML() {
        return super.innerHTML || '';
    }
    set innerHTML(value) {
        super.textContent = value;
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SHEET"]] = null;
    }
    get innerText() {
        return super.innerText || '';
    }
    set innerText(value) {
        super.textContent = value;
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SHEET"]] = null;
    }
    get textContent() {
        return super.textContent || '';
    }
    set textContent(value) {
        super.textContent = value;
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SHEET"]] = null;
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])(tagName, HTMLStyleElement);
;
}}),
"[project]/node_modules/linkedom/esm/html/time-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLTimeElement": (()=>HTMLTimeElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
;
;
/**
 * @implements globalThis.HTMLTimeElement
 */ class HTMLTimeElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'time'){
        super(ownerDocument, localName);
    }
    /**
   * @type {string}
   */ get dateTime() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'datetime');
    }
    set dateTime(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'datetime', value);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])('time', HTMLTimeElement);
;
}}),
"[project]/node_modules/linkedom/esm/html/field-set-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLFieldSetElement": (()=>HTMLFieldSetElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLFieldSetElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'fieldset'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/embed-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLEmbedElement": (()=>HTMLEmbedElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLEmbedElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'embed'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/hr-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLHRElement": (()=>HTMLHRElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLHRElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'hr'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/progress-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLProgressElement": (()=>HTMLProgressElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLProgressElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'progress'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/paragraph-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLParagraphElement": (()=>HTMLParagraphElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLParagraphElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'p'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/table-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLTableElement": (()=>HTMLTableElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLTableElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'table'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/frame-set-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLFrameSetElement": (()=>HTMLFrameSetElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLFrameSetElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'frameset'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/li-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLLIElement": (()=>HTMLLIElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLLIElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'li'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/base-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLBaseElement": (()=>HTMLBaseElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLBaseElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'base'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/data-list-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLDataListElement": (()=>HTMLDataListElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLDataListElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'datalist'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/input-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLInputElement": (()=>HTMLInputElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
;
;
const tagName = 'input';
/**
 * @implements globalThis.HTMLInputElement
 */ class HTMLInputElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = tagName){
        super(ownerDocument, localName);
    }
    /* c8 ignore start */ get autofocus() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].get(this, 'autofocus') || -1;
    }
    set autofocus(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].set(this, 'autofocus', value);
    }
    get disabled() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].get(this, 'disabled');
    }
    set disabled(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].set(this, 'disabled', value);
    }
    get name() {
        return this.getAttribute('name');
    }
    set name(value) {
        this.setAttribute('name', value);
    }
    get placeholder() {
        return this.getAttribute('placeholder');
    }
    set placeholder(value) {
        this.setAttribute('placeholder', value);
    }
    get type() {
        return this.getAttribute('type');
    }
    set type(value) {
        this.setAttribute('type', value);
    }
    get value() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'value');
    }
    set value(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'value', value);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])(tagName, HTMLInputElement);
;
}}),
"[project]/node_modules/linkedom/esm/html/param-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLParamElement": (()=>HTMLParamElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLParamElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'param'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/media-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLMediaElement": (()=>HTMLMediaElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLMediaElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'media'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/audio-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLAudioElement": (()=>HTMLAudioElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLAudioElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'audio'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/heading-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLHeadingElement": (()=>HTMLHeadingElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
;
const tagName = 'h1';
/**
 * @implements globalThis.HTMLHeadingElement
 */ class HTMLHeadingElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = tagName){
        super(ownerDocument, localName);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])([
    tagName,
    'h2',
    'h3',
    'h4',
    'h5',
    'h6'
], HTMLHeadingElement);
;
}}),
"[project]/node_modules/linkedom/esm/html/directory-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLDirectoryElement": (()=>HTMLDirectoryElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLDirectoryElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'dir'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/quote-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLQuoteElement": (()=>HTMLQuoteElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLQuoteElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'quote'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/commonjs/canvas-shim.cjs [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
class Canvas {
    constructor(width, height){
        this.width = width;
        this.height = height;
    }
    getContext() {
        return null;
    }
    toDataURL() {
        return '';
    }
}
module.exports = {
    createCanvas: (width, height)=>new Canvas(width, height)
};
}}),
"[project]/node_modules/linkedom/commonjs/canvas.cjs [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* c8 ignore start */ try {
    module.exports = (()=>{
        const e = new Error("Cannot find module 'canvas'");
        e.code = 'MODULE_NOT_FOUND';
        throw e;
    })();
} catch (fallback) {
    module.exports = __turbopack_context__.r("[project]/node_modules/linkedom/commonjs/canvas-shim.cjs [app-route] (ecmascript)");
} /* c8 ignore stop */ 
}}),
"[project]/node_modules/linkedom/esm/html/canvas-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLCanvasElement": (()=>HTMLCanvasElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$commonjs$2f$canvas$2e$cjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/commonjs/canvas.cjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
;
;
;
;
const { createCanvas } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$commonjs$2f$canvas$2e$cjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"];
const tagName = 'canvas';
/**
 * @implements globalThis.HTMLCanvasElement
 */ class HTMLCanvasElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = tagName){
        super(ownerDocument, localName);
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IMAGE"]] = createCanvas(300, 150);
    }
    get width() {
        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IMAGE"]].width;
    }
    set width(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["numericAttribute"].set(this, 'width', value);
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IMAGE"]].width = value;
    }
    get height() {
        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IMAGE"]].height;
    }
    set height(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["numericAttribute"].set(this, 'height', value);
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IMAGE"]].height = value;
    }
    getContext(type) {
        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IMAGE"]].getContext(type);
    }
    toDataURL(...args) {
        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IMAGE"]].toDataURL(...args);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])(tagName, HTMLCanvasElement);
;
}}),
"[project]/node_modules/linkedom/esm/html/legend-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLLegendElement": (()=>HTMLLegendElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLLegendElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'legend'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/option-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLOptionElement": (()=>HTMLOptionElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
;
;
;
const tagName = 'option';
/**
 * @implements globalThis.HTMLOptionElement
 */ class HTMLOptionElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = tagName){
        super(ownerDocument, localName);
    }
    /* c8 ignore start */ get value() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'value');
    }
    set value(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'value', value);
    }
    /* c8 ignore stop */ get selected() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].get(this, 'selected');
    }
    set selected(value) {
        const option = this.parentElement?.querySelector('option[selected]');
        if (option && option !== this) option.selected = false;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].set(this, 'selected', value);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])(tagName, HTMLOptionElement);
;
}}),
"[project]/node_modules/linkedom/esm/html/span-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLSpanElement": (()=>HTMLSpanElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLSpanElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'span'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/meter-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLMeterElement": (()=>HTMLMeterElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLMeterElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'meter'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/video-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLVideoElement": (()=>HTMLVideoElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLVideoElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'video'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/table-cell-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLTableCellElement": (()=>HTMLTableCellElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLTableCellElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'td'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/title-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLTitleElement": (()=>HTMLTitleElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$text$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/text-element.js [app-route] (ecmascript)");
;
;
const tagName = 'title';
/**
 * @implements globalThis.HTMLTitleElement
 */ class HTMLTitleElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$text$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextElement"] {
    constructor(ownerDocument, localName = tagName){
        super(ownerDocument, localName);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])(tagName, HTMLTitleElement);
;
}}),
"[project]/node_modules/linkedom/esm/html/output-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLOutputElement": (()=>HTMLOutputElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLOutputElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'output'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/table-row-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLTableRowElement": (()=>HTMLTableRowElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLTableRowElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'tr'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/data-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLDataElement": (()=>HTMLDataElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLDataElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'data'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/menu-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLMenuElement": (()=>HTMLMenuElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLMenuElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'menu'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/select-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLSelectElement": (()=>HTMLSelectElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/node-list.js [app-route] (ecmascript)");
;
;
;
;
const tagName = 'select';
/**
 * @implements globalThis.HTMLSelectElement
 */ class HTMLSelectElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = tagName){
        super(ownerDocument, localName);
    }
    get options() {
        let children = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NodeList"];
        let { firstElementChild } = this;
        while(firstElementChild){
            if (firstElementChild.tagName === 'OPTGROUP') children.push(...firstElementChild.children);
            else children.push(firstElementChild);
            firstElementChild = firstElementChild.nextElementSibling;
        }
        return children;
    }
    /* c8 ignore start */ get disabled() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].get(this, 'disabled');
    }
    set disabled(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].set(this, 'disabled', value);
    }
    get name() {
        return this.getAttribute('name');
    }
    set name(value) {
        this.setAttribute('name', value);
    }
    /* c8 ignore stop */ get value() {
        return this.querySelector('option[selected]')?.value;
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])(tagName, HTMLSelectElement);
;
}}),
"[project]/node_modules/linkedom/esm/html/br-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLBRElement": (()=>HTMLBRElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLBRElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'br'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/button-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLButtonElement": (()=>HTMLButtonElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
;
;
const tagName = 'button';
/**
 * @implements globalThis.HTMLButtonElement
 */ class HTMLButtonElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = tagName){
        super(ownerDocument, localName);
    }
    /* c8 ignore start */ get disabled() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].get(this, 'disabled');
    }
    set disabled(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].set(this, 'disabled', value);
    }
    get name() {
        return this.getAttribute('name');
    }
    set name(value) {
        this.setAttribute('name', value);
    }
    get type() {
        return this.getAttribute('type');
    }
    set type(value) {
        this.setAttribute('type', value);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])(tagName, HTMLButtonElement);
;
}}),
"[project]/node_modules/linkedom/esm/html/map-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLMapElement": (()=>HTMLMapElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLMapElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'map'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/opt-group-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLOptGroupElement": (()=>HTMLOptGroupElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLOptGroupElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'optgroup'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/d-list-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLDListElement": (()=>HTMLDListElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLDListElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'dl'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/text-area-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLTextAreaElement": (()=>HTMLTextAreaElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$text$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/text-element.js [app-route] (ecmascript)");
;
;
;
const tagName = 'textarea';
/**
 * @implements globalThis.HTMLTextAreaElement
 */ class HTMLTextAreaElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$text$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextElement"] {
    constructor(ownerDocument, localName = tagName){
        super(ownerDocument, localName);
    }
    /* c8 ignore start */ get disabled() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].get(this, 'disabled');
    }
    set disabled(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].set(this, 'disabled', value);
    }
    get name() {
        return this.getAttribute('name');
    }
    set name(value) {
        this.setAttribute('name', value);
    }
    get placeholder() {
        return this.getAttribute('placeholder');
    }
    set placeholder(value) {
        this.setAttribute('placeholder', value);
    }
    get type() {
        return this.getAttribute('type');
    }
    set type(value) {
        this.setAttribute('type', value);
    }
    get value() {
        return this.textContent;
    }
    set value(content) {
        this.textContent = content;
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])(tagName, HTMLTextAreaElement);
;
}}),
"[project]/node_modules/linkedom/esm/html/font-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLFontElement": (()=>HTMLFontElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLFontElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'font'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/div-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLDivElement": (()=>HTMLDivElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLDivElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'div'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/link-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLLinkElement": (()=>HTMLLinkElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
;
;
const tagName = 'link';
/**
 * @implements globalThis.HTMLLinkElement
 */ class HTMLLinkElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = tagName){
        super(ownerDocument, localName);
    }
    /* c8 ignore start */ // copy paste from img.src, already covered
    get disabled() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].get(this, 'disabled');
    }
    set disabled(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["booleanAttribute"].set(this, 'disabled', value);
    }
    get href() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'href').trim();
    }
    set href(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'href', value);
    }
    get hreflang() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'hreflang');
    }
    set hreflang(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'hreflang', value);
    }
    get media() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'media');
    }
    set media(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'media', value);
    }
    get rel() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'rel');
    }
    set rel(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'rel', value);
    }
    get type() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'type');
    }
    set type(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'type', value);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])(tagName, HTMLLinkElement);
;
}}),
"[project]/node_modules/linkedom/esm/html/slot-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLSlotElement": (()=>HTMLSlotElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
;
;
const tagName = 'slot';
/**
 * @implements globalThis.HTMLSlotElement
 */ class HTMLSlotElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = tagName){
        super(ownerDocument, localName);
    }
    /* c8 ignore start */ get name() {
        return this.getAttribute('name');
    }
    set name(value) {
        this.setAttribute('name', value);
    }
    assign() {}
    assignedNodes(options) {
        const isNamedSlot = !!this.name;
        const hostChildNodes = this.getRootNode().host?.childNodes ?? [];
        let slottables;
        if (isNamedSlot) {
            slottables = [
                ...hostChildNodes
            ].filter((node)=>node.slot === this.name);
        } else {
            slottables = [
                ...hostChildNodes
            ].filter((node)=>!node.slot);
        }
        if (options?.flatten) {
            const result = [];
            // Element and Text nodes are slottables. A slot can be a slottable.
            for (let slottable of slottables){
                if (slottable.localName === 'slot') {
                    result.push(...slottable.assignedNodes({
                        flatten: true
                    }));
                } else {
                    result.push(slottable);
                }
            }
            slottables = result;
        }
        // If no assigned nodes are found, it returns the slot's fallback content.
        return slottables.length ? slottables : [
            ...this.childNodes
        ];
    }
    assignedElements(options) {
        const slottables = this.assignedNodes(options).filter((n)=>n.nodeType === 1);
        // If no assigned elements are found, it returns the slot's fallback content.
        return slottables.length ? slottables : [
            ...this.children
        ];
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])(tagName, HTMLSlotElement);
;
}}),
"[project]/node_modules/linkedom/esm/html/form-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLFormElement": (()=>HTMLFormElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLFormElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'form'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/image-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLImageElement": (()=>HTMLImageElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
;
;
const tagName = 'img';
/**
 * @implements globalThis.HTMLImageElement
 */ class HTMLImageElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = tagName){
        super(ownerDocument, localName);
    }
    /* c8 ignore start */ get alt() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'alt');
    }
    set alt(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'alt', value);
    }
    get sizes() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'sizes');
    }
    set sizes(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'sizes', value);
    }
    get src() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'src');
    }
    set src(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'src', value);
    }
    get srcset() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'srcset');
    }
    set srcset(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'srcset', value);
    }
    get title() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'title');
    }
    set title(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'title', value);
    }
    get width() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["numericAttribute"].get(this, 'width');
    }
    set width(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["numericAttribute"].set(this, 'width', value);
    }
    get height() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["numericAttribute"].get(this, 'height');
    }
    set height(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["numericAttribute"].set(this, 'height', value);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])(tagName, HTMLImageElement);
;
}}),
"[project]/node_modules/linkedom/esm/html/pre-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLPreElement": (()=>HTMLPreElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLPreElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'pre'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/u-list-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLUListElement": (()=>HTMLUListElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLUListElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'ul'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/meta-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLMetaElement": (()=>HTMLMetaElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)");
;
;
;
const tagName = 'meta';
class HTMLMetaElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = tagName){
        super(ownerDocument, localName);
    }
    /* c8 ignore start */ get name() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'name');
    }
    set name(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'name', value);
    }
    get httpEquiv() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'http-equiv');
    }
    set httpEquiv(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'http-equiv', value);
    }
    get content() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'content');
    }
    set content(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'content', value);
    }
    get charset() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'charset');
    }
    set charset(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'charset', value);
    }
    get media() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'media');
    }
    set media(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'media', value);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])(tagName, HTMLMetaElement);
}}),
"[project]/node_modules/linkedom/esm/html/picture-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLPictureElement": (()=>HTMLPictureElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLPictureElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'picture'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/area-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLAreaElement": (()=>HTMLAreaElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLAreaElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'area'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/o-list-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLOListElement": (()=>HTMLOListElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLOListElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'ol'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/table-caption-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLTableCaptionElement": (()=>HTMLTableCaptionElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLTableCaptionElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'caption'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/anchor-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLAnchorElement": (()=>HTMLAnchorElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
;
;
const tagName = 'a';
/**
 * @implements globalThis.HTMLAnchorElement
 */ class HTMLAnchorElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = tagName){
        super(ownerDocument, localName);
    }
    /* c8 ignore start */ // copy paste from img.src, already covered
    get href() {
        return encodeURI(decodeURI(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'href'))).trim();
    }
    set href(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'href', decodeURI(value));
    }
    get download() {
        return encodeURI(decodeURI(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'download')));
    }
    set download(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'download', decodeURI(value));
    }
    get target() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'target');
    }
    set target(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'target', value);
    }
    get type() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'type');
    }
    set type(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'type', value);
    }
    get rel() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'rel');
    }
    set rel(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'rel', value);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])(tagName, HTMLAnchorElement);
;
}}),
"[project]/node_modules/linkedom/esm/html/label-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLLabelElement": (()=>HTMLLabelElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLLabelElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'label'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/unknown-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLUnknownElement": (()=>HTMLUnknownElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLUnknownElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'unknown'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/mod-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLModElement": (()=>HTMLModElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLModElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'mod'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/details-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLDetailsElement": (()=>HTMLDetailsElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLDetailsElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'details'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/source-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLSourceElement": (()=>HTMLSourceElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/attributes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
;
;
const tagName = 'source';
/**
 * @implements globalThis.HTMLSourceElement
 */ class HTMLSourceElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = tagName){
        super(ownerDocument, localName);
    }
    /* c8 ignore start */ get src() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'src');
    }
    set src(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'src', value);
    }
    get srcset() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'srcset');
    }
    set srcset(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'srcset', value);
    }
    get sizes() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'sizes');
    }
    set sizes(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'sizes', value);
    }
    get type() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].get(this, 'type');
    }
    set type(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$attributes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringAttribute"].set(this, 'type', value);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerHTMLClass"])(tagName, HTMLSourceElement);
;
}}),
"[project]/node_modules/linkedom/esm/html/track-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLTrackElement": (()=>HTMLTrackElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLTrackElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'track'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/html/marquee-element.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLMarqueeElement": (()=>HTMLMarqueeElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
class HTMLMarqueeElement extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"] {
    constructor(ownerDocument, localName = 'marquee'){
        super(ownerDocument, localName);
    }
}
}}),
"[project]/node_modules/linkedom/esm/shared/html-classes.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLClasses": (()=>HTMLClasses)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$template$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/template-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$html$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/html-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$script$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/script-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$frame$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/frame-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$i$2d$frame$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/i-frame-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$object$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/object-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$head$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/head-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$body$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/body-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$style$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/style-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$time$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/time-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$field$2d$set$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/field-set-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$embed$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/embed-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$hr$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/hr-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$progress$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/progress-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$paragraph$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/paragraph-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$table$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/table-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$frame$2d$set$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/frame-set-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$li$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/li-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$base$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/base-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$data$2d$list$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/data-list-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$input$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/input-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$param$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/param-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$media$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/media-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$audio$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/audio-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$heading$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/heading-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$directory$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/directory-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$quote$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/quote-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$canvas$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/canvas-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$legend$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/legend-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$option$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/option-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$span$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/span-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$meter$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/meter-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$video$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/video-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$table$2d$cell$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/table-cell-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$title$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/title-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$output$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/output-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$table$2d$row$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/table-row-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$data$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/data-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$menu$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/menu-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$select$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/select-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$br$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/br-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$button$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/button-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$map$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/map-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$opt$2d$group$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/opt-group-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$d$2d$list$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/d-list-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$text$2d$area$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/text-area-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$font$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/font-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$div$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/div-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$link$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/link-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$slot$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/slot-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$form$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/form-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$image$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/image-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$pre$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/pre-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$u$2d$list$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/u-list-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$meta$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/meta-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$picture$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/picture-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$area$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/area-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$o$2d$list$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/o-list-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$table$2d$caption$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/table-caption-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$anchor$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/anchor-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$label$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/label-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$unknown$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/unknown-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$mod$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/mod-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$details$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/details-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$source$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/source-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$track$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/track-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$marquee$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/marquee-element.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const HTMLClasses = {
    HTMLElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"],
    HTMLTemplateElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$template$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLTemplateElement"],
    HTMLHtmlElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$html$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLHtmlElement"],
    HTMLScriptElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$script$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLScriptElement"],
    HTMLFrameElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$frame$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLFrameElement"],
    HTMLIFrameElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$i$2d$frame$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLIFrameElement"],
    HTMLObjectElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$object$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLObjectElement"],
    HTMLHeadElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$head$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLHeadElement"],
    HTMLBodyElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$body$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLBodyElement"],
    HTMLStyleElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$style$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLStyleElement"],
    HTMLTimeElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$time$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLTimeElement"],
    HTMLFieldSetElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$field$2d$set$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLFieldSetElement"],
    HTMLEmbedElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$embed$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLEmbedElement"],
    HTMLHRElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$hr$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLHRElement"],
    HTMLProgressElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$progress$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLProgressElement"],
    HTMLParagraphElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$paragraph$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLParagraphElement"],
    HTMLTableElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$table$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLTableElement"],
    HTMLFrameSetElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$frame$2d$set$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLFrameSetElement"],
    HTMLLIElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$li$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLLIElement"],
    HTMLBaseElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$base$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLBaseElement"],
    HTMLDataListElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$data$2d$list$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLDataListElement"],
    HTMLInputElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$input$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLInputElement"],
    HTMLParamElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$param$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLParamElement"],
    HTMLMediaElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$media$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLMediaElement"],
    HTMLAudioElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$audio$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLAudioElement"],
    HTMLHeadingElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$heading$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLHeadingElement"],
    HTMLDirectoryElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$directory$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLDirectoryElement"],
    HTMLQuoteElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$quote$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLQuoteElement"],
    HTMLCanvasElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$canvas$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLCanvasElement"],
    HTMLLegendElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$legend$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLLegendElement"],
    HTMLOptionElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$option$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLOptionElement"],
    HTMLSpanElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$span$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLSpanElement"],
    HTMLMeterElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$meter$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLMeterElement"],
    HTMLVideoElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$video$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLVideoElement"],
    HTMLTableCellElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$table$2d$cell$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLTableCellElement"],
    HTMLTitleElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$title$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLTitleElement"],
    HTMLOutputElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$output$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLOutputElement"],
    HTMLTableRowElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$table$2d$row$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLTableRowElement"],
    HTMLDataElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$data$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLDataElement"],
    HTMLMenuElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$menu$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLMenuElement"],
    HTMLSelectElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$select$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLSelectElement"],
    HTMLBRElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$br$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLBRElement"],
    HTMLButtonElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$button$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLButtonElement"],
    HTMLMapElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$map$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLMapElement"],
    HTMLOptGroupElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$opt$2d$group$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLOptGroupElement"],
    HTMLDListElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$d$2d$list$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLDListElement"],
    HTMLTextAreaElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$text$2d$area$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLTextAreaElement"],
    HTMLFontElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$font$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLFontElement"],
    HTMLDivElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$div$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLDivElement"],
    HTMLLinkElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$link$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLLinkElement"],
    HTMLSlotElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$slot$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLSlotElement"],
    HTMLFormElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$form$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLFormElement"],
    HTMLImageElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$image$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLImageElement"],
    HTMLPreElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$pre$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLPreElement"],
    HTMLUListElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$u$2d$list$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLUListElement"],
    HTMLMetaElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$meta$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLMetaElement"],
    HTMLPictureElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$picture$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLPictureElement"],
    HTMLAreaElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$area$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLAreaElement"],
    HTMLOListElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$o$2d$list$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLOListElement"],
    HTMLTableCaptionElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$table$2d$caption$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLTableCaptionElement"],
    HTMLAnchorElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$anchor$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLAnchorElement"],
    HTMLLabelElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$label$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLLabelElement"],
    HTMLUnknownElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$unknown$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLUnknownElement"],
    HTMLModElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$mod$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLModElement"],
    HTMLDetailsElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$details$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLDetailsElement"],
    HTMLSourceElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$source$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLSourceElement"],
    HTMLTrackElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$track$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLTrackElement"],
    HTMLMarqueeElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$marquee$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLMarqueeElement"]
};
}}),
"[project]/node_modules/linkedom/esm/shared/html-classes.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$template$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/template-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$html$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/html-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$script$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/script-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$frame$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/frame-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$i$2d$frame$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/i-frame-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$object$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/object-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$head$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/head-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$body$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/body-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$style$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/style-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$time$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/time-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$field$2d$set$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/field-set-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$embed$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/embed-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$hr$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/hr-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$progress$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/progress-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$paragraph$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/paragraph-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$table$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/table-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$frame$2d$set$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/frame-set-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$li$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/li-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$base$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/base-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$data$2d$list$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/data-list-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$input$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/input-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$param$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/param-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$media$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/media-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$audio$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/audio-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$heading$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/heading-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$directory$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/directory-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$quote$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/quote-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$canvas$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/canvas-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$legend$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/legend-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$option$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/option-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$span$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/span-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$meter$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/meter-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$video$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/video-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$table$2d$cell$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/table-cell-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$title$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/title-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$output$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/output-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$table$2d$row$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/table-row-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$data$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/data-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$menu$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/menu-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$select$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/select-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$br$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/br-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$button$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/button-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$map$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/map-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$opt$2d$group$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/opt-group-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$d$2d$list$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/d-list-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$text$2d$area$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/text-area-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$font$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/font-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$div$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/div-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$link$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/link-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$slot$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/slot-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$form$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/form-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$image$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/image-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$pre$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/pre-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$u$2d$list$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/u-list-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$meta$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/meta-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$picture$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/picture-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$area$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/area-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$o$2d$list$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/o-list-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$table$2d$caption$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/table-caption-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$anchor$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/anchor-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$label$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/label-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$unknown$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/unknown-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$mod$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/mod-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$details$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/details-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$source$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/source-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$track$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/track-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$marquee$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/marquee-element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$html$2d$classes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/html-classes.js [app-route] (ecmascript) <locals>");
}}),
"[project]/node_modules/linkedom/esm/shared/mime.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// TODO: ensure all these are text only
// /^(?:plaintext|script|style|textarea|title|xmp)$/i
__turbopack_context__.s({
    "Mime": (()=>Mime)
});
const voidElements = {
    test: ()=>true
};
const Mime = {
    'text/html': {
        docType: '<!DOCTYPE html>',
        ignoreCase: true,
        voidElements: /^(?:area|base|br|col|embed|hr|img|input|keygen|link|menuitem|meta|param|source|track|wbr)$/i
    },
    'image/svg+xml': {
        docType: '<?xml version="1.0" encoding="utf-8"?>',
        ignoreCase: false,
        voidElements
    },
    'text/xml': {
        docType: '<?xml version="1.0" encoding="utf-8"?>',
        ignoreCase: false,
        voidElements
    },
    'application/xml': {
        docType: '<?xml version="1.0" encoding="utf-8"?>',
        ignoreCase: false,
        voidElements
    },
    'application/xhtml+xml': {
        docType: '<?xml version="1.0" encoding="utf-8"?>',
        ignoreCase: false,
        voidElements
    }
};
}}),
"[project]/node_modules/linkedom/esm/interface/custom-event.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// https://dom.spec.whatwg.org/#interface-customevent
/* c8 ignore start */ // One day Node might have CustomEvent too
__turbopack_context__.s({
    "CustomEvent": (()=>CustomEvent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/event.js [app-route] (ecmascript)");
;
class CustomEvent extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Event"] {
    constructor(type, eventInitDict = {}){
        super(type, eventInitDict);
        this.detail = eventInitDict.detail;
    }
} /* c8 ignore stop */ 
}}),
"[project]/node_modules/linkedom/esm/interface/input-event.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// https://dom.spec.whatwg.org/#interface-customevent
/* c8 ignore start */ // One day Node might have CustomEvent too
__turbopack_context__.s({
    "InputEvent": (()=>InputEvent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/event.js [app-route] (ecmascript)");
;
class InputEvent extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Event"] {
    constructor(type, inputEventInit = {}){
        super(type, inputEventInit);
        this.inputType = inputEventInit.inputType;
        this.data = inputEventInit.data;
        this.dataTransfer = inputEventInit.dataTransfer;
        this.isComposing = inputEventInit.isComposing || false;
        this.ranges = inputEventInit.ranges;
    }
} /* c8 ignore stop */ 
}}),
"[project]/node_modules/linkedom/esm/interface/image.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ImageClass": (()=>ImageClass)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$image$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/image-element.js [app-route] (ecmascript)");
;
const ImageClass = (ownerDocument)=>/**
 * @implements globalThis.Image
 */ class Image extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$image$2d$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLImageElement"] {
        constructor(width, height){
            super(ownerDocument);
            switch(arguments.length){
                case 1:
                    this.height = width;
                    this.width = width;
                    break;
                case 2:
                    this.height = height;
                    this.width = width;
                    break;
            }
        }
    };
}}),
"[project]/node_modules/linkedom/esm/interface/range.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// https://dom.spec.whatwg.org/#concept-live-range
__turbopack_context__.s({
    "Range": (()=>Range)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$svg$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/svg/element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/utils.js [app-route] (ecmascript)");
;
;
;
const deleteContents = ({ [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["START"]]: start, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end }, fragment = null)=>{
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setAdjacent"])(start[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]], end[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]);
    do {
        const after = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnd"])(start);
        const next = after === end ? after : after[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        if (fragment) fragment.insertBefore(start, fragment[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]);
        else start.remove();
        start = next;
    }while (start !== end)
};
class Range {
    constructor(){
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["START"]] = null;
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]] = null;
        this.commonAncestorContainer = null;
    }
    /* TODO: this is more complicated than it looks
  setStart(node, offset) {
    this[START] = node.childNodes[offset];
  }

  setEnd(node, offset) {
    this[END] = getEnd(node.childNodes[offset]);
  }
  //*/ insertNode(newNode) {
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]].parentNode.insertBefore(newNode, this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["START"]]);
    }
    selectNode(node) {
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["START"]] = node;
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnd"])(node);
    }
    // TODO: SVG elements should then create contextual fragments
    //       that return SVG nodes
    selectNodeContents(node) {
        this.selectNode(node);
        this.commonAncestorContainer = node;
    }
    surroundContents(parentNode) {
        parentNode.replaceChildren(this.extractContents());
    }
    setStartBefore(node) {
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["START"]] = node;
    }
    setStartAfter(node) {
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["START"]] = node.nextSibling;
    }
    setEndBefore(node) {
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnd"])(node.previousSibling);
    }
    setEndAfter(node) {
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnd"])(node);
    }
    cloneContents() {
        let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["START"]]: start, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = this;
        const fragment = start.ownerDocument.createDocumentFragment();
        while(start !== end){
            fragment.insertBefore(start.cloneNode(true), fragment[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]);
            start = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnd"])(start);
            if (start !== end) start = start[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        }
        return fragment;
    }
    deleteContents() {
        deleteContents(this);
    }
    extractContents() {
        const fragment = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["START"]].ownerDocument.createDocumentFragment();
        deleteContents(this, fragment);
        return fragment;
    }
    createContextualFragment(html) {
        const { commonAncestorContainer: doc } = this;
        const isSVG = 'ownerSVGElement' in doc;
        const document = isSVG ? doc.ownerDocument : doc;
        let content = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["htmlToFragment"])(document, html);
        if (isSVG) {
            const childNodes = [
                ...content.childNodes
            ];
            content = document.createDocumentFragment();
            Object.setPrototypeOf(content, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$svg$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SVGElement"].prototype);
            content.ownerSVGElement = document;
            for (const child of childNodes){
                Object.setPrototypeOf(child, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$svg$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SVGElement"].prototype);
                child.ownerSVGElement = document;
                content.appendChild(child);
            }
        } else this.selectNode(content);
        return content;
    }
    cloneRange() {
        const range = new Range;
        range[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["START"]] = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["START"]];
        range[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]] = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]];
        return range;
    }
}
}}),
"[project]/node_modules/linkedom/esm/interface/tree-walker.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TreeWalker": (()=>TreeWalker)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
;
;
const isOK = ({ nodeType }, mask)=>{
    switch(nodeType){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]:
            return mask & __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SHOW_ELEMENT"];
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TEXT_NODE"]:
            return mask & __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SHOW_TEXT"];
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["COMMENT_NODE"]:
            return mask & __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SHOW_COMMENT"];
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CDATA_SECTION_NODE"]:
            return mask & __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SHOW_CDATA_SECTION"];
    }
    return 0;
};
class TreeWalker {
    constructor(root, whatToShow = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SHOW_ALL"]){
        this.root = root;
        this.currentNode = root;
        this.whatToShow = whatToShow;
        let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = root;
        if (root.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_NODE"]) {
            const { documentElement } = root;
            next = documentElement;
            end = documentElement[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]];
        }
        const nodes = [];
        while(next && next !== end){
            if (isOK(next, whatToShow)) nodes.push(next);
            next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        }
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRIVATE"]] = {
            i: 0,
            nodes
        };
    }
    nextNode() {
        const $ = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRIVATE"]];
        this.currentNode = $.i < $.nodes.length ? $.nodes[$.i++] : null;
        return this.currentNode;
    }
}
}}),
"[project]/node_modules/linkedom/esm/interface/document.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Document": (()=>Document)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$commonjs$2f$perf_hooks$2e$cjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/commonjs/perf_hooks.cjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$facades$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/facades.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$html$2d$classes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/html-classes.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$html$2d$classes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/html-classes.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$mime$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/mime.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/utils.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/object.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$non$2d$element$2d$parent$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/mixin/non-element-parent-node.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$svg$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/svg/element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$attr$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/attr.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$cdata$2d$section$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/cdata-section.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$comment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/comment.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/custom-element-registry.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/custom-event.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2d$fragment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/document-fragment.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2d$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/document-type.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/event.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2d$target$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/event-target.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$input$2d$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/input-event.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$image$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/image.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$mutation$2d$observer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/mutation-observer.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$named$2d$node$2d$map$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/named-node-map.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/node-list.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$range$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/range.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/text.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$tree$2d$walker$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/tree-walker.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const query = (method, ownerDocument, selectors)=>{
    let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = ownerDocument;
    return method.call({
        ownerDocument,
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next,
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end
    }, selectors);
};
const globalExports = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assign"])({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$facades$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Facades"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$html$2d$classes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["HTMLClasses"], {
    CustomEvent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CustomEvent"],
    Event: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Event"],
    EventTarget: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2d$target$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventTarget"],
    InputEvent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$input$2d$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["InputEvent"],
    NamedNodeMap: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$named$2d$node$2d$map$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NamedNodeMap"],
    NodeList: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NodeList"]
});
const window = new WeakMap;
class Document extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$mixin$2f$non$2d$element$2d$parent$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NonElementParentNode"] {
    constructor(type){
        super(null, '#document', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_NODE"]);
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CUSTOM_ELEMENTS"]] = {
            active: false,
            registry: null
        };
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MUTATION_OBSERVER"]] = {
            active: false,
            class: null
        };
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MIME"]] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$mime$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Mime"][type];
        /** @type {DocumentType} */ this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCTYPE"]] = null;
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOM_PARSER"]] = null;
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GLOBALS"]] = null;
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IMAGE"]] = null;
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UPGRADE"]] = null;
    }
    /**
   * @type {globalThis.Document['defaultView']}
   */ get defaultView() {
        if (!window.has(this)) window.set(this, new Proxy(globalThis, {
            set: (target, name, value)=>{
                switch(name){
                    case 'addEventListener':
                    case 'removeEventListener':
                    case 'dispatchEvent':
                        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EVENT_TARGET"]][name] = value;
                        break;
                    default:
                        target[name] = value;
                        break;
                }
                return true;
            },
            get: (globalThis1, name)=>{
                switch(name){
                    case 'addEventListener':
                    case 'removeEventListener':
                    case 'dispatchEvent':
                        if (!this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EVENT_TARGET"]]) {
                            const et = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EVENT_TARGET"]] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2d$target$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventTarget"];
                            et.dispatchEvent = et.dispatchEvent.bind(et);
                            et.addEventListener = et.addEventListener.bind(et);
                            et.removeEventListener = et.removeEventListener.bind(et);
                        }
                        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EVENT_TARGET"]][name];
                    case 'document':
                        return this;
                    /* c8 ignore start */ case 'navigator':
                        return {
                            userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.150 Safari/537.36'
                        };
                    /* c8 ignore stop */ case 'window':
                        return window.get(this);
                    case 'customElements':
                        if (!this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CUSTOM_ELEMENTS"]].registry) this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CUSTOM_ELEMENTS"]] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CustomElementRegistry"](this);
                        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CUSTOM_ELEMENTS"]];
                    case 'performance':
                        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$commonjs$2f$perf_hooks$2e$cjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["performance"];
                    case 'DOMParser':
                        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOM_PARSER"]];
                    case 'Image':
                        if (!this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IMAGE"]]) this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IMAGE"]] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$image$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ImageClass"])(this);
                        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IMAGE"]];
                    case 'MutationObserver':
                        if (!this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MUTATION_OBSERVER"]].class) this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MUTATION_OBSERVER"]] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$mutation$2d$observer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MutationObserverClass"](this);
                        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MUTATION_OBSERVER"]].class;
                }
                return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GLOBALS"]] && this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GLOBALS"]][name] || globalExports[name] || globalThis1[name];
            }
        }));
        return window.get(this);
    }
    get doctype() {
        const docType = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCTYPE"]];
        if (docType) return docType;
        const { firstChild } = this;
        if (firstChild && firstChild.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_TYPE_NODE"]) return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCTYPE"]] = firstChild;
        return null;
    }
    set doctype(value) {
        if (/^([a-z:]+)(\s+system|\s+public(\s+"([^"]+)")?)?(\s+"([^"]+)")?/i.test(value)) {
            const { $1: name, $4: publicId, $6: systemId } = RegExp;
            this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCTYPE"]] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2d$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DocumentType"](this, name, publicId, systemId);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["knownSiblings"])(this, this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCTYPE"]], this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]);
        }
    }
    get documentElement() {
        return this.firstElementChild;
    }
    get isConnected() {
        return true;
    }
    /**
   * @protected
   */ _getParent() {
        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EVENT_TARGET"]];
    }
    createAttribute(name) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$attr$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Attr"](this, name);
    }
    createCDATASection(data) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$cdata$2d$section$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CDATASection"](this, data);
    }
    createComment(textContent) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$comment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Comment"](this, textContent);
    }
    createDocumentFragment() {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2d$fragment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DocumentFragment"](this);
    }
    createDocumentType(name, publicId, systemId) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2d$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DocumentType"](this, name, publicId, systemId);
    }
    createElement(localName) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Element"](this, localName);
    }
    createRange() {
        const range = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$range$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Range"];
        range.commonAncestorContainer = this;
        return range;
    }
    createTextNode(textContent) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Text"](this, textContent);
    }
    createTreeWalker(root, whatToShow = -1) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$tree$2d$walker$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TreeWalker"](root, whatToShow);
    }
    createNodeIterator(root, whatToShow = -1) {
        return this.createTreeWalker(root, whatToShow);
    }
    createEvent(name) {
        const event = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["create"])(name === 'Event' ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Event"]('') : new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CustomEvent"](''));
        event.initEvent = event.initCustomEvent = (type, canBubble = false, cancelable = false, detail)=>{
            event.bubbles = !!canBubble;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["defineProperties"])(event, {
                type: {
                    value: type
                },
                canBubble: {
                    value: canBubble
                },
                cancelable: {
                    value: cancelable
                },
                detail: {
                    value: detail
                }
            });
        };
        return event;
    }
    cloneNode(deep = false) {
        const { constructor, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CUSTOM_ELEMENTS"]]: customElements, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCTYPE"]]: doctype } = this;
        const document = new constructor();
        document[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CUSTOM_ELEMENTS"]] = customElements;
        if (deep) {
            const end = document[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]];
            const { childNodes } = this;
            for(let { length } = childNodes, i = 0; i < length; i++)document.insertBefore(childNodes[i].cloneNode(true), end);
            if (doctype) document[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCTYPE"]] = childNodes[0];
        }
        return document;
    }
    importNode(externalNode) {
        // important: keep the signature length as *one*
        // or it would behave like old IE or Edge with polyfills
        const deep = 1 < arguments.length && !!arguments[1];
        const node = externalNode.cloneNode(deep);
        const { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CUSTOM_ELEMENTS"]]: customElements } = this;
        const { active } = customElements;
        const upgrade = (element)=>{
            const { ownerDocument, nodeType } = element;
            element.ownerDocument = this;
            if (active && ownerDocument !== this && nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]) customElements.upgrade(element);
        };
        upgrade(node);
        if (deep) {
            switch(node.nodeType){
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]:
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_FRAGMENT_NODE"]:
                    {
                        let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = node;
                        while(next !== end){
                            if (next.nodeType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]) upgrade(next);
                            next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
                        }
                        break;
                    }
            }
        }
        return node;
    }
    toString() {
        return this.childNodes.join('');
    }
    querySelector(selectors) {
        return query(super.querySelector, this, selectors);
    }
    querySelectorAll(selectors) {
        return query(super.querySelectorAll, this, selectors);
    }
    /* c8 ignore start */ getElementsByTagNameNS(_, name) {
        return this.getElementsByTagName(name);
    }
    createAttributeNS(_, name) {
        return this.createAttribute(name);
    }
    createElementNS(nsp, localName, options) {
        return nsp === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SVG_NAMESPACE"] ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$svg$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SVGElement"](this, localName, null) : this.createElement(localName, options);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setPrototypeOf"])(globalExports.Document = function Document() {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$facades$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["illegalConstructor"])();
}, Document).prototype = Document.prototype;
}}),
"[project]/node_modules/linkedom/esm/html/document.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HTMLDocument": (()=>HTMLDocument)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/document.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/node-list.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/custom-element-registry.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
const createHTMLElement = (ownerDocument, builtin, localName, options)=>{
    if (!builtin && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["htmlClasses"].has(localName)) {
        const Class = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["htmlClasses"].get(localName);
        return new Class(ownerDocument, localName);
    }
    const { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CUSTOM_ELEMENTS"]]: { active, registry } } = ownerDocument;
    if (active) {
        const ce = builtin ? options.is : localName;
        if (registry.has(ce)) {
            const { Class } = registry.get(ce);
            const element = new Class(ownerDocument, localName);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$element$2d$registry$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customElements"].set(element, {
                connected: false
            });
            return element;
        }
    }
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"](ownerDocument, localName);
};
class HTMLDocument extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Document"] {
    constructor(){
        super('text/html');
    }
    get all() {
        const nodeList = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NodeList"];
        let { [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]]: next, [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]]: end } = this;
        while(next !== end){
            switch(next.nodeType){
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]:
                    nodeList.push(next);
                    break;
            }
            next = next[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NEXT"]];
        }
        return nodeList;
    }
    /**
   * @type HTMLHeadElement
   */ get head() {
        const { documentElement } = this;
        let { firstElementChild } = documentElement;
        if (!firstElementChild || firstElementChild.tagName !== 'HEAD') {
            firstElementChild = this.createElement('head');
            documentElement.prepend(firstElementChild);
        }
        return firstElementChild;
    }
    /**
   * @type HTMLBodyElement
   */ get body() {
        const { head } = this;
        let { nextElementSibling } = head;
        if (!nextElementSibling || nextElementSibling.tagName !== 'BODY') {
            nextElementSibling = this.createElement('body');
            head.after(nextElementSibling);
        }
        return nextElementSibling;
    }
    /**
   * @type HTMLTitleElement
   */ get title() {
        const { head } = this;
        return head.getElementsByTagName('title').at(0)?.textContent || '';
    }
    set title(textContent) {
        const { head } = this;
        let title = head.getElementsByTagName('title').at(0);
        if (title) title.textContent = textContent;
        else {
            head.insertBefore(this.createElement('title'), head.firstChild).textContent = textContent;
        }
    }
    createElement(localName, options) {
        const builtin = !!(options && options.is);
        const element = createHTMLElement(this, builtin, localName, options);
        if (builtin) element.setAttribute('is', options.is);
        return element;
    }
}
}}),
"[project]/node_modules/linkedom/esm/svg/document.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SVGDocument": (()=>SVGDocument)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/document.js [app-route] (ecmascript)");
;
;
class SVGDocument extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Document"] {
    constructor(){
        super('image/svg+xml');
    }
    toString() {
        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MIME"]].docType + super.toString();
    }
}
}}),
"[project]/node_modules/linkedom/esm/xml/document.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "XMLDocument": (()=>XMLDocument)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/document.js [app-route] (ecmascript)");
;
;
class XMLDocument extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Document"] {
    constructor(){
        super('text/xml');
    }
    toString() {
        return this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MIME"]].docType + super.toString();
    }
}
}}),
"[project]/node_modules/linkedom/esm/dom/parser.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DOMParser": (()=>DOMParser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$parse$2d$from$2d$string$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/parse-from-string.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/document.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$svg$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/svg/document.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$xml$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/xml/document.js [app-route] (ecmascript)");
;
;
;
;
;
class DOMParser {
    /** @typedef {{ "text/html": HTMLDocument, "image/svg+xml": SVGDocument, "text/xml": XMLDocument }} MimeToDoc */ /**
   * @template {keyof MimeToDoc} MIME
   * @param {string} markupLanguage
   * @param {MIME} mimeType
   * @returns {MimeToDoc[MIME]}
   */ parseFromString(markupLanguage, mimeType, globals = null) {
        let isHTML = false, document;
        if (mimeType === 'text/html') {
            isHTML = true;
            document = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLDocument"];
        } else if (mimeType === 'image/svg+xml') document = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$svg$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SVGDocument"];
        else document = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$xml$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["XMLDocument"];
        document[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOM_PARSER"]] = DOMParser;
        if (globals) document[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GLOBALS"]] = globals;
        if (isHTML && markupLanguage === '...') markupLanguage = '<!doctype html><html><head></head><body></body></html>';
        return markupLanguage ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$parse$2d$from$2d$string$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseFromString"])(document, isHTML, markupLanguage) : document;
    }
}
}}),
"[project]/node_modules/linkedom/esm/shared/parse-json.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "parseJSON": (()=>parseJSON),
    "toJSON": (()=>toJSON)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/symbols.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/register-html-class.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/utils.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$attr$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/attr.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$cdata$2d$section$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/cdata-section.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$comment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/comment.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2d$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/document-type.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/text.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/document.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/html/element.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$svg$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/svg/element.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
const { parse } = JSON;
const append = (parentNode, node, end)=>{
    node.parentNode = parentNode;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["knownSiblings"])(end[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]], node, end);
};
const createHTMLElement = (ownerDocument, localName)=>{
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["htmlClasses"].has(localName)) {
        const Class = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$register$2d$html$2d$class$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["htmlClasses"].get(localName);
        return new Class(ownerDocument, localName);
    }
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLElement"](ownerDocument, localName);
};
const parseJSON = (value)=>{
    const array = typeof value === 'string' ? parse(value) : value;
    const { length } = array;
    const document = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$html$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTMLDocument"];
    let parentNode = document, end = parentNode[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]], svg = false, i = 0;
    while(i < length){
        let nodeType = array[i++];
        switch(nodeType){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]:
                {
                    const localName = array[i++];
                    const isSVG = svg || localName === 'svg' || localName === 'SVG';
                    const element = isSVG ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$svg$2f$element$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SVGElement"](document, localName, parentNode.ownerSVGElement || null) : createHTMLElement(document, localName);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["knownBoundaries"])(end[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]], element, end);
                    element.parentNode = parentNode;
                    parentNode = element;
                    end = parentNode[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]];
                    svg = isSVG;
                    break;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ATTRIBUTE_NODE"]:
                {
                    const name = array[i++];
                    const value = typeof array[i] === 'string' ? array[i++] : '';
                    const attr = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$attr$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Attr"](document, name, value);
                    attr.ownerElement = parentNode;
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["knownSiblings"])(end[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PREV"]], attr, end);
                    break;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TEXT_NODE"]:
                append(parentNode, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Text"](document, array[i++]), end);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["COMMENT_NODE"]:
                append(parentNode, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$comment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Comment"](document, array[i++]), end);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CDATA_SECTION_NODE"]:
                append(parentNode, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$cdata$2d$section$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CDATASection"](document, array[i++]), end);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_TYPE_NODE"]:
                {
                    const args = [
                        document
                    ];
                    while(typeof array[i] === 'string')args.push(array[i++]);
                    if (args.length === 3 && /\.dtd$/i.test(args[2])) args.splice(2, 0, '');
                    append(parentNode, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2d$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DocumentType"](...args), end);
                    break;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_FRAGMENT_NODE"]:
                parentNode = document.createDocumentFragment();
                end = parentNode[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]];
            /* eslint no-fallthrough:0 */ case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_NODE"]:
                break;
            default:
                do {
                    nodeType -= __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NODE_END"];
                    if (svg && !parentNode.ownerSVGElement) svg = false;
                    parentNode = parentNode.parentNode || parentNode;
                }while (nodeType < 0)
                end = parentNode[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$symbols$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["END"]];
                break;
        }
    }
    switch(i && array[0]){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ELEMENT_NODE"]:
            return document.firstElementChild;
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOCUMENT_FRAGMENT_NODE"]:
            return parentNode;
    }
    return document;
};
const toJSON = (node)=>node.toJSON();
}}),
"[project]/node_modules/linkedom/esm/interface/node-filter.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "NodeFilter": (()=>NodeFilter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/constants.js [app-route] (ecmascript)");
;
class NodeFilter {
    static get SHOW_ALL() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SHOW_ALL"];
    }
    static get SHOW_ELEMENT() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SHOW_ELEMENT"];
    }
    static get SHOW_COMMENT() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SHOW_COMMENT"];
    }
    static get SHOW_CDATA_SECTION() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SHOW_CDATA_SECTION"];
    }
    static get SHOW_TEXT() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SHOW_TEXT"];
    }
}
}}),
"[project]/node_modules/linkedom/esm/index.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Document": (()=>Document),
    "parseHTML": (()=>parseHTML)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$dom$2f$parser$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/dom/parser.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/document.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$facades$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/facades.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/object.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$parse$2d$json$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/parse-json.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$html$2d$classes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/html-classes.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/custom-event.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/event.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2d$target$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/event-target.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$input$2d$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/input-event.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/node-list.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$filter$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/node-filter.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const parseHTML = (html, globals = null)=>(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$dom$2f$parser$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DOMParser"]).parseFromString(html, 'text/html', globals).defaultView;
function Document() {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$facades$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["illegalConstructor"])();
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setPrototypeOf"])(Document, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Document"]).prototype = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Document"].prototype;
}}),
"[project]/node_modules/linkedom/esm/index.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$dom$2f$parser$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/dom/parser.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/document.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$facades$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/facades.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/object.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$parse$2d$json$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/parse-json.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$shared$2f$html$2d$classes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/shared/html-classes.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$custom$2d$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/custom-event.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/event.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$event$2d$target$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/event-target.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$input$2d$event$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/input-event.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$list$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/node-list.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$interface$2f$node$2d$filter$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/interface/node-filter.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$linkedom$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/linkedom/esm/index.js [app-route] (ecmascript) <locals>");
}}),

};

//# sourceMappingURL=node_modules_linkedom_4d9c72e2._.js.map