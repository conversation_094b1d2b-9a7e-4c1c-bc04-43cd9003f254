{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/%40mozilla/readability/Readability.js"], "sourcesContent": ["/*\n * Copyright (c) 2010 Arc90 Inc\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/*\n * This code is heavily based on Arc90's readability.js (1.7.1) script\n * available at: http://code.google.com/p/arc90labs-readability\n */\n\n/**\n * Public constructor.\n * @param {HTMLDocument} doc     The document to parse.\n * @param {Object}       options The options object.\n */\nfunction Readability(doc, options) {\n  // In some older versions, people passed a URI as the first argument. Cope:\n  if (options && options.documentElement) {\n    doc = options;\n    options = arguments[2];\n  } else if (!doc || !doc.documentElement) {\n    throw new Error(\n      \"First argument to Readability constructor should be a document object.\"\n    );\n  }\n  options = options || {};\n\n  this._doc = doc;\n  this._docJSDOMParser = this._doc.firstChild.__JSDOMParser__;\n  this._articleTitle = null;\n  this._articleByline = null;\n  this._articleDir = null;\n  this._articleSiteName = null;\n  this._attempts = [];\n  this._metadata = {};\n\n  // Configurable options\n  this._debug = !!options.debug;\n  this._maxElemsToParse =\n    options.maxElemsToParse || this.DEFAULT_MAX_ELEMS_TO_PARSE;\n  this._nbTopCandidates =\n    options.nbTopCandidates || this.DEFAULT_N_TOP_CANDIDATES;\n  this._charThreshold = options.charThreshold || this.DEFAULT_CHAR_THRESHOLD;\n  this._classesToPreserve = this.CLASSES_TO_PRESERVE.concat(\n    options.classesToPreserve || []\n  );\n  this._keepClasses = !!options.keepClasses;\n  this._serializer =\n    options.serializer ||\n    function (el) {\n      return el.innerHTML;\n    };\n  this._disableJSONLD = !!options.disableJSONLD;\n  this._allowedVideoRegex = options.allowedVideoRegex || this.REGEXPS.videos;\n  this._linkDensityModifier = options.linkDensityModifier || 0;\n\n  // Start with all flags set\n  this._flags =\n    this.FLAG_STRIP_UNLIKELYS |\n    this.FLAG_WEIGHT_CLASSES |\n    this.FLAG_CLEAN_CONDITIONALLY;\n\n  // Control whether log messages are sent to the console\n  if (this._debug) {\n    let logNode = function (node) {\n      if (node.nodeType == node.TEXT_NODE) {\n        return `${node.nodeName} (\"${node.textContent}\")`;\n      }\n      let attrPairs = Array.from(node.attributes || [], function (attr) {\n        return `${attr.name}=\"${attr.value}\"`;\n      }).join(\" \");\n      return `<${node.localName} ${attrPairs}>`;\n    };\n    this.log = function () {\n      if (typeof console !== \"undefined\") {\n        let args = Array.from(arguments, arg => {\n          if (arg && arg.nodeType == this.ELEMENT_NODE) {\n            return logNode(arg);\n          }\n          return arg;\n        });\n        args.unshift(\"Reader: (Readability)\");\n        // eslint-disable-next-line no-console\n        console.log(...args);\n      } else if (typeof dump !== \"undefined\") {\n        /* global dump */\n        var msg = Array.prototype.map\n          .call(arguments, function (x) {\n            return x && x.nodeName ? logNode(x) : x;\n          })\n          .join(\" \");\n        dump(\"Reader: (Readability) \" + msg + \"\\n\");\n      }\n    };\n  } else {\n    this.log = function () {};\n  }\n}\n\nReadability.prototype = {\n  FLAG_STRIP_UNLIKELYS: 0x1,\n  FLAG_WEIGHT_CLASSES: 0x2,\n  FLAG_CLEAN_CONDITIONALLY: 0x4,\n\n  // https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\n  ELEMENT_NODE: 1,\n  TEXT_NODE: 3,\n\n  // Max number of nodes supported by this parser. Default: 0 (no limit)\n  DEFAULT_MAX_ELEMS_TO_PARSE: 0,\n\n  // The number of top candidates to consider when analysing how\n  // tight the competition is among candidates.\n  DEFAULT_N_TOP_CANDIDATES: 5,\n\n  // Element tags to score by default.\n  DEFAULT_TAGS_TO_SCORE: \"section,h2,h3,h4,h5,h6,p,td,pre\"\n    .toUpperCase()\n    .split(\",\"),\n\n  // The default number of chars an article must have in order to return a result\n  DEFAULT_CHAR_THRESHOLD: 500,\n\n  // All of the regular expressions in use within readability.\n  // Defined up here so we don't instantiate them repeatedly in loops.\n  REGEXPS: {\n    // NOTE: These two regular expressions are duplicated in\n    // Readability-readerable.js. Please keep both copies in sync.\n    unlikelyCandidates:\n      /-ad-|ai2html|banner|breadcrumbs|combx|comment|community|cover-wrap|disqus|extra|footer|gdpr|header|legends|menu|related|remark|replies|rss|shoutbox|sidebar|skyscraper|social|sponsor|supplemental|ad-break|agegate|pagination|pager|popup|yom-remote/i,\n    okMaybeItsACandidate: /and|article|body|column|content|main|shadow/i,\n\n    positive:\n      /article|body|content|entry|hentry|h-entry|main|page|pagination|post|text|blog|story/i,\n    negative:\n      /-ad-|hidden|^hid$| hid$| hid |^hid |banner|combx|comment|com-|contact|footer|gdpr|masthead|media|meta|outbrain|promo|related|scroll|share|shoutbox|sidebar|skyscraper|sponsor|shopping|tags|widget/i,\n    extraneous:\n      /print|archive|comment|discuss|e[\\-]?mail|share|reply|all|login|sign|single|utility/i,\n    byline: /byline|author|dateline|writtenby|p-author/i,\n    replaceFonts: /<(\\/?)font[^>]*>/gi,\n    normalize: /\\s{2,}/g,\n    videos:\n      /\\/\\/(www\\.)?((dailymotion|youtube|youtube-nocookie|player\\.vimeo|v\\.qq)\\.com|(archive|upload\\.wikimedia)\\.org|player\\.twitch\\.tv)/i,\n    shareElements: /(\\b|_)(share|sharedaddy)(\\b|_)/i,\n    nextLink: /(next|weiter|continue|>([^\\|]|$)|»([^\\|]|$))/i,\n    prevLink: /(prev|earl|old|new|<|«)/i,\n    tokenize: /\\W+/g,\n    whitespace: /^\\s*$/,\n    hasContent: /\\S$/,\n    hashUrl: /^#.+/,\n    srcsetUrl: /(\\S+)(\\s+[\\d.]+[xw])?(\\s*(?:,|$))/g,\n    b64DataUrl: /^data:\\s*([^\\s;,]+)\\s*;\\s*base64\\s*,/i,\n    // Commas as used in Latin, Sindhi, Chinese and various other scripts.\n    // see: https://en.wikipedia.org/wiki/Comma#Comma_variants\n    commas: /\\u002C|\\u060C|\\uFE50|\\uFE10|\\uFE11|\\u2E41|\\u2E34|\\u2E32|\\uFF0C/g,\n    // See: https://schema.org/Article\n    jsonLdArticleTypes:\n      /^Article|AdvertiserContentArticle|NewsArticle|AnalysisNewsArticle|AskPublicNewsArticle|BackgroundNewsArticle|OpinionNewsArticle|ReportageNewsArticle|ReviewNewsArticle|Report|SatiricalArticle|ScholarlyArticle|MedicalScholarlyArticle|SocialMediaPosting|BlogPosting|LiveBlogPosting|DiscussionForumPosting|TechArticle|APIReference$/,\n    // used to see if a node's content matches words commonly used for ad blocks or loading indicators\n    adWords:\n      /^(ad(vertising|vertisement)?|pub(licité)?|werb(ung)?|广告|Реклама|Anuncio)$/iu,\n    loadingWords:\n      /^((loading|正在加载|Загрузка|chargement|cargando)(…|\\.\\.\\.)?)$/iu,\n  },\n\n  UNLIKELY_ROLES: [\n    \"menu\",\n    \"menubar\",\n    \"complementary\",\n    \"navigation\",\n    \"alert\",\n    \"alertdialog\",\n    \"dialog\",\n  ],\n\n  DIV_TO_P_ELEMS: new Set([\n    \"BLOCKQUOTE\",\n    \"DL\",\n    \"DIV\",\n    \"IMG\",\n    \"OL\",\n    \"P\",\n    \"PRE\",\n    \"TABLE\",\n    \"UL\",\n  ]),\n\n  ALTER_TO_DIV_EXCEPTIONS: [\"DIV\", \"ARTICLE\", \"SECTION\", \"P\", \"OL\", \"UL\"],\n\n  PRESENTATIONAL_ATTRIBUTES: [\n    \"align\",\n    \"background\",\n    \"bgcolor\",\n    \"border\",\n    \"cellpadding\",\n    \"cellspacing\",\n    \"frame\",\n    \"hspace\",\n    \"rules\",\n    \"style\",\n    \"valign\",\n    \"vspace\",\n  ],\n\n  DEPRECATED_SIZE_ATTRIBUTE_ELEMS: [\"TABLE\", \"TH\", \"TD\", \"HR\", \"PRE\"],\n\n  // The commented out elements qualify as phrasing content but tend to be\n  // removed by readability when put into paragraphs, so we ignore them here.\n  PHRASING_ELEMS: [\n    // \"CANVAS\", \"IFRAME\", \"SVG\", \"VIDEO\",\n    \"ABBR\",\n    \"AUDIO\",\n    \"B\",\n    \"BDO\",\n    \"BR\",\n    \"BUTTON\",\n    \"CITE\",\n    \"CODE\",\n    \"DATA\",\n    \"DATALIST\",\n    \"DFN\",\n    \"EM\",\n    \"EMBED\",\n    \"I\",\n    \"IMG\",\n    \"INPUT\",\n    \"KBD\",\n    \"LABEL\",\n    \"MARK\",\n    \"MATH\",\n    \"METER\",\n    \"NOSCRIPT\",\n    \"OBJECT\",\n    \"OUTPUT\",\n    \"PROGRESS\",\n    \"Q\",\n    \"RUBY\",\n    \"SAMP\",\n    \"SCRIPT\",\n    \"SELECT\",\n    \"SMALL\",\n    \"SPAN\",\n    \"STRONG\",\n    \"SUB\",\n    \"SUP\",\n    \"TEXTAREA\",\n    \"TIME\",\n    \"VAR\",\n    \"WBR\",\n  ],\n\n  // These are the classes that readability sets itself.\n  CLASSES_TO_PRESERVE: [\"page\"],\n\n  // These are the list of HTML entities that need to be escaped.\n  HTML_ESCAPE_MAP: {\n    lt: \"<\",\n    gt: \">\",\n    amp: \"&\",\n    quot: '\"',\n    apos: \"'\",\n  },\n\n  /**\n   * Run any post-process modifications to article content as necessary.\n   *\n   * @param Element\n   * @return void\n   **/\n  _postProcessContent(articleContent) {\n    // Readability cannot open relative uris so we convert them to absolute uris.\n    this._fixRelativeUris(articleContent);\n\n    this._simplifyNestedElements(articleContent);\n\n    if (!this._keepClasses) {\n      // Remove classes.\n      this._cleanClasses(articleContent);\n    }\n  },\n\n  /**\n   * Iterates over a NodeList, calls `filterFn` for each node and removes node\n   * if function returned `true`.\n   *\n   * If function is not passed, removes all the nodes in node list.\n   *\n   * @param NodeList nodeList The nodes to operate on\n   * @param Function filterFn the function to use as a filter\n   * @return void\n   */\n  _removeNodes(nodeList, filterFn) {\n    // Avoid ever operating on live node lists.\n    if (this._docJSDOMParser && nodeList._isLiveNodeList) {\n      throw new Error(\"Do not pass live node lists to _removeNodes\");\n    }\n    for (var i = nodeList.length - 1; i >= 0; i--) {\n      var node = nodeList[i];\n      var parentNode = node.parentNode;\n      if (parentNode) {\n        if (!filterFn || filterFn.call(this, node, i, nodeList)) {\n          parentNode.removeChild(node);\n        }\n      }\n    }\n  },\n\n  /**\n   * Iterates over a NodeList, and calls _setNodeTag for each node.\n   *\n   * @param NodeList nodeList The nodes to operate on\n   * @param String newTagName the new tag name to use\n   * @return void\n   */\n  _replaceNodeTags(nodeList, newTagName) {\n    // Avoid ever operating on live node lists.\n    if (this._docJSDOMParser && nodeList._isLiveNodeList) {\n      throw new Error(\"Do not pass live node lists to _replaceNodeTags\");\n    }\n    for (const node of nodeList) {\n      this._setNodeTag(node, newTagName);\n    }\n  },\n\n  /**\n   * Iterate over a NodeList, which doesn't natively fully implement the Array\n   * interface.\n   *\n   * For convenience, the current object context is applied to the provided\n   * iterate function.\n   *\n   * @param  NodeList nodeList The NodeList.\n   * @param  Function fn       The iterate function.\n   * @return void\n   */\n  _forEachNode(nodeList, fn) {\n    Array.prototype.forEach.call(nodeList, fn, this);\n  },\n\n  /**\n   * Iterate over a NodeList, and return the first node that passes\n   * the supplied test function\n   *\n   * For convenience, the current object context is applied to the provided\n   * test function.\n   *\n   * @param  NodeList nodeList The NodeList.\n   * @param  Function fn       The test function.\n   * @return void\n   */\n  _findNode(nodeList, fn) {\n    return Array.prototype.find.call(nodeList, fn, this);\n  },\n\n  /**\n   * Iterate over a NodeList, return true if any of the provided iterate\n   * function calls returns true, false otherwise.\n   *\n   * For convenience, the current object context is applied to the\n   * provided iterate function.\n   *\n   * @param  NodeList nodeList The NodeList.\n   * @param  Function fn       The iterate function.\n   * @return Boolean\n   */\n  _someNode(nodeList, fn) {\n    return Array.prototype.some.call(nodeList, fn, this);\n  },\n\n  /**\n   * Iterate over a NodeList, return true if all of the provided iterate\n   * function calls return true, false otherwise.\n   *\n   * For convenience, the current object context is applied to the\n   * provided iterate function.\n   *\n   * @param  NodeList nodeList The NodeList.\n   * @param  Function fn       The iterate function.\n   * @return Boolean\n   */\n  _everyNode(nodeList, fn) {\n    return Array.prototype.every.call(nodeList, fn, this);\n  },\n\n  _getAllNodesWithTag(node, tagNames) {\n    if (node.querySelectorAll) {\n      return node.querySelectorAll(tagNames.join(\",\"));\n    }\n    return [].concat.apply(\n      [],\n      tagNames.map(function (tag) {\n        var collection = node.getElementsByTagName(tag);\n        return Array.isArray(collection) ? collection : Array.from(collection);\n      })\n    );\n  },\n\n  /**\n   * Removes the class=\"\" attribute from every element in the given\n   * subtree, except those that match CLASSES_TO_PRESERVE and\n   * the classesToPreserve array from the options object.\n   *\n   * @param Element\n   * @return void\n   */\n  _cleanClasses(node) {\n    var classesToPreserve = this._classesToPreserve;\n    var className = (node.getAttribute(\"class\") || \"\")\n      .split(/\\s+/)\n      .filter(cls => classesToPreserve.includes(cls))\n      .join(\" \");\n\n    if (className) {\n      node.setAttribute(\"class\", className);\n    } else {\n      node.removeAttribute(\"class\");\n    }\n\n    for (node = node.firstElementChild; node; node = node.nextElementSibling) {\n      this._cleanClasses(node);\n    }\n  },\n\n  /**\n   * Tests whether a string is a URL or not.\n   *\n   * @param {string} str The string to test\n   * @return {boolean} true if str is a URL, false if not\n   */\n  _isUrl(str) {\n    try {\n      new URL(str);\n      return true;\n    } catch {\n      return false;\n    }\n  },\n  /**\n   * Converts each <a> and <img> uri in the given element to an absolute URI,\n   * ignoring #ref URIs.\n   *\n   * @param Element\n   * @return void\n   */\n  _fixRelativeUris(articleContent) {\n    var baseURI = this._doc.baseURI;\n    var documentURI = this._doc.documentURI;\n    function toAbsoluteURI(uri) {\n      // Leave hash links alone if the base URI matches the document URI:\n      if (baseURI == documentURI && uri.charAt(0) == \"#\") {\n        return uri;\n      }\n\n      // Otherwise, resolve against base URI:\n      try {\n        return new URL(uri, baseURI).href;\n      } catch (ex) {\n        // Something went wrong, just return the original:\n      }\n      return uri;\n    }\n\n    var links = this._getAllNodesWithTag(articleContent, [\"a\"]);\n    this._forEachNode(links, function (link) {\n      var href = link.getAttribute(\"href\");\n      if (href) {\n        // Remove links with javascript: URIs, since\n        // they won't work after scripts have been removed from the page.\n        if (href.indexOf(\"javascript:\") === 0) {\n          // if the link only contains simple text content, it can be converted to a text node\n          if (\n            link.childNodes.length === 1 &&\n            link.childNodes[0].nodeType === this.TEXT_NODE\n          ) {\n            var text = this._doc.createTextNode(link.textContent);\n            link.parentNode.replaceChild(text, link);\n          } else {\n            // if the link has multiple children, they should all be preserved\n            var container = this._doc.createElement(\"span\");\n            while (link.firstChild) {\n              container.appendChild(link.firstChild);\n            }\n            link.parentNode.replaceChild(container, link);\n          }\n        } else {\n          link.setAttribute(\"href\", toAbsoluteURI(href));\n        }\n      }\n    });\n\n    var medias = this._getAllNodesWithTag(articleContent, [\n      \"img\",\n      \"picture\",\n      \"figure\",\n      \"video\",\n      \"audio\",\n      \"source\",\n    ]);\n\n    this._forEachNode(medias, function (media) {\n      var src = media.getAttribute(\"src\");\n      var poster = media.getAttribute(\"poster\");\n      var srcset = media.getAttribute(\"srcset\");\n\n      if (src) {\n        media.setAttribute(\"src\", toAbsoluteURI(src));\n      }\n\n      if (poster) {\n        media.setAttribute(\"poster\", toAbsoluteURI(poster));\n      }\n\n      if (srcset) {\n        var newSrcset = srcset.replace(\n          this.REGEXPS.srcsetUrl,\n          function (_, p1, p2, p3) {\n            return toAbsoluteURI(p1) + (p2 || \"\") + p3;\n          }\n        );\n\n        media.setAttribute(\"srcset\", newSrcset);\n      }\n    });\n  },\n\n  _simplifyNestedElements(articleContent) {\n    var node = articleContent;\n\n    while (node) {\n      if (\n        node.parentNode &&\n        [\"DIV\", \"SECTION\"].includes(node.tagName) &&\n        !(node.id && node.id.startsWith(\"readability\"))\n      ) {\n        if (this._isElementWithoutContent(node)) {\n          node = this._removeAndGetNext(node);\n          continue;\n        } else if (\n          this._hasSingleTagInsideElement(node, \"DIV\") ||\n          this._hasSingleTagInsideElement(node, \"SECTION\")\n        ) {\n          var child = node.children[0];\n          for (var i = 0; i < node.attributes.length; i++) {\n            child.setAttributeNode(node.attributes[i].cloneNode());\n          }\n          node.parentNode.replaceChild(child, node);\n          node = child;\n          continue;\n        }\n      }\n\n      node = this._getNextNode(node);\n    }\n  },\n\n  /**\n   * Get the article title as an H1.\n   *\n   * @return string\n   **/\n  _getArticleTitle() {\n    var doc = this._doc;\n    var curTitle = \"\";\n    var origTitle = \"\";\n\n    try {\n      curTitle = origTitle = doc.title.trim();\n\n      // If they had an element with id \"title\" in their HTML\n      if (typeof curTitle !== \"string\") {\n        curTitle = origTitle = this._getInnerText(\n          doc.getElementsByTagName(\"title\")[0]\n        );\n      }\n    } catch (e) {\n      /* ignore exceptions setting the title. */\n    }\n\n    var titleHadHierarchicalSeparators = false;\n    function wordCount(str) {\n      return str.split(/\\s+/).length;\n    }\n\n    // If there's a separator in the title, first remove the final part\n    if (/ [\\|\\-\\\\\\/>»] /.test(curTitle)) {\n      titleHadHierarchicalSeparators = / [\\\\\\/>»] /.test(curTitle);\n      let allSeparators = Array.from(origTitle.matchAll(/ [\\|\\-\\\\\\/>»] /gi));\n      curTitle = origTitle.substring(0, allSeparators.pop().index);\n\n      // If the resulting title is too short, remove the first part instead:\n      if (wordCount(curTitle) < 3) {\n        curTitle = origTitle.replace(/^[^\\|\\-\\\\\\/>»]*[\\|\\-\\\\\\/>»]/gi, \"\");\n      }\n    } else if (curTitle.includes(\": \")) {\n      // Check if we have an heading containing this exact string, so we\n      // could assume it's the full title.\n      var headings = this._getAllNodesWithTag(doc, [\"h1\", \"h2\"]);\n      var trimmedTitle = curTitle.trim();\n      var match = this._someNode(headings, function (heading) {\n        return heading.textContent.trim() === trimmedTitle;\n      });\n\n      // If we don't, let's extract the title out of the original title string.\n      if (!match) {\n        curTitle = origTitle.substring(origTitle.lastIndexOf(\":\") + 1);\n\n        // If the title is now too short, try the first colon instead:\n        if (wordCount(curTitle) < 3) {\n          curTitle = origTitle.substring(origTitle.indexOf(\":\") + 1);\n          // But if we have too many words before the colon there's something weird\n          // with the titles and the H tags so let's just use the original title instead\n        } else if (wordCount(origTitle.substr(0, origTitle.indexOf(\":\"))) > 5) {\n          curTitle = origTitle;\n        }\n      }\n    } else if (curTitle.length > 150 || curTitle.length < 15) {\n      var hOnes = doc.getElementsByTagName(\"h1\");\n\n      if (hOnes.length === 1) {\n        curTitle = this._getInnerText(hOnes[0]);\n      }\n    }\n\n    curTitle = curTitle.trim().replace(this.REGEXPS.normalize, \" \");\n    // If we now have 4 words or fewer as our title, and either no\n    // 'hierarchical' separators (\\, /, > or ») were found in the original\n    // title or we decreased the number of words by more than 1 word, use\n    // the original title.\n    var curTitleWordCount = wordCount(curTitle);\n    if (\n      curTitleWordCount <= 4 &&\n      (!titleHadHierarchicalSeparators ||\n        curTitleWordCount !=\n          wordCount(origTitle.replace(/[\\|\\-\\\\\\/>»]+/g, \"\")) - 1)\n    ) {\n      curTitle = origTitle;\n    }\n\n    return curTitle;\n  },\n\n  /**\n   * Prepare the HTML document for readability to scrape it.\n   * This includes things like stripping javascript, CSS, and handling terrible markup.\n   *\n   * @return void\n   **/\n  _prepDocument() {\n    var doc = this._doc;\n\n    // Remove all style tags in head\n    this._removeNodes(this._getAllNodesWithTag(doc, [\"style\"]));\n\n    if (doc.body) {\n      this._replaceBrs(doc.body);\n    }\n\n    this._replaceNodeTags(this._getAllNodesWithTag(doc, [\"font\"]), \"SPAN\");\n  },\n\n  /**\n   * Finds the next node, starting from the given node, and ignoring\n   * whitespace in between. If the given node is an element, the same node is\n   * returned.\n   */\n  _nextNode(node) {\n    var next = node;\n    while (\n      next &&\n      next.nodeType != this.ELEMENT_NODE &&\n      this.REGEXPS.whitespace.test(next.textContent)\n    ) {\n      next = next.nextSibling;\n    }\n    return next;\n  },\n\n  /**\n   * Replaces 2 or more successive <br> elements with a single <p>.\n   * Whitespace between <br> elements are ignored. For example:\n   *   <div>foo<br>bar<br> <br><br>abc</div>\n   * will become:\n   *   <div>foo<br>bar<p>abc</p></div>\n   */\n  _replaceBrs(elem) {\n    this._forEachNode(this._getAllNodesWithTag(elem, [\"br\"]), function (br) {\n      var next = br.nextSibling;\n\n      // Whether 2 or more <br> elements have been found and replaced with a\n      // <p> block.\n      var replaced = false;\n\n      // If we find a <br> chain, remove the <br>s until we hit another node\n      // or non-whitespace. This leaves behind the first <br> in the chain\n      // (which will be replaced with a <p> later).\n      while ((next = this._nextNode(next)) && next.tagName == \"BR\") {\n        replaced = true;\n        var brSibling = next.nextSibling;\n        next.remove();\n        next = brSibling;\n      }\n\n      // If we removed a <br> chain, replace the remaining <br> with a <p>. Add\n      // all sibling nodes as children of the <p> until we hit another <br>\n      // chain.\n      if (replaced) {\n        var p = this._doc.createElement(\"p\");\n        br.parentNode.replaceChild(p, br);\n\n        next = p.nextSibling;\n        while (next) {\n          // If we've hit another <br><br>, we're done adding children to this <p>.\n          if (next.tagName == \"BR\") {\n            var nextElem = this._nextNode(next.nextSibling);\n            if (nextElem && nextElem.tagName == \"BR\") {\n              break;\n            }\n          }\n\n          if (!this._isPhrasingContent(next)) {\n            break;\n          }\n\n          // Otherwise, make this node a child of the new <p>.\n          var sibling = next.nextSibling;\n          p.appendChild(next);\n          next = sibling;\n        }\n\n        while (p.lastChild && this._isWhitespace(p.lastChild)) {\n          p.lastChild.remove();\n        }\n\n        if (p.parentNode.tagName === \"P\") {\n          this._setNodeTag(p.parentNode, \"DIV\");\n        }\n      }\n    });\n  },\n\n  _setNodeTag(node, tag) {\n    this.log(\"_setNodeTag\", node, tag);\n    if (this._docJSDOMParser) {\n      node.localName = tag.toLowerCase();\n      node.tagName = tag.toUpperCase();\n      return node;\n    }\n\n    var replacement = node.ownerDocument.createElement(tag);\n    while (node.firstChild) {\n      replacement.appendChild(node.firstChild);\n    }\n    node.parentNode.replaceChild(replacement, node);\n    if (node.readability) {\n      replacement.readability = node.readability;\n    }\n\n    for (var i = 0; i < node.attributes.length; i++) {\n      replacement.setAttributeNode(node.attributes[i].cloneNode());\n    }\n    return replacement;\n  },\n\n  /**\n   * Prepare the article node for display. Clean out any inline styles,\n   * iframes, forms, strip extraneous <p> tags, etc.\n   *\n   * @param Element\n   * @return void\n   **/\n  _prepArticle(articleContent) {\n    this._cleanStyles(articleContent);\n\n    // Check for data tables before we continue, to avoid removing items in\n    // those tables, which will often be isolated even though they're\n    // visually linked to other content-ful elements (text, images, etc.).\n    this._markDataTables(articleContent);\n\n    this._fixLazyImages(articleContent);\n\n    // Clean out junk from the article content\n    this._cleanConditionally(articleContent, \"form\");\n    this._cleanConditionally(articleContent, \"fieldset\");\n    this._clean(articleContent, \"object\");\n    this._clean(articleContent, \"embed\");\n    this._clean(articleContent, \"footer\");\n    this._clean(articleContent, \"link\");\n    this._clean(articleContent, \"aside\");\n\n    // Clean out elements with little content that have \"share\" in their id/class combinations from final top candidates,\n    // which means we don't remove the top candidates even they have \"share\".\n\n    var shareElementThreshold = this.DEFAULT_CHAR_THRESHOLD;\n\n    this._forEachNode(articleContent.children, function (topCandidate) {\n      this._cleanMatchedNodes(topCandidate, function (node, matchString) {\n        return (\n          this.REGEXPS.shareElements.test(matchString) &&\n          node.textContent.length < shareElementThreshold\n        );\n      });\n    });\n\n    this._clean(articleContent, \"iframe\");\n    this._clean(articleContent, \"input\");\n    this._clean(articleContent, \"textarea\");\n    this._clean(articleContent, \"select\");\n    this._clean(articleContent, \"button\");\n    this._cleanHeaders(articleContent);\n\n    // Do these last as the previous stuff may have removed junk\n    // that will affect these\n    this._cleanConditionally(articleContent, \"table\");\n    this._cleanConditionally(articleContent, \"ul\");\n    this._cleanConditionally(articleContent, \"div\");\n\n    // replace H1 with H2 as H1 should be only title that is displayed separately\n    this._replaceNodeTags(\n      this._getAllNodesWithTag(articleContent, [\"h1\"]),\n      \"h2\"\n    );\n\n    // Remove extra paragraphs\n    this._removeNodes(\n      this._getAllNodesWithTag(articleContent, [\"p\"]),\n      function (paragraph) {\n        // At this point, nasty iframes have been removed; only embedded video\n        // ones remain.\n        var contentElementCount = this._getAllNodesWithTag(paragraph, [\n          \"img\",\n          \"embed\",\n          \"object\",\n          \"iframe\",\n        ]).length;\n        return (\n          contentElementCount === 0 && !this._getInnerText(paragraph, false)\n        );\n      }\n    );\n\n    this._forEachNode(\n      this._getAllNodesWithTag(articleContent, [\"br\"]),\n      function (br) {\n        var next = this._nextNode(br.nextSibling);\n        if (next && next.tagName == \"P\") {\n          br.remove();\n        }\n      }\n    );\n\n    // Remove single-cell tables\n    this._forEachNode(\n      this._getAllNodesWithTag(articleContent, [\"table\"]),\n      function (table) {\n        var tbody = this._hasSingleTagInsideElement(table, \"TBODY\")\n          ? table.firstElementChild\n          : table;\n        if (this._hasSingleTagInsideElement(tbody, \"TR\")) {\n          var row = tbody.firstElementChild;\n          if (this._hasSingleTagInsideElement(row, \"TD\")) {\n            var cell = row.firstElementChild;\n            cell = this._setNodeTag(\n              cell,\n              this._everyNode(cell.childNodes, this._isPhrasingContent)\n                ? \"P\"\n                : \"DIV\"\n            );\n            table.parentNode.replaceChild(cell, table);\n          }\n        }\n      }\n    );\n  },\n\n  /**\n   * Initialize a node with the readability object. Also checks the\n   * className/id for special names to add to its score.\n   *\n   * @param Element\n   * @return void\n   **/\n  _initializeNode(node) {\n    node.readability = { contentScore: 0 };\n\n    switch (node.tagName) {\n      case \"DIV\":\n        node.readability.contentScore += 5;\n        break;\n\n      case \"PRE\":\n      case \"TD\":\n      case \"BLOCKQUOTE\":\n        node.readability.contentScore += 3;\n        break;\n\n      case \"ADDRESS\":\n      case \"OL\":\n      case \"UL\":\n      case \"DL\":\n      case \"DD\":\n      case \"DT\":\n      case \"LI\":\n      case \"FORM\":\n        node.readability.contentScore -= 3;\n        break;\n\n      case \"H1\":\n      case \"H2\":\n      case \"H3\":\n      case \"H4\":\n      case \"H5\":\n      case \"H6\":\n      case \"TH\":\n        node.readability.contentScore -= 5;\n        break;\n    }\n\n    node.readability.contentScore += this._getClassWeight(node);\n  },\n\n  _removeAndGetNext(node) {\n    var nextNode = this._getNextNode(node, true);\n    node.remove();\n    return nextNode;\n  },\n\n  /**\n   * Traverse the DOM from node to node, starting at the node passed in.\n   * Pass true for the second parameter to indicate this node itself\n   * (and its kids) are going away, and we want the next node over.\n   *\n   * Calling this in a loop will traverse the DOM depth-first.\n   *\n   * @param {Element} node\n   * @param {boolean} ignoreSelfAndKids\n   * @return {Element}\n   */\n  _getNextNode(node, ignoreSelfAndKids) {\n    // First check for kids if those aren't being ignored\n    if (!ignoreSelfAndKids && node.firstElementChild) {\n      return node.firstElementChild;\n    }\n    // Then for siblings...\n    if (node.nextElementSibling) {\n      return node.nextElementSibling;\n    }\n    // And finally, move up the parent chain *and* find a sibling\n    // (because this is depth-first traversal, we will have already\n    // seen the parent nodes themselves).\n    do {\n      node = node.parentNode;\n    } while (node && !node.nextElementSibling);\n    return node && node.nextElementSibling;\n  },\n\n  // compares second text to first one\n  // 1 = same text, 0 = completely different text\n  // works the way that it splits both texts into words and then finds words that are unique in second text\n  // the result is given by the lower length of unique parts\n  _textSimilarity(textA, textB) {\n    var tokensA = textA\n      .toLowerCase()\n      .split(this.REGEXPS.tokenize)\n      .filter(Boolean);\n    var tokensB = textB\n      .toLowerCase()\n      .split(this.REGEXPS.tokenize)\n      .filter(Boolean);\n    if (!tokensA.length || !tokensB.length) {\n      return 0;\n    }\n    var uniqTokensB = tokensB.filter(token => !tokensA.includes(token));\n    var distanceB = uniqTokensB.join(\" \").length / tokensB.join(\" \").length;\n    return 1 - distanceB;\n  },\n\n  /**\n   * Checks whether an element node contains a valid byline\n   *\n   * @param node {Element}\n   * @param matchString {string}\n   * @return boolean\n   */\n  _isValidByline(node, matchString) {\n    var rel = node.getAttribute(\"rel\");\n    var itemprop = node.getAttribute(\"itemprop\");\n    var bylineLength = node.textContent.trim().length;\n\n    return (\n      (rel === \"author\" ||\n        (itemprop && itemprop.includes(\"author\")) ||\n        this.REGEXPS.byline.test(matchString)) &&\n      !!bylineLength &&\n      bylineLength < 100\n    );\n  },\n\n  _getNodeAncestors(node, maxDepth) {\n    maxDepth = maxDepth || 0;\n    var i = 0,\n      ancestors = [];\n    while (node.parentNode) {\n      ancestors.push(node.parentNode);\n      if (maxDepth && ++i === maxDepth) {\n        break;\n      }\n      node = node.parentNode;\n    }\n    return ancestors;\n  },\n\n  /***\n   * grabArticle - Using a variety of metrics (content score, classname, element types), find the content that is\n   *         most likely to be the stuff a user wants to read. Then return it wrapped up in a div.\n   *\n   * @param page a document to run upon. Needs to be a full document, complete with body.\n   * @return Element\n   **/\n  /* eslint-disable-next-line complexity */\n  _grabArticle(page) {\n    this.log(\"**** grabArticle ****\");\n    var doc = this._doc;\n    var isPaging = page !== null;\n    page = page ? page : this._doc.body;\n\n    // We can't grab an article if we don't have a page!\n    if (!page) {\n      this.log(\"No body found in document. Abort.\");\n      return null;\n    }\n\n    var pageCacheHtml = page.innerHTML;\n\n    while (true) {\n      this.log(\"Starting grabArticle loop\");\n      var stripUnlikelyCandidates = this._flagIsActive(\n        this.FLAG_STRIP_UNLIKELYS\n      );\n\n      // First, node prepping. Trash nodes that look cruddy (like ones with the\n      // class name \"comment\", etc), and turn divs into P tags where they have been\n      // used inappropriately (as in, where they contain no other block level elements.)\n      var elementsToScore = [];\n      var node = this._doc.documentElement;\n\n      let shouldRemoveTitleHeader = true;\n\n      while (node) {\n        if (node.tagName === \"HTML\") {\n          this._articleLang = node.getAttribute(\"lang\");\n        }\n\n        var matchString = node.className + \" \" + node.id;\n\n        if (!this._isProbablyVisible(node)) {\n          this.log(\"Removing hidden node - \" + matchString);\n          node = this._removeAndGetNext(node);\n          continue;\n        }\n\n        // User is not able to see elements applied with both \"aria-modal = true\" and \"role = dialog\"\n        if (\n          node.getAttribute(\"aria-modal\") == \"true\" &&\n          node.getAttribute(\"role\") == \"dialog\"\n        ) {\n          node = this._removeAndGetNext(node);\n          continue;\n        }\n\n        // If we don't have a byline yet check to see if this node is a byline; if it is store the byline and remove the node.\n        if (\n          !this._articleByline &&\n          !this._metadata.byline &&\n          this._isValidByline(node, matchString)\n        ) {\n          // Find child node matching [itemprop=\"name\"] and use that if it exists for a more accurate author name byline\n          var endOfSearchMarkerNode = this._getNextNode(node, true);\n          var next = this._getNextNode(node);\n          var itemPropNameNode = null;\n          while (next && next != endOfSearchMarkerNode) {\n            var itemprop = next.getAttribute(\"itemprop\");\n            if (itemprop && itemprop.includes(\"name\")) {\n              itemPropNameNode = next;\n              break;\n            } else {\n              next = this._getNextNode(next);\n            }\n          }\n          this._articleByline = (itemPropNameNode ?? node).textContent.trim();\n          node = this._removeAndGetNext(node);\n          continue;\n        }\n\n        if (shouldRemoveTitleHeader && this._headerDuplicatesTitle(node)) {\n          this.log(\n            \"Removing header: \",\n            node.textContent.trim(),\n            this._articleTitle.trim()\n          );\n          shouldRemoveTitleHeader = false;\n          node = this._removeAndGetNext(node);\n          continue;\n        }\n\n        // Remove unlikely candidates\n        if (stripUnlikelyCandidates) {\n          if (\n            this.REGEXPS.unlikelyCandidates.test(matchString) &&\n            !this.REGEXPS.okMaybeItsACandidate.test(matchString) &&\n            !this._hasAncestorTag(node, \"table\") &&\n            !this._hasAncestorTag(node, \"code\") &&\n            node.tagName !== \"BODY\" &&\n            node.tagName !== \"A\"\n          ) {\n            this.log(\"Removing unlikely candidate - \" + matchString);\n            node = this._removeAndGetNext(node);\n            continue;\n          }\n\n          if (this.UNLIKELY_ROLES.includes(node.getAttribute(\"role\"))) {\n            this.log(\n              \"Removing content with role \" +\n                node.getAttribute(\"role\") +\n                \" - \" +\n                matchString\n            );\n            node = this._removeAndGetNext(node);\n            continue;\n          }\n        }\n\n        // Remove DIV, SECTION, and HEADER nodes without any content(e.g. text, image, video, or iframe).\n        if (\n          (node.tagName === \"DIV\" ||\n            node.tagName === \"SECTION\" ||\n            node.tagName === \"HEADER\" ||\n            node.tagName === \"H1\" ||\n            node.tagName === \"H2\" ||\n            node.tagName === \"H3\" ||\n            node.tagName === \"H4\" ||\n            node.tagName === \"H5\" ||\n            node.tagName === \"H6\") &&\n          this._isElementWithoutContent(node)\n        ) {\n          node = this._removeAndGetNext(node);\n          continue;\n        }\n\n        if (this.DEFAULT_TAGS_TO_SCORE.includes(node.tagName)) {\n          elementsToScore.push(node);\n        }\n\n        // Turn all divs that don't have children block level elements into p's\n        if (node.tagName === \"DIV\") {\n          // Put phrasing content into paragraphs.\n          var p = null;\n          var childNode = node.firstChild;\n          while (childNode) {\n            var nextSibling = childNode.nextSibling;\n            if (this._isPhrasingContent(childNode)) {\n              if (p !== null) {\n                p.appendChild(childNode);\n              } else if (!this._isWhitespace(childNode)) {\n                p = doc.createElement(\"p\");\n                node.replaceChild(p, childNode);\n                p.appendChild(childNode);\n              }\n            } else if (p !== null) {\n              while (p.lastChild && this._isWhitespace(p.lastChild)) {\n                p.lastChild.remove();\n              }\n              p = null;\n            }\n            childNode = nextSibling;\n          }\n\n          // Sites like http://mobile.slate.com encloses each paragraph with a DIV\n          // element. DIVs with only a P element inside and no text content can be\n          // safely converted into plain P elements to avoid confusing the scoring\n          // algorithm with DIVs with are, in practice, paragraphs.\n          if (\n            this._hasSingleTagInsideElement(node, \"P\") &&\n            this._getLinkDensity(node) < 0.25\n          ) {\n            var newNode = node.children[0];\n            node.parentNode.replaceChild(newNode, node);\n            node = newNode;\n            elementsToScore.push(node);\n          } else if (!this._hasChildBlockElement(node)) {\n            node = this._setNodeTag(node, \"P\");\n            elementsToScore.push(node);\n          }\n        }\n        node = this._getNextNode(node);\n      }\n\n      /**\n       * Loop through all paragraphs, and assign a score to them based on how content-y they look.\n       * Then add their score to their parent node.\n       *\n       * A score is determined by things like number of commas, class names, etc. Maybe eventually link density.\n       **/\n      var candidates = [];\n      this._forEachNode(elementsToScore, function (elementToScore) {\n        if (\n          !elementToScore.parentNode ||\n          typeof elementToScore.parentNode.tagName === \"undefined\"\n        ) {\n          return;\n        }\n\n        // If this paragraph is less than 25 characters, don't even count it.\n        var innerText = this._getInnerText(elementToScore);\n        if (innerText.length < 25) {\n          return;\n        }\n\n        // Exclude nodes with no ancestor.\n        var ancestors = this._getNodeAncestors(elementToScore, 5);\n        if (ancestors.length === 0) {\n          return;\n        }\n\n        var contentScore = 0;\n\n        // Add a point for the paragraph itself as a base.\n        contentScore += 1;\n\n        // Add points for any commas within this paragraph.\n        contentScore += innerText.split(this.REGEXPS.commas).length;\n\n        // For every 100 characters in this paragraph, add another point. Up to 3 points.\n        contentScore += Math.min(Math.floor(innerText.length / 100), 3);\n\n        // Initialize and score ancestors.\n        this._forEachNode(ancestors, function (ancestor, level) {\n          if (\n            !ancestor.tagName ||\n            !ancestor.parentNode ||\n            typeof ancestor.parentNode.tagName === \"undefined\"\n          ) {\n            return;\n          }\n\n          if (typeof ancestor.readability === \"undefined\") {\n            this._initializeNode(ancestor);\n            candidates.push(ancestor);\n          }\n\n          // Node score divider:\n          // - parent:             1 (no division)\n          // - grandparent:        2\n          // - great grandparent+: ancestor level * 3\n          if (level === 0) {\n            var scoreDivider = 1;\n          } else if (level === 1) {\n            scoreDivider = 2;\n          } else {\n            scoreDivider = level * 3;\n          }\n          ancestor.readability.contentScore += contentScore / scoreDivider;\n        });\n      });\n\n      // After we've calculated scores, loop through all of the possible\n      // candidate nodes we found and find the one with the highest score.\n      var topCandidates = [];\n      for (var c = 0, cl = candidates.length; c < cl; c += 1) {\n        var candidate = candidates[c];\n\n        // Scale the final candidates score based on link density. Good content\n        // should have a relatively small link density (5% or less) and be mostly\n        // unaffected by this operation.\n        var candidateScore =\n          candidate.readability.contentScore *\n          (1 - this._getLinkDensity(candidate));\n        candidate.readability.contentScore = candidateScore;\n\n        this.log(\"Candidate:\", candidate, \"with score \" + candidateScore);\n\n        for (var t = 0; t < this._nbTopCandidates; t++) {\n          var aTopCandidate = topCandidates[t];\n\n          if (\n            !aTopCandidate ||\n            candidateScore > aTopCandidate.readability.contentScore\n          ) {\n            topCandidates.splice(t, 0, candidate);\n            if (topCandidates.length > this._nbTopCandidates) {\n              topCandidates.pop();\n            }\n            break;\n          }\n        }\n      }\n\n      var topCandidate = topCandidates[0] || null;\n      var neededToCreateTopCandidate = false;\n      var parentOfTopCandidate;\n\n      // If we still have no top candidate, just use the body as a last resort.\n      // We also have to copy the body node so it is something we can modify.\n      if (topCandidate === null || topCandidate.tagName === \"BODY\") {\n        // Move all of the page's children into topCandidate\n        topCandidate = doc.createElement(\"DIV\");\n        neededToCreateTopCandidate = true;\n        // Move everything (not just elements, also text nodes etc.) into the container\n        // so we even include text directly in the body:\n        while (page.firstChild) {\n          this.log(\"Moving child out:\", page.firstChild);\n          topCandidate.appendChild(page.firstChild);\n        }\n\n        page.appendChild(topCandidate);\n\n        this._initializeNode(topCandidate);\n      } else if (topCandidate) {\n        // Find a better top candidate node if it contains (at least three) nodes which belong to `topCandidates` array\n        // and whose scores are quite closed with current `topCandidate` node.\n        var alternativeCandidateAncestors = [];\n        for (var i = 1; i < topCandidates.length; i++) {\n          if (\n            topCandidates[i].readability.contentScore /\n              topCandidate.readability.contentScore >=\n            0.75\n          ) {\n            alternativeCandidateAncestors.push(\n              this._getNodeAncestors(topCandidates[i])\n            );\n          }\n        }\n        var MINIMUM_TOPCANDIDATES = 3;\n        if (alternativeCandidateAncestors.length >= MINIMUM_TOPCANDIDATES) {\n          parentOfTopCandidate = topCandidate.parentNode;\n          while (parentOfTopCandidate.tagName !== \"BODY\") {\n            var listsContainingThisAncestor = 0;\n            for (\n              var ancestorIndex = 0;\n              ancestorIndex < alternativeCandidateAncestors.length &&\n              listsContainingThisAncestor < MINIMUM_TOPCANDIDATES;\n              ancestorIndex++\n            ) {\n              listsContainingThisAncestor += Number(\n                alternativeCandidateAncestors[ancestorIndex].includes(\n                  parentOfTopCandidate\n                )\n              );\n            }\n            if (listsContainingThisAncestor >= MINIMUM_TOPCANDIDATES) {\n              topCandidate = parentOfTopCandidate;\n              break;\n            }\n            parentOfTopCandidate = parentOfTopCandidate.parentNode;\n          }\n        }\n        if (!topCandidate.readability) {\n          this._initializeNode(topCandidate);\n        }\n\n        // Because of our bonus system, parents of candidates might have scores\n        // themselves. They get half of the node. There won't be nodes with higher\n        // scores than our topCandidate, but if we see the score going *up* in the first\n        // few steps up the tree, that's a decent sign that there might be more content\n        // lurking in other places that we want to unify in. The sibling stuff\n        // below does some of that - but only if we've looked high enough up the DOM\n        // tree.\n        parentOfTopCandidate = topCandidate.parentNode;\n        var lastScore = topCandidate.readability.contentScore;\n        // The scores shouldn't get too low.\n        var scoreThreshold = lastScore / 3;\n        while (parentOfTopCandidate.tagName !== \"BODY\") {\n          if (!parentOfTopCandidate.readability) {\n            parentOfTopCandidate = parentOfTopCandidate.parentNode;\n            continue;\n          }\n          var parentScore = parentOfTopCandidate.readability.contentScore;\n          if (parentScore < scoreThreshold) {\n            break;\n          }\n          if (parentScore > lastScore) {\n            // Alright! We found a better parent to use.\n            topCandidate = parentOfTopCandidate;\n            break;\n          }\n          lastScore = parentOfTopCandidate.readability.contentScore;\n          parentOfTopCandidate = parentOfTopCandidate.parentNode;\n        }\n\n        // If the top candidate is the only child, use parent instead. This will help sibling\n        // joining logic when adjacent content is actually located in parent's sibling node.\n        parentOfTopCandidate = topCandidate.parentNode;\n        while (\n          parentOfTopCandidate.tagName != \"BODY\" &&\n          parentOfTopCandidate.children.length == 1\n        ) {\n          topCandidate = parentOfTopCandidate;\n          parentOfTopCandidate = topCandidate.parentNode;\n        }\n        if (!topCandidate.readability) {\n          this._initializeNode(topCandidate);\n        }\n      }\n\n      // Now that we have the top candidate, look through its siblings for content\n      // that might also be related. Things like preambles, content split by ads\n      // that we removed, etc.\n      var articleContent = doc.createElement(\"DIV\");\n      if (isPaging) {\n        articleContent.id = \"readability-content\";\n      }\n\n      var siblingScoreThreshold = Math.max(\n        10,\n        topCandidate.readability.contentScore * 0.2\n      );\n      // Keep potential top candidate's parent node to try to get text direction of it later.\n      parentOfTopCandidate = topCandidate.parentNode;\n      var siblings = parentOfTopCandidate.children;\n\n      for (var s = 0, sl = siblings.length; s < sl; s++) {\n        var sibling = siblings[s];\n        var append = false;\n\n        this.log(\n          \"Looking at sibling node:\",\n          sibling,\n          sibling.readability\n            ? \"with score \" + sibling.readability.contentScore\n            : \"\"\n        );\n        this.log(\n          \"Sibling has score\",\n          sibling.readability ? sibling.readability.contentScore : \"Unknown\"\n        );\n\n        if (sibling === topCandidate) {\n          append = true;\n        } else {\n          var contentBonus = 0;\n\n          // Give a bonus if sibling nodes and top candidates have the example same classname\n          if (\n            sibling.className === topCandidate.className &&\n            topCandidate.className !== \"\"\n          ) {\n            contentBonus += topCandidate.readability.contentScore * 0.2;\n          }\n\n          if (\n            sibling.readability &&\n            sibling.readability.contentScore + contentBonus >=\n              siblingScoreThreshold\n          ) {\n            append = true;\n          } else if (sibling.nodeName === \"P\") {\n            var linkDensity = this._getLinkDensity(sibling);\n            var nodeContent = this._getInnerText(sibling);\n            var nodeLength = nodeContent.length;\n\n            if (nodeLength > 80 && linkDensity < 0.25) {\n              append = true;\n            } else if (\n              nodeLength < 80 &&\n              nodeLength > 0 &&\n              linkDensity === 0 &&\n              nodeContent.search(/\\.( |$)/) !== -1\n            ) {\n              append = true;\n            }\n          }\n        }\n\n        if (append) {\n          this.log(\"Appending node:\", sibling);\n\n          if (!this.ALTER_TO_DIV_EXCEPTIONS.includes(sibling.nodeName)) {\n            // We have a node that isn't a common block level element, like a form or td tag.\n            // Turn it into a div so it doesn't get filtered out later by accident.\n            this.log(\"Altering sibling:\", sibling, \"to div.\");\n\n            sibling = this._setNodeTag(sibling, \"DIV\");\n          }\n\n          articleContent.appendChild(sibling);\n          // Fetch children again to make it compatible\n          // with DOM parsers without live collection support.\n          siblings = parentOfTopCandidate.children;\n          // siblings is a reference to the children array, and\n          // sibling is removed from the array when we call appendChild().\n          // As a result, we must revisit this index since the nodes\n          // have been shifted.\n          s -= 1;\n          sl -= 1;\n        }\n      }\n\n      if (this._debug) {\n        this.log(\"Article content pre-prep: \" + articleContent.innerHTML);\n      }\n      // So we have all of the content that we need. Now we clean it up for presentation.\n      this._prepArticle(articleContent);\n      if (this._debug) {\n        this.log(\"Article content post-prep: \" + articleContent.innerHTML);\n      }\n\n      if (neededToCreateTopCandidate) {\n        // We already created a fake div thing, and there wouldn't have been any siblings left\n        // for the previous loop, so there's no point trying to create a new div, and then\n        // move all the children over. Just assign IDs and class names here. No need to append\n        // because that already happened anyway.\n        topCandidate.id = \"readability-page-1\";\n        topCandidate.className = \"page\";\n      } else {\n        var div = doc.createElement(\"DIV\");\n        div.id = \"readability-page-1\";\n        div.className = \"page\";\n        while (articleContent.firstChild) {\n          div.appendChild(articleContent.firstChild);\n        }\n        articleContent.appendChild(div);\n      }\n\n      if (this._debug) {\n        this.log(\"Article content after paging: \" + articleContent.innerHTML);\n      }\n\n      var parseSuccessful = true;\n\n      // Now that we've gone through the full algorithm, check to see if\n      // we got any meaningful content. If we didn't, we may need to re-run\n      // grabArticle with different flags set. This gives us a higher likelihood of\n      // finding the content, and the sieve approach gives us a higher likelihood of\n      // finding the -right- content.\n      var textLength = this._getInnerText(articleContent, true).length;\n      if (textLength < this._charThreshold) {\n        parseSuccessful = false;\n        // eslint-disable-next-line no-unsanitized/property\n        page.innerHTML = pageCacheHtml;\n\n        this._attempts.push({\n          articleContent,\n          textLength,\n        });\n\n        if (this._flagIsActive(this.FLAG_STRIP_UNLIKELYS)) {\n          this._removeFlag(this.FLAG_STRIP_UNLIKELYS);\n        } else if (this._flagIsActive(this.FLAG_WEIGHT_CLASSES)) {\n          this._removeFlag(this.FLAG_WEIGHT_CLASSES);\n        } else if (this._flagIsActive(this.FLAG_CLEAN_CONDITIONALLY)) {\n          this._removeFlag(this.FLAG_CLEAN_CONDITIONALLY);\n        } else {\n          // No luck after removing flags, just return the longest text we found during the different loops\n          this._attempts.sort(function (a, b) {\n            return b.textLength - a.textLength;\n          });\n\n          // But first check if we actually have something\n          if (!this._attempts[0].textLength) {\n            return null;\n          }\n\n          articleContent = this._attempts[0].articleContent;\n          parseSuccessful = true;\n        }\n      }\n\n      if (parseSuccessful) {\n        // Find out text direction from ancestors of final top candidate.\n        var ancestors = [parentOfTopCandidate, topCandidate].concat(\n          this._getNodeAncestors(parentOfTopCandidate)\n        );\n        this._someNode(ancestors, function (ancestor) {\n          if (!ancestor.tagName) {\n            return false;\n          }\n          var articleDir = ancestor.getAttribute(\"dir\");\n          if (articleDir) {\n            this._articleDir = articleDir;\n            return true;\n          }\n          return false;\n        });\n        return articleContent;\n      }\n    }\n  },\n\n  /**\n   * Converts some of the common HTML entities in string to their corresponding characters.\n   *\n   * @param str {string} - a string to unescape.\n   * @return string without HTML entity.\n   */\n  _unescapeHtmlEntities(str) {\n    if (!str) {\n      return str;\n    }\n\n    var htmlEscapeMap = this.HTML_ESCAPE_MAP;\n    return str\n      .replace(/&(quot|amp|apos|lt|gt);/g, function (_, tag) {\n        return htmlEscapeMap[tag];\n      })\n      .replace(/&#(?:x([0-9a-f]+)|([0-9]+));/gi, function (_, hex, numStr) {\n        var num = parseInt(hex || numStr, hex ? 16 : 10);\n\n        // these character references are replaced by a conforming HTML parser\n        if (num == 0 || num > 0x10ffff || (num >= 0xd800 && num <= 0xdfff)) {\n          num = 0xfffd;\n        }\n\n        return String.fromCodePoint(num);\n      });\n  },\n\n  /**\n   * Try to extract metadata from JSON-LD object.\n   * For now, only Schema.org objects of type Article or its subtypes are supported.\n   * @return Object with any metadata that could be extracted (possibly none)\n   */\n  _getJSONLD(doc) {\n    var scripts = this._getAllNodesWithTag(doc, [\"script\"]);\n\n    var metadata;\n\n    this._forEachNode(scripts, function (jsonLdElement) {\n      if (\n        !metadata &&\n        jsonLdElement.getAttribute(\"type\") === \"application/ld+json\"\n      ) {\n        try {\n          // Strip CDATA markers if present\n          var content = jsonLdElement.textContent.replace(\n            /^\\s*<!\\[CDATA\\[|\\]\\]>\\s*$/g,\n            \"\"\n          );\n          var parsed = JSON.parse(content);\n\n          if (Array.isArray(parsed)) {\n            parsed = parsed.find(it => {\n              return (\n                it[\"@type\"] &&\n                it[\"@type\"].match(this.REGEXPS.jsonLdArticleTypes)\n              );\n            });\n            if (!parsed) {\n              return;\n            }\n          }\n\n          var schemaDotOrgRegex = /^https?\\:\\/\\/schema\\.org\\/?$/;\n          var matches =\n            (typeof parsed[\"@context\"] === \"string\" &&\n              parsed[\"@context\"].match(schemaDotOrgRegex)) ||\n            (typeof parsed[\"@context\"] === \"object\" &&\n              typeof parsed[\"@context\"][\"@vocab\"] == \"string\" &&\n              parsed[\"@context\"][\"@vocab\"].match(schemaDotOrgRegex));\n\n          if (!matches) {\n            return;\n          }\n\n          if (!parsed[\"@type\"] && Array.isArray(parsed[\"@graph\"])) {\n            parsed = parsed[\"@graph\"].find(it => {\n              return (it[\"@type\"] || \"\").match(this.REGEXPS.jsonLdArticleTypes);\n            });\n          }\n\n          if (\n            !parsed ||\n            !parsed[\"@type\"] ||\n            !parsed[\"@type\"].match(this.REGEXPS.jsonLdArticleTypes)\n          ) {\n            return;\n          }\n\n          metadata = {};\n\n          if (\n            typeof parsed.name === \"string\" &&\n            typeof parsed.headline === \"string\" &&\n            parsed.name !== parsed.headline\n          ) {\n            // we have both name and headline element in the JSON-LD. They should both be the same but some websites like aktualne.cz\n            // put their own name into \"name\" and the article title to \"headline\" which confuses Readability. So we try to check if either\n            // \"name\" or \"headline\" closely matches the html title, and if so, use that one. If not, then we use \"name\" by default.\n\n            var title = this._getArticleTitle();\n            var nameMatches = this._textSimilarity(parsed.name, title) > 0.75;\n            var headlineMatches =\n              this._textSimilarity(parsed.headline, title) > 0.75;\n\n            if (headlineMatches && !nameMatches) {\n              metadata.title = parsed.headline;\n            } else {\n              metadata.title = parsed.name;\n            }\n          } else if (typeof parsed.name === \"string\") {\n            metadata.title = parsed.name.trim();\n          } else if (typeof parsed.headline === \"string\") {\n            metadata.title = parsed.headline.trim();\n          }\n          if (parsed.author) {\n            if (typeof parsed.author.name === \"string\") {\n              metadata.byline = parsed.author.name.trim();\n            } else if (\n              Array.isArray(parsed.author) &&\n              parsed.author[0] &&\n              typeof parsed.author[0].name === \"string\"\n            ) {\n              metadata.byline = parsed.author\n                .filter(function (author) {\n                  return author && typeof author.name === \"string\";\n                })\n                .map(function (author) {\n                  return author.name.trim();\n                })\n                .join(\", \");\n            }\n          }\n          if (typeof parsed.description === \"string\") {\n            metadata.excerpt = parsed.description.trim();\n          }\n          if (parsed.publisher && typeof parsed.publisher.name === \"string\") {\n            metadata.siteName = parsed.publisher.name.trim();\n          }\n          if (typeof parsed.datePublished === \"string\") {\n            metadata.datePublished = parsed.datePublished.trim();\n          }\n        } catch (err) {\n          this.log(err.message);\n        }\n      }\n    });\n    return metadata ? metadata : {};\n  },\n\n  /**\n   * Attempts to get excerpt and byline metadata for the article.\n   *\n   * @param {Object} jsonld — object containing any metadata that\n   * could be extracted from JSON-LD object.\n   *\n   * @return Object with optional \"excerpt\" and \"byline\" properties\n   */\n  _getArticleMetadata(jsonld) {\n    var metadata = {};\n    var values = {};\n    var metaElements = this._doc.getElementsByTagName(\"meta\");\n\n    // property is a space-separated list of values\n    var propertyPattern =\n      /\\s*(article|dc|dcterm|og|twitter)\\s*:\\s*(author|creator|description|published_time|title|site_name)\\s*/gi;\n\n    // name is a single value\n    var namePattern =\n      /^\\s*(?:(dc|dcterm|og|twitter|parsely|weibo:(article|webpage))\\s*[-\\.:]\\s*)?(author|creator|pub-date|description|title|site_name)\\s*$/i;\n\n    // Find description tags.\n    this._forEachNode(metaElements, function (element) {\n      var elementName = element.getAttribute(\"name\");\n      var elementProperty = element.getAttribute(\"property\");\n      var content = element.getAttribute(\"content\");\n      if (!content) {\n        return;\n      }\n      var matches = null;\n      var name = null;\n\n      if (elementProperty) {\n        matches = elementProperty.match(propertyPattern);\n        if (matches) {\n          // Convert to lowercase, and remove any whitespace\n          // so we can match below.\n          name = matches[0].toLowerCase().replace(/\\s/g, \"\");\n          // multiple authors\n          values[name] = content.trim();\n        }\n      }\n      if (!matches && elementName && namePattern.test(elementName)) {\n        name = elementName;\n        if (content) {\n          // Convert to lowercase, remove any whitespace, and convert dots\n          // to colons so we can match below.\n          name = name.toLowerCase().replace(/\\s/g, \"\").replace(/\\./g, \":\");\n          values[name] = content.trim();\n        }\n      }\n    });\n\n    // get title\n    metadata.title =\n      jsonld.title ||\n      values[\"dc:title\"] ||\n      values[\"dcterm:title\"] ||\n      values[\"og:title\"] ||\n      values[\"weibo:article:title\"] ||\n      values[\"weibo:webpage:title\"] ||\n      values.title ||\n      values[\"twitter:title\"] ||\n      values[\"parsely-title\"];\n\n    if (!metadata.title) {\n      metadata.title = this._getArticleTitle();\n    }\n\n    const articleAuthor =\n      typeof values[\"article:author\"] === \"string\" &&\n      !this._isUrl(values[\"article:author\"])\n        ? values[\"article:author\"]\n        : undefined;\n\n    // get author\n    metadata.byline =\n      jsonld.byline ||\n      values[\"dc:creator\"] ||\n      values[\"dcterm:creator\"] ||\n      values.author ||\n      values[\"parsely-author\"] ||\n      articleAuthor;\n\n    // get description\n    metadata.excerpt =\n      jsonld.excerpt ||\n      values[\"dc:description\"] ||\n      values[\"dcterm:description\"] ||\n      values[\"og:description\"] ||\n      values[\"weibo:article:description\"] ||\n      values[\"weibo:webpage:description\"] ||\n      values.description ||\n      values[\"twitter:description\"];\n\n    // get site name\n    metadata.siteName = jsonld.siteName || values[\"og:site_name\"];\n\n    // get article published time\n    metadata.publishedTime =\n      jsonld.datePublished ||\n      values[\"article:published_time\"] ||\n      values[\"parsely-pub-date\"] ||\n      null;\n\n    // in many sites the meta value is escaped with HTML entities,\n    // so here we need to unescape it\n    metadata.title = this._unescapeHtmlEntities(metadata.title);\n    metadata.byline = this._unescapeHtmlEntities(metadata.byline);\n    metadata.excerpt = this._unescapeHtmlEntities(metadata.excerpt);\n    metadata.siteName = this._unescapeHtmlEntities(metadata.siteName);\n    metadata.publishedTime = this._unescapeHtmlEntities(metadata.publishedTime);\n\n    return metadata;\n  },\n\n  /**\n   * Check if node is image, or if node contains exactly only one image\n   * whether as a direct child or as its descendants.\n   *\n   * @param Element\n   **/\n  _isSingleImage(node) {\n    while (node) {\n      if (node.tagName === \"IMG\") {\n        return true;\n      }\n      if (node.children.length !== 1 || node.textContent.trim() !== \"\") {\n        return false;\n      }\n      node = node.children[0];\n    }\n    return false;\n  },\n\n  /**\n   * Find all <noscript> that are located after <img> nodes, and which contain only one\n   * <img> element. Replace the first image with the image from inside the <noscript> tag,\n   * and remove the <noscript> tag. This improves the quality of the images we use on\n   * some sites (e.g. Medium).\n   *\n   * @param Element\n   **/\n  _unwrapNoscriptImages(doc) {\n    // Find img without source or attributes that might contains image, and remove it.\n    // This is done to prevent a placeholder img is replaced by img from noscript in next step.\n    var imgs = Array.from(doc.getElementsByTagName(\"img\"));\n    this._forEachNode(imgs, function (img) {\n      for (var i = 0; i < img.attributes.length; i++) {\n        var attr = img.attributes[i];\n        switch (attr.name) {\n          case \"src\":\n          case \"srcset\":\n          case \"data-src\":\n          case \"data-srcset\":\n            return;\n        }\n\n        if (/\\.(jpg|jpeg|png|webp)/i.test(attr.value)) {\n          return;\n        }\n      }\n\n      img.remove();\n    });\n\n    // Next find noscript and try to extract its image\n    var noscripts = Array.from(doc.getElementsByTagName(\"noscript\"));\n    this._forEachNode(noscripts, function (noscript) {\n      // Parse content of noscript and make sure it only contains image\n      if (!this._isSingleImage(noscript)) {\n        return;\n      }\n      var tmp = doc.createElement(\"div\");\n      // We're running in the document context, and using unmodified\n      // document contents, so doing this should be safe.\n      // (Also we heavily discourage people from allowing script to\n      // run at all in this document...)\n      // eslint-disable-next-line no-unsanitized/property\n      tmp.innerHTML = noscript.innerHTML;\n\n      // If noscript has previous sibling and it only contains image,\n      // replace it with noscript content. However we also keep old\n      // attributes that might contains image.\n      var prevElement = noscript.previousElementSibling;\n      if (prevElement && this._isSingleImage(prevElement)) {\n        var prevImg = prevElement;\n        if (prevImg.tagName !== \"IMG\") {\n          prevImg = prevElement.getElementsByTagName(\"img\")[0];\n        }\n\n        var newImg = tmp.getElementsByTagName(\"img\")[0];\n        for (var i = 0; i < prevImg.attributes.length; i++) {\n          var attr = prevImg.attributes[i];\n          if (attr.value === \"\") {\n            continue;\n          }\n\n          if (\n            attr.name === \"src\" ||\n            attr.name === \"srcset\" ||\n            /\\.(jpg|jpeg|png|webp)/i.test(attr.value)\n          ) {\n            if (newImg.getAttribute(attr.name) === attr.value) {\n              continue;\n            }\n\n            var attrName = attr.name;\n            if (newImg.hasAttribute(attrName)) {\n              attrName = \"data-old-\" + attrName;\n            }\n\n            newImg.setAttribute(attrName, attr.value);\n          }\n        }\n\n        noscript.parentNode.replaceChild(tmp.firstElementChild, prevElement);\n      }\n    });\n  },\n\n  /**\n   * Removes script tags from the document.\n   *\n   * @param Element\n   **/\n  _removeScripts(doc) {\n    this._removeNodes(this._getAllNodesWithTag(doc, [\"script\", \"noscript\"]));\n  },\n\n  /**\n   * Check if this node has only whitespace and a single element with given tag\n   * Returns false if the DIV node contains non-empty text nodes\n   * or if it contains no element with given tag or more than 1 element.\n   *\n   * @param Element\n   * @param string tag of child element\n   **/\n  _hasSingleTagInsideElement(element, tag) {\n    // There should be exactly 1 element child with given tag\n    if (element.children.length != 1 || element.children[0].tagName !== tag) {\n      return false;\n    }\n\n    // And there should be no text nodes with real content\n    return !this._someNode(element.childNodes, function (node) {\n      return (\n        node.nodeType === this.TEXT_NODE &&\n        this.REGEXPS.hasContent.test(node.textContent)\n      );\n    });\n  },\n\n  _isElementWithoutContent(node) {\n    return (\n      node.nodeType === this.ELEMENT_NODE &&\n      !node.textContent.trim().length &&\n      (!node.children.length ||\n        node.children.length ==\n          node.getElementsByTagName(\"br\").length +\n            node.getElementsByTagName(\"hr\").length)\n    );\n  },\n\n  /**\n   * Determine whether element has any children block level elements.\n   *\n   * @param Element\n   */\n  _hasChildBlockElement(element) {\n    return this._someNode(element.childNodes, function (node) {\n      return (\n        this.DIV_TO_P_ELEMS.has(node.tagName) ||\n        this._hasChildBlockElement(node)\n      );\n    });\n  },\n\n  /***\n   * Determine if a node qualifies as phrasing content.\n   * https://developer.mozilla.org/en-US/docs/Web/Guide/HTML/Content_categories#Phrasing_content\n   **/\n  _isPhrasingContent(node) {\n    return (\n      node.nodeType === this.TEXT_NODE ||\n      this.PHRASING_ELEMS.includes(node.tagName) ||\n      ((node.tagName === \"A\" ||\n        node.tagName === \"DEL\" ||\n        node.tagName === \"INS\") &&\n        this._everyNode(node.childNodes, this._isPhrasingContent))\n    );\n  },\n\n  _isWhitespace(node) {\n    return (\n      (node.nodeType === this.TEXT_NODE &&\n        node.textContent.trim().length === 0) ||\n      (node.nodeType === this.ELEMENT_NODE && node.tagName === \"BR\")\n    );\n  },\n\n  /**\n   * Get the inner text of a node - cross browser compatibly.\n   * This also strips out any excess whitespace to be found.\n   *\n   * @param Element\n   * @param Boolean normalizeSpaces (default: true)\n   * @return string\n   **/\n  _getInnerText(e, normalizeSpaces) {\n    normalizeSpaces =\n      typeof normalizeSpaces === \"undefined\" ? true : normalizeSpaces;\n    var textContent = e.textContent.trim();\n\n    if (normalizeSpaces) {\n      return textContent.replace(this.REGEXPS.normalize, \" \");\n    }\n    return textContent;\n  },\n\n  /**\n   * Get the number of times a string s appears in the node e.\n   *\n   * @param Element\n   * @param string - what to split on. Default is \",\"\n   * @return number (integer)\n   **/\n  _getCharCount(e, s) {\n    s = s || \",\";\n    return this._getInnerText(e).split(s).length - 1;\n  },\n\n  /**\n   * Remove the style attribute on every e and under.\n   * TODO: Test if getElementsByTagName(*) is faster.\n   *\n   * @param Element\n   * @return void\n   **/\n  _cleanStyles(e) {\n    if (!e || e.tagName.toLowerCase() === \"svg\") {\n      return;\n    }\n\n    // Remove `style` and deprecated presentational attributes\n    for (var i = 0; i < this.PRESENTATIONAL_ATTRIBUTES.length; i++) {\n      e.removeAttribute(this.PRESENTATIONAL_ATTRIBUTES[i]);\n    }\n\n    if (this.DEPRECATED_SIZE_ATTRIBUTE_ELEMS.includes(e.tagName)) {\n      e.removeAttribute(\"width\");\n      e.removeAttribute(\"height\");\n    }\n\n    var cur = e.firstElementChild;\n    while (cur !== null) {\n      this._cleanStyles(cur);\n      cur = cur.nextElementSibling;\n    }\n  },\n\n  /**\n   * Get the density of links as a percentage of the content\n   * This is the amount of text that is inside a link divided by the total text in the node.\n   *\n   * @param Element\n   * @return number (float)\n   **/\n  _getLinkDensity(element) {\n    var textLength = this._getInnerText(element).length;\n    if (textLength === 0) {\n      return 0;\n    }\n\n    var linkLength = 0;\n\n    // XXX implement _reduceNodeList?\n    this._forEachNode(element.getElementsByTagName(\"a\"), function (linkNode) {\n      var href = linkNode.getAttribute(\"href\");\n      var coefficient = href && this.REGEXPS.hashUrl.test(href) ? 0.3 : 1;\n      linkLength += this._getInnerText(linkNode).length * coefficient;\n    });\n\n    return linkLength / textLength;\n  },\n\n  /**\n   * Get an elements class/id weight. Uses regular expressions to tell if this\n   * element looks good or bad.\n   *\n   * @param Element\n   * @return number (Integer)\n   **/\n  _getClassWeight(e) {\n    if (!this._flagIsActive(this.FLAG_WEIGHT_CLASSES)) {\n      return 0;\n    }\n\n    var weight = 0;\n\n    // Look for a special classname\n    if (typeof e.className === \"string\" && e.className !== \"\") {\n      if (this.REGEXPS.negative.test(e.className)) {\n        weight -= 25;\n      }\n\n      if (this.REGEXPS.positive.test(e.className)) {\n        weight += 25;\n      }\n    }\n\n    // Look for a special ID\n    if (typeof e.id === \"string\" && e.id !== \"\") {\n      if (this.REGEXPS.negative.test(e.id)) {\n        weight -= 25;\n      }\n\n      if (this.REGEXPS.positive.test(e.id)) {\n        weight += 25;\n      }\n    }\n\n    return weight;\n  },\n\n  /**\n   * Clean a node of all elements of type \"tag\".\n   * (Unless it's a youtube/vimeo video. People love movies.)\n   *\n   * @param Element\n   * @param string tag to clean\n   * @return void\n   **/\n  _clean(e, tag) {\n    var isEmbed = [\"object\", \"embed\", \"iframe\"].includes(tag);\n\n    this._removeNodes(this._getAllNodesWithTag(e, [tag]), function (element) {\n      // Allow youtube and vimeo videos through as people usually want to see those.\n      if (isEmbed) {\n        // First, check the elements attributes to see if any of them contain youtube or vimeo\n        for (var i = 0; i < element.attributes.length; i++) {\n          if (this._allowedVideoRegex.test(element.attributes[i].value)) {\n            return false;\n          }\n        }\n\n        // For embed with <object> tag, check inner HTML as well.\n        if (\n          element.tagName === \"object\" &&\n          this._allowedVideoRegex.test(element.innerHTML)\n        ) {\n          return false;\n        }\n      }\n\n      return true;\n    });\n  },\n\n  /**\n   * Check if a given node has one of its ancestor tag name matching the\n   * provided one.\n   * @param  HTMLElement node\n   * @param  String      tagName\n   * @param  Number      maxDepth\n   * @param  Function    filterFn a filter to invoke to determine whether this node 'counts'\n   * @return Boolean\n   */\n  _hasAncestorTag(node, tagName, maxDepth, filterFn) {\n    maxDepth = maxDepth || 3;\n    tagName = tagName.toUpperCase();\n    var depth = 0;\n    while (node.parentNode) {\n      if (maxDepth > 0 && depth > maxDepth) {\n        return false;\n      }\n      if (\n        node.parentNode.tagName === tagName &&\n        (!filterFn || filterFn(node.parentNode))\n      ) {\n        return true;\n      }\n      node = node.parentNode;\n      depth++;\n    }\n    return false;\n  },\n\n  /**\n   * Return an object indicating how many rows and columns this table has.\n   */\n  _getRowAndColumnCount(table) {\n    var rows = 0;\n    var columns = 0;\n    var trs = table.getElementsByTagName(\"tr\");\n    for (var i = 0; i < trs.length; i++) {\n      var rowspan = trs[i].getAttribute(\"rowspan\") || 0;\n      if (rowspan) {\n        rowspan = parseInt(rowspan, 10);\n      }\n      rows += rowspan || 1;\n\n      // Now look for column-related info\n      var columnsInThisRow = 0;\n      var cells = trs[i].getElementsByTagName(\"td\");\n      for (var j = 0; j < cells.length; j++) {\n        var colspan = cells[j].getAttribute(\"colspan\") || 0;\n        if (colspan) {\n          colspan = parseInt(colspan, 10);\n        }\n        columnsInThisRow += colspan || 1;\n      }\n      columns = Math.max(columns, columnsInThisRow);\n    }\n    return { rows, columns };\n  },\n\n  /**\n   * Look for 'data' (as opposed to 'layout') tables, for which we use\n   * similar checks as\n   * https://searchfox.org/mozilla-central/rev/f82d5c549f046cb64ce5602bfd894b7ae807c8f8/accessible/generic/TableAccessible.cpp#19\n   */\n  _markDataTables(root) {\n    var tables = root.getElementsByTagName(\"table\");\n    for (var i = 0; i < tables.length; i++) {\n      var table = tables[i];\n      var role = table.getAttribute(\"role\");\n      if (role == \"presentation\") {\n        table._readabilityDataTable = false;\n        continue;\n      }\n      var datatable = table.getAttribute(\"datatable\");\n      if (datatable == \"0\") {\n        table._readabilityDataTable = false;\n        continue;\n      }\n      var summary = table.getAttribute(\"summary\");\n      if (summary) {\n        table._readabilityDataTable = true;\n        continue;\n      }\n\n      var caption = table.getElementsByTagName(\"caption\")[0];\n      if (caption && caption.childNodes.length) {\n        table._readabilityDataTable = true;\n        continue;\n      }\n\n      // If the table has a descendant with any of these tags, consider a data table:\n      var dataTableDescendants = [\"col\", \"colgroup\", \"tfoot\", \"thead\", \"th\"];\n      var descendantExists = function (tag) {\n        return !!table.getElementsByTagName(tag)[0];\n      };\n      if (dataTableDescendants.some(descendantExists)) {\n        this.log(\"Data table because found data-y descendant\");\n        table._readabilityDataTable = true;\n        continue;\n      }\n\n      // Nested tables indicate a layout table:\n      if (table.getElementsByTagName(\"table\")[0]) {\n        table._readabilityDataTable = false;\n        continue;\n      }\n\n      var sizeInfo = this._getRowAndColumnCount(table);\n\n      if (sizeInfo.columns == 1 || sizeInfo.rows == 1) {\n        // single colum/row tables are commonly used for page layout purposes.\n        table._readabilityDataTable = false;\n        continue;\n      }\n\n      if (sizeInfo.rows >= 10 || sizeInfo.columns > 4) {\n        table._readabilityDataTable = true;\n        continue;\n      }\n      // Now just go by size entirely:\n      table._readabilityDataTable = sizeInfo.rows * sizeInfo.columns > 10;\n    }\n  },\n\n  /* convert images and figures that have properties like data-src into images that can be loaded without JS */\n  _fixLazyImages(root) {\n    this._forEachNode(\n      this._getAllNodesWithTag(root, [\"img\", \"picture\", \"figure\"]),\n      function (elem) {\n        // In some sites (e.g. Kotaku), they put 1px square image as base64 data uri in the src attribute.\n        // So, here we check if the data uri is too short, just might as well remove it.\n        if (elem.src && this.REGEXPS.b64DataUrl.test(elem.src)) {\n          // Make sure it's not SVG, because SVG can have a meaningful image in under 133 bytes.\n          var parts = this.REGEXPS.b64DataUrl.exec(elem.src);\n          if (parts[1] === \"image/svg+xml\") {\n            return;\n          }\n\n          // Make sure this element has other attributes which contains image.\n          // If it doesn't, then this src is important and shouldn't be removed.\n          var srcCouldBeRemoved = false;\n          for (var i = 0; i < elem.attributes.length; i++) {\n            var attr = elem.attributes[i];\n            if (attr.name === \"src\") {\n              continue;\n            }\n\n            if (/\\.(jpg|jpeg|png|webp)/i.test(attr.value)) {\n              srcCouldBeRemoved = true;\n              break;\n            }\n          }\n\n          // Here we assume if image is less than 100 bytes (or 133 after encoded to base64)\n          // it will be too small, therefore it might be placeholder image.\n          if (srcCouldBeRemoved) {\n            var b64starts = parts[0].length;\n            var b64length = elem.src.length - b64starts;\n            if (b64length < 133) {\n              elem.removeAttribute(\"src\");\n            }\n          }\n        }\n\n        // also check for \"null\" to work around https://github.com/jsdom/jsdom/issues/2580\n        if (\n          (elem.src || (elem.srcset && elem.srcset != \"null\")) &&\n          !elem.className.toLowerCase().includes(\"lazy\")\n        ) {\n          return;\n        }\n\n        for (var j = 0; j < elem.attributes.length; j++) {\n          attr = elem.attributes[j];\n          if (\n            attr.name === \"src\" ||\n            attr.name === \"srcset\" ||\n            attr.name === \"alt\"\n          ) {\n            continue;\n          }\n          var copyTo = null;\n          if (/\\.(jpg|jpeg|png|webp)\\s+\\d/.test(attr.value)) {\n            copyTo = \"srcset\";\n          } else if (/^\\s*\\S+\\.(jpg|jpeg|png|webp)\\S*\\s*$/.test(attr.value)) {\n            copyTo = \"src\";\n          }\n          if (copyTo) {\n            //if this is an img or picture, set the attribute directly\n            if (elem.tagName === \"IMG\" || elem.tagName === \"PICTURE\") {\n              elem.setAttribute(copyTo, attr.value);\n            } else if (\n              elem.tagName === \"FIGURE\" &&\n              !this._getAllNodesWithTag(elem, [\"img\", \"picture\"]).length\n            ) {\n              //if the item is a <figure> that does not contain an image or picture, create one and place it inside the figure\n              //see the nytimes-3 testcase for an example\n              var img = this._doc.createElement(\"img\");\n              img.setAttribute(copyTo, attr.value);\n              elem.appendChild(img);\n            }\n          }\n        }\n      }\n    );\n  },\n\n  _getTextDensity(e, tags) {\n    var textLength = this._getInnerText(e, true).length;\n    if (textLength === 0) {\n      return 0;\n    }\n    var childrenLength = 0;\n    var children = this._getAllNodesWithTag(e, tags);\n    this._forEachNode(\n      children,\n      child => (childrenLength += this._getInnerText(child, true).length)\n    );\n    return childrenLength / textLength;\n  },\n\n  /**\n   * Clean an element of all tags of type \"tag\" if they look fishy.\n   * \"Fishy\" is an algorithm based on content length, classnames, link density, number of images & embeds, etc.\n   *\n   * @return void\n   **/\n  _cleanConditionally(e, tag) {\n    if (!this._flagIsActive(this.FLAG_CLEAN_CONDITIONALLY)) {\n      return;\n    }\n\n    // Gather counts for other typical elements embedded within.\n    // Traverse backwards so we can remove nodes at the same time\n    // without effecting the traversal.\n    //\n    // TODO: Consider taking into account original contentScore here.\n    this._removeNodes(this._getAllNodesWithTag(e, [tag]), function (node) {\n      // First check if this node IS data table, in which case don't remove it.\n      var isDataTable = function (t) {\n        return t._readabilityDataTable;\n      };\n\n      var isList = tag === \"ul\" || tag === \"ol\";\n      if (!isList) {\n        var listLength = 0;\n        var listNodes = this._getAllNodesWithTag(node, [\"ul\", \"ol\"]);\n        this._forEachNode(\n          listNodes,\n          list => (listLength += this._getInnerText(list).length)\n        );\n        isList = listLength / this._getInnerText(node).length > 0.9;\n      }\n\n      if (tag === \"table\" && isDataTable(node)) {\n        return false;\n      }\n\n      // Next check if we're inside a data table, in which case don't remove it as well.\n      if (this._hasAncestorTag(node, \"table\", -1, isDataTable)) {\n        return false;\n      }\n\n      if (this._hasAncestorTag(node, \"code\")) {\n        return false;\n      }\n\n      // keep element if it has a data tables\n      if (\n        [...node.getElementsByTagName(\"table\")].some(\n          tbl => tbl._readabilityDataTable\n        )\n      ) {\n        return false;\n      }\n\n      var weight = this._getClassWeight(node);\n\n      this.log(\"Cleaning Conditionally\", node);\n\n      var contentScore = 0;\n\n      if (weight + contentScore < 0) {\n        return true;\n      }\n\n      if (this._getCharCount(node, \",\") < 10) {\n        // If there are not very many commas, and the number of\n        // non-paragraph elements is more than paragraphs or other\n        // ominous signs, remove the element.\n        var p = node.getElementsByTagName(\"p\").length;\n        var img = node.getElementsByTagName(\"img\").length;\n        var li = node.getElementsByTagName(\"li\").length - 100;\n        var input = node.getElementsByTagName(\"input\").length;\n        var headingDensity = this._getTextDensity(node, [\n          \"h1\",\n          \"h2\",\n          \"h3\",\n          \"h4\",\n          \"h5\",\n          \"h6\",\n        ]);\n\n        var embedCount = 0;\n        var embeds = this._getAllNodesWithTag(node, [\n          \"object\",\n          \"embed\",\n          \"iframe\",\n        ]);\n\n        for (var i = 0; i < embeds.length; i++) {\n          // If this embed has attribute that matches video regex, don't delete it.\n          for (var j = 0; j < embeds[i].attributes.length; j++) {\n            if (this._allowedVideoRegex.test(embeds[i].attributes[j].value)) {\n              return false;\n            }\n          }\n\n          // For embed with <object> tag, check inner HTML as well.\n          if (\n            embeds[i].tagName === \"object\" &&\n            this._allowedVideoRegex.test(embeds[i].innerHTML)\n          ) {\n            return false;\n          }\n\n          embedCount++;\n        }\n\n        var innerText = this._getInnerText(node);\n\n        // toss any node whose inner text contains nothing but suspicious words\n        if (\n          this.REGEXPS.adWords.test(innerText) ||\n          this.REGEXPS.loadingWords.test(innerText)\n        ) {\n          return true;\n        }\n\n        var contentLength = innerText.length;\n        var linkDensity = this._getLinkDensity(node);\n        var textishTags = [\"SPAN\", \"LI\", \"TD\"].concat(\n          Array.from(this.DIV_TO_P_ELEMS)\n        );\n        var textDensity = this._getTextDensity(node, textishTags);\n        var isFigureChild = this._hasAncestorTag(node, \"figure\");\n\n        // apply shadiness checks, then check for exceptions\n        const shouldRemoveNode = () => {\n          const errs = [];\n          if (!isFigureChild && img > 1 && p / img < 0.5) {\n            errs.push(`Bad p to img ratio (img=${img}, p=${p})`);\n          }\n          if (!isList && li > p) {\n            errs.push(`Too many li's outside of a list. (li=${li} > p=${p})`);\n          }\n          if (input > Math.floor(p / 3)) {\n            errs.push(`Too many inputs per p. (input=${input}, p=${p})`);\n          }\n          if (\n            !isList &&\n            !isFigureChild &&\n            headingDensity < 0.9 &&\n            contentLength < 25 &&\n            (img === 0 || img > 2) &&\n            linkDensity > 0\n          ) {\n            errs.push(\n              `Suspiciously short. (headingDensity=${headingDensity}, img=${img}, linkDensity=${linkDensity})`\n            );\n          }\n          if (\n            !isList &&\n            weight < 25 &&\n            linkDensity > 0.2 + this._linkDensityModifier\n          ) {\n            errs.push(\n              `Low weight and a little linky. (linkDensity=${linkDensity})`\n            );\n          }\n          if (weight >= 25 && linkDensity > 0.5 + this._linkDensityModifier) {\n            errs.push(\n              `High weight and mostly links. (linkDensity=${linkDensity})`\n            );\n          }\n          if ((embedCount === 1 && contentLength < 75) || embedCount > 1) {\n            errs.push(\n              `Suspicious embed. (embedCount=${embedCount}, contentLength=${contentLength})`\n            );\n          }\n          if (img === 0 && textDensity === 0) {\n            errs.push(\n              `No useful content. (img=${img}, textDensity=${textDensity})`\n            );\n          }\n\n          if (errs.length) {\n            this.log(\"Checks failed\", errs);\n            return true;\n          }\n\n          return false;\n        };\n\n        var haveToRemove = shouldRemoveNode();\n\n        // Allow simple lists of images to remain in pages\n        if (isList && haveToRemove) {\n          for (var x = 0; x < node.children.length; x++) {\n            let child = node.children[x];\n            // Don't filter in lists with li's that contain more than one child\n            if (child.children.length > 1) {\n              return haveToRemove;\n            }\n          }\n          let li_count = node.getElementsByTagName(\"li\").length;\n          // Only allow the list to remain if every li contains an image\n          if (img == li_count) {\n            return false;\n          }\n        }\n        return haveToRemove;\n      }\n      return false;\n    });\n  },\n\n  /**\n   * Clean out elements that match the specified conditions\n   *\n   * @param Element\n   * @param Function determines whether a node should be removed\n   * @return void\n   **/\n  _cleanMatchedNodes(e, filter) {\n    var endOfSearchMarkerNode = this._getNextNode(e, true);\n    var next = this._getNextNode(e);\n    while (next && next != endOfSearchMarkerNode) {\n      if (filter.call(this, next, next.className + \" \" + next.id)) {\n        next = this._removeAndGetNext(next);\n      } else {\n        next = this._getNextNode(next);\n      }\n    }\n  },\n\n  /**\n   * Clean out spurious headers from an Element.\n   *\n   * @param Element\n   * @return void\n   **/\n  _cleanHeaders(e) {\n    let headingNodes = this._getAllNodesWithTag(e, [\"h1\", \"h2\"]);\n    this._removeNodes(headingNodes, function (node) {\n      let shouldRemove = this._getClassWeight(node) < 0;\n      if (shouldRemove) {\n        this.log(\"Removing header with low class weight:\", node);\n      }\n      return shouldRemove;\n    });\n  },\n\n  /**\n   * Check if this node is an H1 or H2 element whose content is mostly\n   * the same as the article title.\n   *\n   * @param Element  the node to check.\n   * @return boolean indicating whether this is a title-like header.\n   */\n  _headerDuplicatesTitle(node) {\n    if (node.tagName != \"H1\" && node.tagName != \"H2\") {\n      return false;\n    }\n    var heading = this._getInnerText(node, false);\n    this.log(\"Evaluating similarity of header:\", heading, this._articleTitle);\n    return this._textSimilarity(this._articleTitle, heading) > 0.75;\n  },\n\n  _flagIsActive(flag) {\n    return (this._flags & flag) > 0;\n  },\n\n  _removeFlag(flag) {\n    this._flags = this._flags & ~flag;\n  },\n\n  _isProbablyVisible(node) {\n    // Have to null-check node.style and node.className.includes to deal with SVG and MathML nodes.\n    return (\n      (!node.style || node.style.display != \"none\") &&\n      (!node.style || node.style.visibility != \"hidden\") &&\n      !node.hasAttribute(\"hidden\") &&\n      //check for \"fallback-image\" so that wikimedia math images are displayed\n      (!node.hasAttribute(\"aria-hidden\") ||\n        node.getAttribute(\"aria-hidden\") != \"true\" ||\n        (node.className &&\n          node.className.includes &&\n          node.className.includes(\"fallback-image\")))\n    );\n  },\n\n  /**\n   * Runs readability.\n   *\n   * Workflow:\n   *  1. Prep the document by removing script tags, css, etc.\n   *  2. Build readability's DOM tree.\n   *  3. Grab the article content from the current dom tree.\n   *  4. Replace the current DOM tree with the new one.\n   *  5. Read peacefully.\n   *\n   * @return void\n   **/\n  parse() {\n    // Avoid parsing too large documents, as per configuration option\n    if (this._maxElemsToParse > 0) {\n      var numTags = this._doc.getElementsByTagName(\"*\").length;\n      if (numTags > this._maxElemsToParse) {\n        throw new Error(\n          \"Aborting parsing document; \" + numTags + \" elements found\"\n        );\n      }\n    }\n\n    // Unwrap image from noscript\n    this._unwrapNoscriptImages(this._doc);\n\n    // Extract JSON-LD metadata before removing scripts\n    var jsonLd = this._disableJSONLD ? {} : this._getJSONLD(this._doc);\n\n    // Remove script tags from the document.\n    this._removeScripts(this._doc);\n\n    this._prepDocument();\n\n    var metadata = this._getArticleMetadata(jsonLd);\n    this._metadata = metadata;\n    this._articleTitle = metadata.title;\n\n    var articleContent = this._grabArticle();\n    if (!articleContent) {\n      return null;\n    }\n\n    this.log(\"Grabbed: \" + articleContent.innerHTML);\n\n    this._postProcessContent(articleContent);\n\n    // If we haven't found an excerpt in the article's metadata, use the article's\n    // first paragraph as the excerpt. This is used for displaying a preview of\n    // the article's content.\n    if (!metadata.excerpt) {\n      var paragraphs = articleContent.getElementsByTagName(\"p\");\n      if (paragraphs.length) {\n        metadata.excerpt = paragraphs[0].textContent.trim();\n      }\n    }\n\n    var textContent = articleContent.textContent;\n    return {\n      title: this._articleTitle,\n      byline: metadata.byline || this._articleByline,\n      dir: this._articleDir,\n      lang: this._articleLang,\n      content: this._serializer(articleContent),\n      textContent,\n      length: textContent.length,\n      excerpt: metadata.excerpt,\n      siteName: metadata.siteName || this._articleSiteName,\n      publishedTime: metadata.publishedTime,\n    };\n  },\n};\n\nif (typeof module === \"object\") {\n  /* eslint-disable-next-line no-redeclare */\n  /* global module */\n  module.exports = Readability;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GAED;;;CAGC,GAED;;;;CAIC,GACD,SAAS,YAAY,GAAG,EAAE,OAAO;IAC/B,2EAA2E;IAC3E,IAAI,WAAW,QAAQ,eAAe,EAAE;QACtC,MAAM;QACN,UAAU,SAAS,CAAC,EAAE;IACxB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,eAAe,EAAE;QACvC,MAAM,IAAI,MACR;IAEJ;IACA,UAAU,WAAW,CAAC;IAEtB,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe;IAC3D,IAAI,CAAC,aAAa,GAAG;IACrB,IAAI,CAAC,cAAc,GAAG;IACtB,IAAI,CAAC,WAAW,GAAG;IACnB,IAAI,CAAC,gBAAgB,GAAG;IACxB,IAAI,CAAC,SAAS,GAAG,EAAE;IACnB,IAAI,CAAC,SAAS,GAAG,CAAC;IAElB,uBAAuB;IACvB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,QAAQ,KAAK;IAC7B,IAAI,CAAC,gBAAgB,GACnB,QAAQ,eAAe,IAAI,IAAI,CAAC,0BAA0B;IAC5D,IAAI,CAAC,gBAAgB,GACnB,QAAQ,eAAe,IAAI,IAAI,CAAC,wBAAwB;IAC1D,IAAI,CAAC,cAAc,GAAG,QAAQ,aAAa,IAAI,IAAI,CAAC,sBAAsB;IAC1E,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CACvD,QAAQ,iBAAiB,IAAI,EAAE;IAEjC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,QAAQ,WAAW;IACzC,IAAI,CAAC,WAAW,GACd,QAAQ,UAAU,IAClB,SAAU,EAAE;QACV,OAAO,GAAG,SAAS;IACrB;IACF,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,QAAQ,aAAa;IAC7C,IAAI,CAAC,kBAAkB,GAAG,QAAQ,iBAAiB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM;IAC1E,IAAI,CAAC,oBAAoB,GAAG,QAAQ,mBAAmB,IAAI;IAE3D,2BAA2B;IAC3B,IAAI,CAAC,MAAM,GACT,IAAI,CAAC,oBAAoB,GACzB,IAAI,CAAC,mBAAmB,GACxB,IAAI,CAAC,wBAAwB;IAE/B,uDAAuD;IACvD,IAAI,IAAI,CAAC,MAAM,EAAE;QACf,IAAI,UAAU,SAAU,IAAI;YAC1B,IAAI,KAAK,QAAQ,IAAI,KAAK,SAAS,EAAE;gBACnC,OAAO,GAAG,KAAK,QAAQ,CAAC,GAAG,EAAE,KAAK,WAAW,CAAC,EAAE,CAAC;YACnD;YACA,IAAI,YAAY,MAAM,IAAI,CAAC,KAAK,UAAU,IAAI,EAAE,EAAE,SAAU,IAAI;gBAC9D,OAAO,GAAG,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC;YACvC,GAAG,IAAI,CAAC;YACR,OAAO,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;QAC3C;QACA,IAAI,CAAC,GAAG,GAAG;YACT,IAAI,OAAO,YAAY,aAAa;gBAClC,IAAI,OAAO,MAAM,IAAI,CAAC,WAAW,CAAA;oBAC/B,IAAI,OAAO,IAAI,QAAQ,IAAI,IAAI,CAAC,YAAY,EAAE;wBAC5C,OAAO,QAAQ;oBACjB;oBACA,OAAO;gBACT;gBACA,KAAK,OAAO,CAAC;gBACb,sCAAsC;gBACtC,QAAQ,GAAG,IAAI;YACjB,OAAO,IAAI,OAAO,SAAS,aAAa;gBACtC,eAAe,GACf,IAAI,MAAM,MAAM,SAAS,CAAC,GAAG,CAC1B,IAAI,CAAC,WAAW,SAAU,CAAC;oBAC1B,OAAO,KAAK,EAAE,QAAQ,GAAG,QAAQ,KAAK;gBACxC,GACC,IAAI,CAAC;gBACR,KAAK,2BAA2B,MAAM;YACxC;QACF;IACF,OAAO;QACL,IAAI,CAAC,GAAG,GAAG,YAAa;IAC1B;AACF;AAEA,YAAY,SAAS,GAAG;IACtB,sBAAsB;IACtB,qBAAqB;IACrB,0BAA0B;IAE1B,iEAAiE;IACjE,cAAc;IACd,WAAW;IAEX,sEAAsE;IACtE,4BAA4B;IAE5B,8DAA8D;IAC9D,6CAA6C;IAC7C,0BAA0B;IAE1B,oCAAoC;IACpC,uBAAuB,kCACpB,WAAW,GACX,KAAK,CAAC;IAET,+EAA+E;IAC/E,wBAAwB;IAExB,4DAA4D;IAC5D,oEAAoE;IACpE,SAAS;QACP,wDAAwD;QACxD,8DAA8D;QAC9D,oBACE;QACF,sBAAsB;QAEtB,UACE;QACF,UACE;QACF,YACE;QACF,QAAQ;QACR,cAAc;QACd,WAAW;QACX,QACE;QACF,eAAe;QACf,UAAU;QACV,UAAU;QACV,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,SAAS;QACT,WAAW;QACX,YAAY;QACZ,sEAAsE;QACtE,0DAA0D;QAC1D,QAAQ;QACR,kCAAkC;QAClC,oBACE;QACF,kGAAkG;QAClG,SACE;QACF,cACE;IACJ;IAEA,gBAAgB;QACd;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,gBAAgB,IAAI,IAAI;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,yBAAyB;QAAC;QAAO;QAAW;QAAW;QAAK;QAAM;KAAK;IAEvE,2BAA2B;QACzB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,iCAAiC;QAAC;QAAS;QAAM;QAAM;QAAM;KAAM;IAEnE,wEAAwE;IACxE,2EAA2E;IAC3E,gBAAgB;QACd,sCAAsC;QACtC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,sDAAsD;IACtD,qBAAqB;QAAC;KAAO;IAE7B,+DAA+D;IAC/D,iBAAiB;QACf,IAAI;QACJ,IAAI;QACJ,KAAK;QACL,MAAM;QACN,MAAM;IACR;IAEA;;;;;IAKE,GACF,qBAAoB,cAAc;QAChC,6EAA6E;QAC7E,IAAI,CAAC,gBAAgB,CAAC;QAEtB,IAAI,CAAC,uBAAuB,CAAC;QAE7B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,kBAAkB;YAClB,IAAI,CAAC,aAAa,CAAC;QACrB;IACF;IAEA;;;;;;;;;GASC,GACD,cAAa,QAAQ,EAAE,QAAQ;QAC7B,2CAA2C;QAC3C,IAAI,IAAI,CAAC,eAAe,IAAI,SAAS,eAAe,EAAE;YACpD,MAAM,IAAI,MAAM;QAClB;QACA,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YAC7C,IAAI,OAAO,QAAQ,CAAC,EAAE;YACtB,IAAI,aAAa,KAAK,UAAU;YAChC,IAAI,YAAY;gBACd,IAAI,CAAC,YAAY,SAAS,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,WAAW;oBACvD,WAAW,WAAW,CAAC;gBACzB;YACF;QACF;IACF;IAEA;;;;;;GAMC,GACD,kBAAiB,QAAQ,EAAE,UAAU;QACnC,2CAA2C;QAC3C,IAAI,IAAI,CAAC,eAAe,IAAI,SAAS,eAAe,EAAE;YACpD,MAAM,IAAI,MAAM;QAClB;QACA,KAAK,MAAM,QAAQ,SAAU;YAC3B,IAAI,CAAC,WAAW,CAAC,MAAM;QACzB;IACF;IAEA;;;;;;;;;;GAUC,GACD,cAAa,QAAQ,EAAE,EAAE;QACvB,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI;IACjD;IAEA;;;;;;;;;;GAUC,GACD,WAAU,QAAQ,EAAE,EAAE;QACpB,OAAO,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI;IACrD;IAEA;;;;;;;;;;GAUC,GACD,WAAU,QAAQ,EAAE,EAAE;QACpB,OAAO,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI;IACrD;IAEA;;;;;;;;;;GAUC,GACD,YAAW,QAAQ,EAAE,EAAE;QACrB,OAAO,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI;IACtD;IAEA,qBAAoB,IAAI,EAAE,QAAQ;QAChC,IAAI,KAAK,gBAAgB,EAAE;YACzB,OAAO,KAAK,gBAAgB,CAAC,SAAS,IAAI,CAAC;QAC7C;QACA,OAAO,EAAE,CAAC,MAAM,CAAC,KAAK,CACpB,EAAE,EACF,SAAS,GAAG,CAAC,SAAU,GAAG;YACxB,IAAI,aAAa,KAAK,oBAAoB,CAAC;YAC3C,OAAO,MAAM,OAAO,CAAC,cAAc,aAAa,MAAM,IAAI,CAAC;QAC7D;IAEJ;IAEA;;;;;;;GAOC,GACD,eAAc,IAAI;QAChB,IAAI,oBAAoB,IAAI,CAAC,kBAAkB;QAC/C,IAAI,YAAY,CAAC,KAAK,YAAY,CAAC,YAAY,EAAE,EAC9C,KAAK,CAAC,OACN,MAAM,CAAC,CAAA,MAAO,kBAAkB,QAAQ,CAAC,MACzC,IAAI,CAAC;QAER,IAAI,WAAW;YACb,KAAK,YAAY,CAAC,SAAS;QAC7B,OAAO;YACL,KAAK,eAAe,CAAC;QACvB;QAEA,IAAK,OAAO,KAAK,iBAAiB,EAAE,MAAM,OAAO,KAAK,kBAAkB,CAAE;YACxE,IAAI,CAAC,aAAa,CAAC;QACrB;IACF;IAEA;;;;;GAKC,GACD,QAAO,GAAG;QACR,IAAI;YACF,IAAI,IAAI;YACR,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;IACA;;;;;;GAMC,GACD,kBAAiB,cAAc;QAC7B,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO;QAC/B,IAAI,cAAc,IAAI,CAAC,IAAI,CAAC,WAAW;QACvC,SAAS,cAAc,GAAG;YACxB,mEAAmE;YACnE,IAAI,WAAW,eAAe,IAAI,MAAM,CAAC,MAAM,KAAK;gBAClD,OAAO;YACT;YAEA,uCAAuC;YACvC,IAAI;gBACF,OAAO,IAAI,IAAI,KAAK,SAAS,IAAI;YACnC,EAAE,OAAO,IAAI;YACX,kDAAkD;YACpD;YACA,OAAO;QACT;QAEA,IAAI,QAAQ,IAAI,CAAC,mBAAmB,CAAC,gBAAgB;YAAC;SAAI;QAC1D,IAAI,CAAC,YAAY,CAAC,OAAO,SAAU,IAAI;YACrC,IAAI,OAAO,KAAK,YAAY,CAAC;YAC7B,IAAI,MAAM;gBACR,4CAA4C;gBAC5C,iEAAiE;gBACjE,IAAI,KAAK,OAAO,CAAC,mBAAmB,GAAG;oBACrC,oFAAoF;oBACpF,IACE,KAAK,UAAU,CAAC,MAAM,KAAK,KAC3B,KAAK,UAAU,CAAC,EAAE,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,EAC9C;wBACA,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,WAAW;wBACpD,KAAK,UAAU,CAAC,YAAY,CAAC,MAAM;oBACrC,OAAO;wBACL,kEAAkE;wBAClE,IAAI,YAAY,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;wBACxC,MAAO,KAAK,UAAU,CAAE;4BACtB,UAAU,WAAW,CAAC,KAAK,UAAU;wBACvC;wBACA,KAAK,UAAU,CAAC,YAAY,CAAC,WAAW;oBAC1C;gBACF,OAAO;oBACL,KAAK,YAAY,CAAC,QAAQ,cAAc;gBAC1C;YACF;QACF;QAEA,IAAI,SAAS,IAAI,CAAC,mBAAmB,CAAC,gBAAgB;YACpD;YACA;YACA;YACA;YACA;YACA;SACD;QAED,IAAI,CAAC,YAAY,CAAC,QAAQ,SAAU,KAAK;YACvC,IAAI,MAAM,MAAM,YAAY,CAAC;YAC7B,IAAI,SAAS,MAAM,YAAY,CAAC;YAChC,IAAI,SAAS,MAAM,YAAY,CAAC;YAEhC,IAAI,KAAK;gBACP,MAAM,YAAY,CAAC,OAAO,cAAc;YAC1C;YAEA,IAAI,QAAQ;gBACV,MAAM,YAAY,CAAC,UAAU,cAAc;YAC7C;YAEA,IAAI,QAAQ;gBACV,IAAI,YAAY,OAAO,OAAO,CAC5B,IAAI,CAAC,OAAO,CAAC,SAAS,EACtB,SAAU,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;oBACrB,OAAO,cAAc,MAAM,CAAC,MAAM,EAAE,IAAI;gBAC1C;gBAGF,MAAM,YAAY,CAAC,UAAU;YAC/B;QACF;IACF;IAEA,yBAAwB,cAAc;QACpC,IAAI,OAAO;QAEX,MAAO,KAAM;YACX,IACE,KAAK,UAAU,IACf;gBAAC;gBAAO;aAAU,CAAC,QAAQ,CAAC,KAAK,OAAO,KACxC,CAAC,CAAC,KAAK,EAAE,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc,GAC9C;gBACA,IAAI,IAAI,CAAC,wBAAwB,CAAC,OAAO;oBACvC,OAAO,IAAI,CAAC,iBAAiB,CAAC;oBAC9B;gBACF,OAAO,IACL,IAAI,CAAC,0BAA0B,CAAC,MAAM,UACtC,IAAI,CAAC,0BAA0B,CAAC,MAAM,YACtC;oBACA,IAAI,QAAQ,KAAK,QAAQ,CAAC,EAAE;oBAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,UAAU,CAAC,MAAM,EAAE,IAAK;wBAC/C,MAAM,gBAAgB,CAAC,KAAK,UAAU,CAAC,EAAE,CAAC,SAAS;oBACrD;oBACA,KAAK,UAAU,CAAC,YAAY,CAAC,OAAO;oBACpC,OAAO;oBACP;gBACF;YACF;YAEA,OAAO,IAAI,CAAC,YAAY,CAAC;QAC3B;IACF;IAEA;;;;IAIE,GACF;QACE,IAAI,MAAM,IAAI,CAAC,IAAI;QACnB,IAAI,WAAW;QACf,IAAI,YAAY;QAEhB,IAAI;YACF,WAAW,YAAY,IAAI,KAAK,CAAC,IAAI;YAErC,uDAAuD;YACvD,IAAI,OAAO,aAAa,UAAU;gBAChC,WAAW,YAAY,IAAI,CAAC,aAAa,CACvC,IAAI,oBAAoB,CAAC,QAAQ,CAAC,EAAE;YAExC;QACF,EAAE,OAAO,GAAG;QACV,wCAAwC,GAC1C;QAEA,IAAI,iCAAiC;QACrC,SAAS,UAAU,GAAG;YACpB,OAAO,IAAI,KAAK,CAAC,OAAO,MAAM;QAChC;QAEA,mEAAmE;QACnE,IAAI,iBAAiB,IAAI,CAAC,WAAW;YACnC,iCAAiC,aAAa,IAAI,CAAC;YACnD,IAAI,gBAAgB,MAAM,IAAI,CAAC,UAAU,QAAQ,CAAC;YAClD,WAAW,UAAU,SAAS,CAAC,GAAG,cAAc,GAAG,GAAG,KAAK;YAE3D,sEAAsE;YACtE,IAAI,UAAU,YAAY,GAAG;gBAC3B,WAAW,UAAU,OAAO,CAAC,iCAAiC;YAChE;QACF,OAAO,IAAI,SAAS,QAAQ,CAAC,OAAO;YAClC,kEAAkE;YAClE,oCAAoC;YACpC,IAAI,WAAW,IAAI,CAAC,mBAAmB,CAAC,KAAK;gBAAC;gBAAM;aAAK;YACzD,IAAI,eAAe,SAAS,IAAI;YAChC,IAAI,QAAQ,IAAI,CAAC,SAAS,CAAC,UAAU,SAAU,OAAO;gBACpD,OAAO,QAAQ,WAAW,CAAC,IAAI,OAAO;YACxC;YAEA,yEAAyE;YACzE,IAAI,CAAC,OAAO;gBACV,WAAW,UAAU,SAAS,CAAC,UAAU,WAAW,CAAC,OAAO;gBAE5D,8DAA8D;gBAC9D,IAAI,UAAU,YAAY,GAAG;oBAC3B,WAAW,UAAU,SAAS,CAAC,UAAU,OAAO,CAAC,OAAO;gBACxD,yEAAyE;gBACzE,8EAA8E;gBAChF,OAAO,IAAI,UAAU,UAAU,MAAM,CAAC,GAAG,UAAU,OAAO,CAAC,SAAS,GAAG;oBACrE,WAAW;gBACb;YACF;QACF,OAAO,IAAI,SAAS,MAAM,GAAG,OAAO,SAAS,MAAM,GAAG,IAAI;YACxD,IAAI,QAAQ,IAAI,oBAAoB,CAAC;YAErC,IAAI,MAAM,MAAM,KAAK,GAAG;gBACtB,WAAW,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;YACxC;QACF;QAEA,WAAW,SAAS,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;QAC3D,8DAA8D;QAC9D,sEAAsE;QACtE,qEAAqE;QACrE,sBAAsB;QACtB,IAAI,oBAAoB,UAAU;QAClC,IACE,qBAAqB,KACrB,CAAC,CAAC,kCACA,qBACE,UAAU,UAAU,OAAO,CAAC,kBAAkB,OAAO,CAAC,GAC1D;YACA,WAAW;QACb;QAEA,OAAO;IACT;IAEA;;;;;IAKE,GACF;QACE,IAAI,MAAM,IAAI,CAAC,IAAI;QAEnB,gCAAgC;QAChC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK;YAAC;SAAQ;QAEzD,IAAI,IAAI,IAAI,EAAE;YACZ,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI;QAC3B;QAEA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK;YAAC;SAAO,GAAG;IACjE;IAEA;;;;GAIC,GACD,WAAU,IAAI;QACZ,IAAI,OAAO;QACX,MACE,QACA,KAAK,QAAQ,IAAI,IAAI,CAAC,YAAY,IAClC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,WAAW,EAC7C;YACA,OAAO,KAAK,WAAW;QACzB;QACA,OAAO;IACT;IAEA;;;;;;GAMC,GACD,aAAY,IAAI;QACd,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM;YAAC;SAAK,GAAG,SAAU,EAAE;YACpE,IAAI,OAAO,GAAG,WAAW;YAEzB,sEAAsE;YACtE,aAAa;YACb,IAAI,WAAW;YAEf,sEAAsE;YACtE,oEAAoE;YACpE,6CAA6C;YAC7C,MAAO,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK,KAAK,OAAO,IAAI,KAAM;gBAC5D,WAAW;gBACX,IAAI,YAAY,KAAK,WAAW;gBAChC,KAAK,MAAM;gBACX,OAAO;YACT;YAEA,yEAAyE;YACzE,qEAAqE;YACrE,SAAS;YACT,IAAI,UAAU;gBACZ,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;gBAChC,GAAG,UAAU,CAAC,YAAY,CAAC,GAAG;gBAE9B,OAAO,EAAE,WAAW;gBACpB,MAAO,KAAM;oBACX,yEAAyE;oBACzE,IAAI,KAAK,OAAO,IAAI,MAAM;wBACxB,IAAI,WAAW,IAAI,CAAC,SAAS,CAAC,KAAK,WAAW;wBAC9C,IAAI,YAAY,SAAS,OAAO,IAAI,MAAM;4BACxC;wBACF;oBACF;oBAEA,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO;wBAClC;oBACF;oBAEA,oDAAoD;oBACpD,IAAI,UAAU,KAAK,WAAW;oBAC9B,EAAE,WAAW,CAAC;oBACd,OAAO;gBACT;gBAEA,MAAO,EAAE,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC,EAAE,SAAS,EAAG;oBACrD,EAAE,SAAS,CAAC,MAAM;gBACpB;gBAEA,IAAI,EAAE,UAAU,CAAC,OAAO,KAAK,KAAK;oBAChC,IAAI,CAAC,WAAW,CAAC,EAAE,UAAU,EAAE;gBACjC;YACF;QACF;IACF;IAEA,aAAY,IAAI,EAAE,GAAG;QACnB,IAAI,CAAC,GAAG,CAAC,eAAe,MAAM;QAC9B,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,KAAK,SAAS,GAAG,IAAI,WAAW;YAChC,KAAK,OAAO,GAAG,IAAI,WAAW;YAC9B,OAAO;QACT;QAEA,IAAI,cAAc,KAAK,aAAa,CAAC,aAAa,CAAC;QACnD,MAAO,KAAK,UAAU,CAAE;YACtB,YAAY,WAAW,CAAC,KAAK,UAAU;QACzC;QACA,KAAK,UAAU,CAAC,YAAY,CAAC,aAAa;QAC1C,IAAI,KAAK,WAAW,EAAE;YACpB,YAAY,WAAW,GAAG,KAAK,WAAW;QAC5C;QAEA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,UAAU,CAAC,MAAM,EAAE,IAAK;YAC/C,YAAY,gBAAgB,CAAC,KAAK,UAAU,CAAC,EAAE,CAAC,SAAS;QAC3D;QACA,OAAO;IACT;IAEA;;;;;;IAME,GACF,cAAa,cAAc;QACzB,IAAI,CAAC,YAAY,CAAC;QAElB,uEAAuE;QACvE,iEAAiE;QACjE,sEAAsE;QACtE,IAAI,CAAC,eAAe,CAAC;QAErB,IAAI,CAAC,cAAc,CAAC;QAEpB,0CAA0C;QAC1C,IAAI,CAAC,mBAAmB,CAAC,gBAAgB;QACzC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB;QACzC,IAAI,CAAC,MAAM,CAAC,gBAAgB;QAC5B,IAAI,CAAC,MAAM,CAAC,gBAAgB;QAC5B,IAAI,CAAC,MAAM,CAAC,gBAAgB;QAC5B,IAAI,CAAC,MAAM,CAAC,gBAAgB;QAC5B,IAAI,CAAC,MAAM,CAAC,gBAAgB;QAE5B,qHAAqH;QACrH,yEAAyE;QAEzE,IAAI,wBAAwB,IAAI,CAAC,sBAAsB;QAEvD,IAAI,CAAC,YAAY,CAAC,eAAe,QAAQ,EAAE,SAAU,YAAY;YAC/D,IAAI,CAAC,kBAAkB,CAAC,cAAc,SAAU,IAAI,EAAE,WAAW;gBAC/D,OACE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,gBAChC,KAAK,WAAW,CAAC,MAAM,GAAG;YAE9B;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,gBAAgB;QAC5B,IAAI,CAAC,MAAM,CAAC,gBAAgB;QAC5B,IAAI,CAAC,MAAM,CAAC,gBAAgB;QAC5B,IAAI,CAAC,MAAM,CAAC,gBAAgB;QAC5B,IAAI,CAAC,MAAM,CAAC,gBAAgB;QAC5B,IAAI,CAAC,aAAa,CAAC;QAEnB,4DAA4D;QAC5D,yBAAyB;QACzB,IAAI,CAAC,mBAAmB,CAAC,gBAAgB;QACzC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB;QACzC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB;QAEzC,6EAA6E;QAC7E,IAAI,CAAC,gBAAgB,CACnB,IAAI,CAAC,mBAAmB,CAAC,gBAAgB;YAAC;SAAK,GAC/C;QAGF,0BAA0B;QAC1B,IAAI,CAAC,YAAY,CACf,IAAI,CAAC,mBAAmB,CAAC,gBAAgB;YAAC;SAAI,GAC9C,SAAU,SAAS;YACjB,sEAAsE;YACtE,eAAe;YACf,IAAI,sBAAsB,IAAI,CAAC,mBAAmB,CAAC,WAAW;gBAC5D;gBACA;gBACA;gBACA;aACD,EAAE,MAAM;YACT,OACE,wBAAwB,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW;QAEhE;QAGF,IAAI,CAAC,YAAY,CACf,IAAI,CAAC,mBAAmB,CAAC,gBAAgB;YAAC;SAAK,GAC/C,SAAU,EAAE;YACV,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,WAAW;YACxC,IAAI,QAAQ,KAAK,OAAO,IAAI,KAAK;gBAC/B,GAAG,MAAM;YACX;QACF;QAGF,4BAA4B;QAC5B,IAAI,CAAC,YAAY,CACf,IAAI,CAAC,mBAAmB,CAAC,gBAAgB;YAAC;SAAQ,GAClD,SAAU,KAAK;YACb,IAAI,QAAQ,IAAI,CAAC,0BAA0B,CAAC,OAAO,WAC/C,MAAM,iBAAiB,GACvB;YACJ,IAAI,IAAI,CAAC,0BAA0B,CAAC,OAAO,OAAO;gBAChD,IAAI,MAAM,MAAM,iBAAiB;gBACjC,IAAI,IAAI,CAAC,0BAA0B,CAAC,KAAK,OAAO;oBAC9C,IAAI,OAAO,IAAI,iBAAiB;oBAChC,OAAO,IAAI,CAAC,WAAW,CACrB,MACA,IAAI,CAAC,UAAU,CAAC,KAAK,UAAU,EAAE,IAAI,CAAC,kBAAkB,IACpD,MACA;oBAEN,MAAM,UAAU,CAAC,YAAY,CAAC,MAAM;gBACtC;YACF;QACF;IAEJ;IAEA;;;;;;IAME,GACF,iBAAgB,IAAI;QAClB,KAAK,WAAW,GAAG;YAAE,cAAc;QAAE;QAErC,OAAQ,KAAK,OAAO;YAClB,KAAK;gBACH,KAAK,WAAW,CAAC,YAAY,IAAI;gBACjC;YAEF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,KAAK,WAAW,CAAC,YAAY,IAAI;gBACjC;YAEF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,KAAK,WAAW,CAAC,YAAY,IAAI;gBACjC;YAEF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,KAAK,WAAW,CAAC,YAAY,IAAI;gBACjC;QACJ;QAEA,KAAK,WAAW,CAAC,YAAY,IAAI,IAAI,CAAC,eAAe,CAAC;IACxD;IAEA,mBAAkB,IAAI;QACpB,IAAI,WAAW,IAAI,CAAC,YAAY,CAAC,MAAM;QACvC,KAAK,MAAM;QACX,OAAO;IACT;IAEA;;;;;;;;;;GAUC,GACD,cAAa,IAAI,EAAE,iBAAiB;QAClC,qDAAqD;QACrD,IAAI,CAAC,qBAAqB,KAAK,iBAAiB,EAAE;YAChD,OAAO,KAAK,iBAAiB;QAC/B;QACA,uBAAuB;QACvB,IAAI,KAAK,kBAAkB,EAAE;YAC3B,OAAO,KAAK,kBAAkB;QAChC;QACA,6DAA6D;QAC7D,+DAA+D;QAC/D,qCAAqC;QACrC,GAAG;YACD,OAAO,KAAK,UAAU;QACxB,QAAS,QAAQ,CAAC,KAAK,kBAAkB,CAAE;QAC3C,OAAO,QAAQ,KAAK,kBAAkB;IACxC;IAEA,oCAAoC;IACpC,+CAA+C;IAC/C,yGAAyG;IACzG,0DAA0D;IAC1D,iBAAgB,KAAK,EAAE,KAAK;QAC1B,IAAI,UAAU,MACX,WAAW,GACX,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAC3B,MAAM,CAAC;QACV,IAAI,UAAU,MACX,WAAW,GACX,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAC3B,MAAM,CAAC;QACV,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ,MAAM,EAAE;YACtC,OAAO;QACT;QACA,IAAI,cAAc,QAAQ,MAAM,CAAC,CAAA,QAAS,CAAC,QAAQ,QAAQ,CAAC;QAC5D,IAAI,YAAY,YAAY,IAAI,CAAC,KAAK,MAAM,GAAG,QAAQ,IAAI,CAAC,KAAK,MAAM;QACvE,OAAO,IAAI;IACb;IAEA;;;;;;GAMC,GACD,gBAAe,IAAI,EAAE,WAAW;QAC9B,IAAI,MAAM,KAAK,YAAY,CAAC;QAC5B,IAAI,WAAW,KAAK,YAAY,CAAC;QACjC,IAAI,eAAe,KAAK,WAAW,CAAC,IAAI,GAAG,MAAM;QAEjD,OACE,CAAC,QAAQ,YACN,YAAY,SAAS,QAAQ,CAAC,aAC/B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,KACvC,CAAC,CAAC,gBACF,eAAe;IAEnB;IAEA,mBAAkB,IAAI,EAAE,QAAQ;QAC9B,WAAW,YAAY;QACvB,IAAI,IAAI,GACN,YAAY,EAAE;QAChB,MAAO,KAAK,UAAU,CAAE;YACtB,UAAU,IAAI,CAAC,KAAK,UAAU;YAC9B,IAAI,YAAY,EAAE,MAAM,UAAU;gBAChC;YACF;YACA,OAAO,KAAK,UAAU;QACxB;QACA,OAAO;IACT;IAEA;;;;;;IAME,GACF,uCAAuC,GACvC,cAAa,IAAI;QACf,IAAI,CAAC,GAAG,CAAC;QACT,IAAI,MAAM,IAAI,CAAC,IAAI;QACnB,IAAI,WAAW,SAAS;QACxB,OAAO,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;QAEnC,oDAAoD;QACpD,IAAI,CAAC,MAAM;YACT,IAAI,CAAC,GAAG,CAAC;YACT,OAAO;QACT;QAEA,IAAI,gBAAgB,KAAK,SAAS;QAElC,MAAO,KAAM;YACX,IAAI,CAAC,GAAG,CAAC;YACT,IAAI,0BAA0B,IAAI,CAAC,aAAa,CAC9C,IAAI,CAAC,oBAAoB;YAG3B,yEAAyE;YACzE,6EAA6E;YAC7E,kFAAkF;YAClF,IAAI,kBAAkB,EAAE;YACxB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe;YAEpC,IAAI,0BAA0B;YAE9B,MAAO,KAAM;gBACX,IAAI,KAAK,OAAO,KAAK,QAAQ;oBAC3B,IAAI,CAAC,YAAY,GAAG,KAAK,YAAY,CAAC;gBACxC;gBAEA,IAAI,cAAc,KAAK,SAAS,GAAG,MAAM,KAAK,EAAE;gBAEhD,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO;oBAClC,IAAI,CAAC,GAAG,CAAC,4BAA4B;oBACrC,OAAO,IAAI,CAAC,iBAAiB,CAAC;oBAC9B;gBACF;gBAEA,6FAA6F;gBAC7F,IACE,KAAK,YAAY,CAAC,iBAAiB,UACnC,KAAK,YAAY,CAAC,WAAW,UAC7B;oBACA,OAAO,IAAI,CAAC,iBAAiB,CAAC;oBAC9B;gBACF;gBAEA,sHAAsH;gBACtH,IACE,CAAC,IAAI,CAAC,cAAc,IACpB,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,IACtB,IAAI,CAAC,cAAc,CAAC,MAAM,cAC1B;oBACA,8GAA8G;oBAC9G,IAAI,wBAAwB,IAAI,CAAC,YAAY,CAAC,MAAM;oBACpD,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC;oBAC7B,IAAI,mBAAmB;oBACvB,MAAO,QAAQ,QAAQ,sBAAuB;wBAC5C,IAAI,WAAW,KAAK,YAAY,CAAC;wBACjC,IAAI,YAAY,SAAS,QAAQ,CAAC,SAAS;4BACzC,mBAAmB;4BACnB;wBACF,OAAO;4BACL,OAAO,IAAI,CAAC,YAAY,CAAC;wBAC3B;oBACF;oBACA,IAAI,CAAC,cAAc,GAAG,CAAC,oBAAoB,IAAI,EAAE,WAAW,CAAC,IAAI;oBACjE,OAAO,IAAI,CAAC,iBAAiB,CAAC;oBAC9B;gBACF;gBAEA,IAAI,2BAA2B,IAAI,CAAC,sBAAsB,CAAC,OAAO;oBAChE,IAAI,CAAC,GAAG,CACN,qBACA,KAAK,WAAW,CAAC,IAAI,IACrB,IAAI,CAAC,aAAa,CAAC,IAAI;oBAEzB,0BAA0B;oBAC1B,OAAO,IAAI,CAAC,iBAAiB,CAAC;oBAC9B;gBACF;gBAEA,6BAA6B;gBAC7B,IAAI,yBAAyB;oBAC3B,IACE,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBACrC,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,gBACxC,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,YAC5B,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,WAC5B,KAAK,OAAO,KAAK,UACjB,KAAK,OAAO,KAAK,KACjB;wBACA,IAAI,CAAC,GAAG,CAAC,mCAAmC;wBAC5C,OAAO,IAAI,CAAC,iBAAiB,CAAC;wBAC9B;oBACF;oBAEA,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,YAAY,CAAC,UAAU;wBAC3D,IAAI,CAAC,GAAG,CACN,gCACE,KAAK,YAAY,CAAC,UAClB,QACA;wBAEJ,OAAO,IAAI,CAAC,iBAAiB,CAAC;wBAC9B;oBACF;gBACF;gBAEA,iGAAiG;gBACjG,IACE,CAAC,KAAK,OAAO,KAAK,SAChB,KAAK,OAAO,KAAK,aACjB,KAAK,OAAO,KAAK,YACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,IAAI,KACvB,IAAI,CAAC,wBAAwB,CAAC,OAC9B;oBACA,OAAO,IAAI,CAAC,iBAAiB,CAAC;oBAC9B;gBACF;gBAEA,IAAI,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,KAAK,OAAO,GAAG;oBACrD,gBAAgB,IAAI,CAAC;gBACvB;gBAEA,uEAAuE;gBACvE,IAAI,KAAK,OAAO,KAAK,OAAO;oBAC1B,wCAAwC;oBACxC,IAAI,IAAI;oBACR,IAAI,YAAY,KAAK,UAAU;oBAC/B,MAAO,UAAW;wBAChB,IAAI,cAAc,UAAU,WAAW;wBACvC,IAAI,IAAI,CAAC,kBAAkB,CAAC,YAAY;4BACtC,IAAI,MAAM,MAAM;gCACd,EAAE,WAAW,CAAC;4BAChB,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY;gCACzC,IAAI,IAAI,aAAa,CAAC;gCACtB,KAAK,YAAY,CAAC,GAAG;gCACrB,EAAE,WAAW,CAAC;4BAChB;wBACF,OAAO,IAAI,MAAM,MAAM;4BACrB,MAAO,EAAE,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC,EAAE,SAAS,EAAG;gCACrD,EAAE,SAAS,CAAC,MAAM;4BACpB;4BACA,IAAI;wBACN;wBACA,YAAY;oBACd;oBAEA,wEAAwE;oBACxE,wEAAwE;oBACxE,wEAAwE;oBACxE,yDAAyD;oBACzD,IACE,IAAI,CAAC,0BAA0B,CAAC,MAAM,QACtC,IAAI,CAAC,eAAe,CAAC,QAAQ,MAC7B;wBACA,IAAI,UAAU,KAAK,QAAQ,CAAC,EAAE;wBAC9B,KAAK,UAAU,CAAC,YAAY,CAAC,SAAS;wBACtC,OAAO;wBACP,gBAAgB,IAAI,CAAC;oBACvB,OAAO,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;wBAC5C,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM;wBAC9B,gBAAgB,IAAI,CAAC;oBACvB;gBACF;gBACA,OAAO,IAAI,CAAC,YAAY,CAAC;YAC3B;YAEA;;;;;QAKE,GACF,IAAI,aAAa,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,iBAAiB,SAAU,cAAc;gBACzD,IACE,CAAC,eAAe,UAAU,IAC1B,OAAO,eAAe,UAAU,CAAC,OAAO,KAAK,aAC7C;oBACA;gBACF;gBAEA,qEAAqE;gBACrE,IAAI,YAAY,IAAI,CAAC,aAAa,CAAC;gBACnC,IAAI,UAAU,MAAM,GAAG,IAAI;oBACzB;gBACF;gBAEA,kCAAkC;gBAClC,IAAI,YAAY,IAAI,CAAC,iBAAiB,CAAC,gBAAgB;gBACvD,IAAI,UAAU,MAAM,KAAK,GAAG;oBAC1B;gBACF;gBAEA,IAAI,eAAe;gBAEnB,kDAAkD;gBAClD,gBAAgB;gBAEhB,mDAAmD;gBACnD,gBAAgB,UAAU,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM;gBAE3D,iFAAiF;gBACjF,gBAAgB,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,UAAU,MAAM,GAAG,MAAM;gBAE7D,kCAAkC;gBAClC,IAAI,CAAC,YAAY,CAAC,WAAW,SAAU,QAAQ,EAAE,KAAK;oBACpD,IACE,CAAC,SAAS,OAAO,IACjB,CAAC,SAAS,UAAU,IACpB,OAAO,SAAS,UAAU,CAAC,OAAO,KAAK,aACvC;wBACA;oBACF;oBAEA,IAAI,OAAO,SAAS,WAAW,KAAK,aAAa;wBAC/C,IAAI,CAAC,eAAe,CAAC;wBACrB,WAAW,IAAI,CAAC;oBAClB;oBAEA,sBAAsB;oBACtB,wCAAwC;oBACxC,0BAA0B;oBAC1B,2CAA2C;oBAC3C,IAAI,UAAU,GAAG;wBACf,IAAI,eAAe;oBACrB,OAAO,IAAI,UAAU,GAAG;wBACtB,eAAe;oBACjB,OAAO;wBACL,eAAe,QAAQ;oBACzB;oBACA,SAAS,WAAW,CAAC,YAAY,IAAI,eAAe;gBACtD;YACF;YAEA,kEAAkE;YAClE,oEAAoE;YACpE,IAAI,gBAAgB,EAAE;YACtB,IAAK,IAAI,IAAI,GAAG,KAAK,WAAW,MAAM,EAAE,IAAI,IAAI,KAAK,EAAG;gBACtD,IAAI,YAAY,UAAU,CAAC,EAAE;gBAE7B,uEAAuE;gBACvE,yEAAyE;gBACzE,gCAAgC;gBAChC,IAAI,iBACF,UAAU,WAAW,CAAC,YAAY,GAClC,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU;gBACtC,UAAU,WAAW,CAAC,YAAY,GAAG;gBAErC,IAAI,CAAC,GAAG,CAAC,cAAc,WAAW,gBAAgB;gBAElD,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,gBAAgB,EAAE,IAAK;oBAC9C,IAAI,gBAAgB,aAAa,CAAC,EAAE;oBAEpC,IACE,CAAC,iBACD,iBAAiB,cAAc,WAAW,CAAC,YAAY,EACvD;wBACA,cAAc,MAAM,CAAC,GAAG,GAAG;wBAC3B,IAAI,cAAc,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE;4BAChD,cAAc,GAAG;wBACnB;wBACA;oBACF;gBACF;YACF;YAEA,IAAI,eAAe,aAAa,CAAC,EAAE,IAAI;YACvC,IAAI,6BAA6B;YACjC,IAAI;YAEJ,yEAAyE;YACzE,uEAAuE;YACvE,IAAI,iBAAiB,QAAQ,aAAa,OAAO,KAAK,QAAQ;gBAC5D,oDAAoD;gBACpD,eAAe,IAAI,aAAa,CAAC;gBACjC,6BAA6B;gBAC7B,+EAA+E;gBAC/E,gDAAgD;gBAChD,MAAO,KAAK,UAAU,CAAE;oBACtB,IAAI,CAAC,GAAG,CAAC,qBAAqB,KAAK,UAAU;oBAC7C,aAAa,WAAW,CAAC,KAAK,UAAU;gBAC1C;gBAEA,KAAK,WAAW,CAAC;gBAEjB,IAAI,CAAC,eAAe,CAAC;YACvB,OAAO,IAAI,cAAc;gBACvB,+GAA+G;gBAC/G,sEAAsE;gBACtE,IAAI,gCAAgC,EAAE;gBACtC,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;oBAC7C,IACE,aAAa,CAAC,EAAE,CAAC,WAAW,CAAC,YAAY,GACvC,aAAa,WAAW,CAAC,YAAY,IACvC,MACA;wBACA,8BAA8B,IAAI,CAChC,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,EAAE;oBAE3C;gBACF;gBACA,IAAI,wBAAwB;gBAC5B,IAAI,8BAA8B,MAAM,IAAI,uBAAuB;oBACjE,uBAAuB,aAAa,UAAU;oBAC9C,MAAO,qBAAqB,OAAO,KAAK,OAAQ;wBAC9C,IAAI,8BAA8B;wBAClC,IACE,IAAI,gBAAgB,GACpB,gBAAgB,8BAA8B,MAAM,IACpD,8BAA8B,uBAC9B,gBACA;4BACA,+BAA+B,OAC7B,6BAA6B,CAAC,cAAc,CAAC,QAAQ,CACnD;wBAGN;wBACA,IAAI,+BAA+B,uBAAuB;4BACxD,eAAe;4BACf;wBACF;wBACA,uBAAuB,qBAAqB,UAAU;oBACxD;gBACF;gBACA,IAAI,CAAC,aAAa,WAAW,EAAE;oBAC7B,IAAI,CAAC,eAAe,CAAC;gBACvB;gBAEA,uEAAuE;gBACvE,0EAA0E;gBAC1E,gFAAgF;gBAChF,+EAA+E;gBAC/E,sEAAsE;gBACtE,4EAA4E;gBAC5E,QAAQ;gBACR,uBAAuB,aAAa,UAAU;gBAC9C,IAAI,YAAY,aAAa,WAAW,CAAC,YAAY;gBACrD,oCAAoC;gBACpC,IAAI,iBAAiB,YAAY;gBACjC,MAAO,qBAAqB,OAAO,KAAK,OAAQ;oBAC9C,IAAI,CAAC,qBAAqB,WAAW,EAAE;wBACrC,uBAAuB,qBAAqB,UAAU;wBACtD;oBACF;oBACA,IAAI,cAAc,qBAAqB,WAAW,CAAC,YAAY;oBAC/D,IAAI,cAAc,gBAAgB;wBAChC;oBACF;oBACA,IAAI,cAAc,WAAW;wBAC3B,4CAA4C;wBAC5C,eAAe;wBACf;oBACF;oBACA,YAAY,qBAAqB,WAAW,CAAC,YAAY;oBACzD,uBAAuB,qBAAqB,UAAU;gBACxD;gBAEA,qFAAqF;gBACrF,oFAAoF;gBACpF,uBAAuB,aAAa,UAAU;gBAC9C,MACE,qBAAqB,OAAO,IAAI,UAChC,qBAAqB,QAAQ,CAAC,MAAM,IAAI,EACxC;oBACA,eAAe;oBACf,uBAAuB,aAAa,UAAU;gBAChD;gBACA,IAAI,CAAC,aAAa,WAAW,EAAE;oBAC7B,IAAI,CAAC,eAAe,CAAC;gBACvB;YACF;YAEA,4EAA4E;YAC5E,0EAA0E;YAC1E,wBAAwB;YACxB,IAAI,iBAAiB,IAAI,aAAa,CAAC;YACvC,IAAI,UAAU;gBACZ,eAAe,EAAE,GAAG;YACtB;YAEA,IAAI,wBAAwB,KAAK,GAAG,CAClC,IACA,aAAa,WAAW,CAAC,YAAY,GAAG;YAE1C,uFAAuF;YACvF,uBAAuB,aAAa,UAAU;YAC9C,IAAI,WAAW,qBAAqB,QAAQ;YAE5C,IAAK,IAAI,IAAI,GAAG,KAAK,SAAS,MAAM,EAAE,IAAI,IAAI,IAAK;gBACjD,IAAI,UAAU,QAAQ,CAAC,EAAE;gBACzB,IAAI,SAAS;gBAEb,IAAI,CAAC,GAAG,CACN,4BACA,SACA,QAAQ,WAAW,GACf,gBAAgB,QAAQ,WAAW,CAAC,YAAY,GAChD;gBAEN,IAAI,CAAC,GAAG,CACN,qBACA,QAAQ,WAAW,GAAG,QAAQ,WAAW,CAAC,YAAY,GAAG;gBAG3D,IAAI,YAAY,cAAc;oBAC5B,SAAS;gBACX,OAAO;oBACL,IAAI,eAAe;oBAEnB,mFAAmF;oBACnF,IACE,QAAQ,SAAS,KAAK,aAAa,SAAS,IAC5C,aAAa,SAAS,KAAK,IAC3B;wBACA,gBAAgB,aAAa,WAAW,CAAC,YAAY,GAAG;oBAC1D;oBAEA,IACE,QAAQ,WAAW,IACnB,QAAQ,WAAW,CAAC,YAAY,GAAG,gBACjC,uBACF;wBACA,SAAS;oBACX,OAAO,IAAI,QAAQ,QAAQ,KAAK,KAAK;wBACnC,IAAI,cAAc,IAAI,CAAC,eAAe,CAAC;wBACvC,IAAI,cAAc,IAAI,CAAC,aAAa,CAAC;wBACrC,IAAI,aAAa,YAAY,MAAM;wBAEnC,IAAI,aAAa,MAAM,cAAc,MAAM;4BACzC,SAAS;wBACX,OAAO,IACL,aAAa,MACb,aAAa,KACb,gBAAgB,KAChB,YAAY,MAAM,CAAC,eAAe,CAAC,GACnC;4BACA,SAAS;wBACX;oBACF;gBACF;gBAEA,IAAI,QAAQ;oBACV,IAAI,CAAC,GAAG,CAAC,mBAAmB;oBAE5B,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,QAAQ,QAAQ,GAAG;wBAC5D,iFAAiF;wBACjF,uEAAuE;wBACvE,IAAI,CAAC,GAAG,CAAC,qBAAqB,SAAS;wBAEvC,UAAU,IAAI,CAAC,WAAW,CAAC,SAAS;oBACtC;oBAEA,eAAe,WAAW,CAAC;oBAC3B,6CAA6C;oBAC7C,oDAAoD;oBACpD,WAAW,qBAAqB,QAAQ;oBACxC,qDAAqD;oBACrD,gEAAgE;oBAChE,0DAA0D;oBAC1D,qBAAqB;oBACrB,KAAK;oBACL,MAAM;gBACR;YACF;YAEA,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,GAAG,CAAC,+BAA+B,eAAe,SAAS;YAClE;YACA,mFAAmF;YACnF,IAAI,CAAC,YAAY,CAAC;YAClB,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,GAAG,CAAC,gCAAgC,eAAe,SAAS;YACnE;YAEA,IAAI,4BAA4B;gBAC9B,sFAAsF;gBACtF,kFAAkF;gBAClF,sFAAsF;gBACtF,wCAAwC;gBACxC,aAAa,EAAE,GAAG;gBAClB,aAAa,SAAS,GAAG;YAC3B,OAAO;gBACL,IAAI,MAAM,IAAI,aAAa,CAAC;gBAC5B,IAAI,EAAE,GAAG;gBACT,IAAI,SAAS,GAAG;gBAChB,MAAO,eAAe,UAAU,CAAE;oBAChC,IAAI,WAAW,CAAC,eAAe,UAAU;gBAC3C;gBACA,eAAe,WAAW,CAAC;YAC7B;YAEA,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,GAAG,CAAC,mCAAmC,eAAe,SAAS;YACtE;YAEA,IAAI,kBAAkB;YAEtB,kEAAkE;YAClE,qEAAqE;YACrE,6EAA6E;YAC7E,8EAA8E;YAC9E,+BAA+B;YAC/B,IAAI,aAAa,IAAI,CAAC,aAAa,CAAC,gBAAgB,MAAM,MAAM;YAChE,IAAI,aAAa,IAAI,CAAC,cAAc,EAAE;gBACpC,kBAAkB;gBAClB,mDAAmD;gBACnD,KAAK,SAAS,GAAG;gBAEjB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;oBAClB;oBACA;gBACF;gBAEA,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,GAAG;oBACjD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,oBAAoB;gBAC5C,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,GAAG;oBACvD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,mBAAmB;gBAC3C,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,wBAAwB,GAAG;oBAC5D,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,wBAAwB;gBAChD,OAAO;oBACL,iGAAiG;oBACjG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;wBAChC,OAAO,EAAE,UAAU,GAAG,EAAE,UAAU;oBACpC;oBAEA,gDAAgD;oBAChD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU,EAAE;wBACjC,OAAO;oBACT;oBAEA,iBAAiB,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,cAAc;oBACjD,kBAAkB;gBACpB;YACF;YAEA,IAAI,iBAAiB;gBACnB,iEAAiE;gBACjE,IAAI,YAAY;oBAAC;oBAAsB;iBAAa,CAAC,MAAM,CACzD,IAAI,CAAC,iBAAiB,CAAC;gBAEzB,IAAI,CAAC,SAAS,CAAC,WAAW,SAAU,QAAQ;oBAC1C,IAAI,CAAC,SAAS,OAAO,EAAE;wBACrB,OAAO;oBACT;oBACA,IAAI,aAAa,SAAS,YAAY,CAAC;oBACvC,IAAI,YAAY;wBACd,IAAI,CAAC,WAAW,GAAG;wBACnB,OAAO;oBACT;oBACA,OAAO;gBACT;gBACA,OAAO;YACT;QACF;IACF;IAEA;;;;;GAKC,GACD,uBAAsB,GAAG;QACvB,IAAI,CAAC,KAAK;YACR,OAAO;QACT;QAEA,IAAI,gBAAgB,IAAI,CAAC,eAAe;QACxC,OAAO,IACJ,OAAO,CAAC,4BAA4B,SAAU,CAAC,EAAE,GAAG;YACnD,OAAO,aAAa,CAAC,IAAI;QAC3B,GACC,OAAO,CAAC,kCAAkC,SAAU,CAAC,EAAE,GAAG,EAAE,MAAM;YACjE,IAAI,MAAM,SAAS,OAAO,QAAQ,MAAM,KAAK;YAE7C,sEAAsE;YACtE,IAAI,OAAO,KAAK,MAAM,YAAa,OAAO,UAAU,OAAO,QAAS;gBAClE,MAAM;YACR;YAEA,OAAO,OAAO,aAAa,CAAC;QAC9B;IACJ;IAEA;;;;GAIC,GACD,YAAW,GAAG;QACZ,IAAI,UAAU,IAAI,CAAC,mBAAmB,CAAC,KAAK;YAAC;SAAS;QAEtD,IAAI;QAEJ,IAAI,CAAC,YAAY,CAAC,SAAS,SAAU,aAAa;YAChD,IACE,CAAC,YACD,cAAc,YAAY,CAAC,YAAY,uBACvC;gBACA,IAAI;oBACF,iCAAiC;oBACjC,IAAI,UAAU,cAAc,WAAW,CAAC,OAAO,CAC7C,8BACA;oBAEF,IAAI,SAAS,KAAK,KAAK,CAAC;oBAExB,IAAI,MAAM,OAAO,CAAC,SAAS;wBACzB,SAAS,OAAO,IAAI,CAAC,CAAA;4BACnB,OACE,EAAE,CAAC,QAAQ,IACX,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB;wBAErD;wBACA,IAAI,CAAC,QAAQ;4BACX;wBACF;oBACF;oBAEA,IAAI,oBAAoB;oBACxB,IAAI,UACF,AAAC,OAAO,MAAM,CAAC,WAAW,KAAK,YAC7B,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,sBAC1B,OAAO,MAAM,CAAC,WAAW,KAAK,YAC7B,OAAO,MAAM,CAAC,WAAW,CAAC,SAAS,IAAI,YACvC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC;oBAEvC,IAAI,CAAC,SAAS;wBACZ;oBACF;oBAEA,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,MAAM,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG;wBACvD,SAAS,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;4BAC7B,OAAO,CAAC,EAAE,CAAC,QAAQ,IAAI,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB;wBAClE;oBACF;oBAEA,IACE,CAAC,UACD,CAAC,MAAM,CAAC,QAAQ,IAChB,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,GACtD;wBACA;oBACF;oBAEA,WAAW,CAAC;oBAEZ,IACE,OAAO,OAAO,IAAI,KAAK,YACvB,OAAO,OAAO,QAAQ,KAAK,YAC3B,OAAO,IAAI,KAAK,OAAO,QAAQ,EAC/B;wBACA,yHAAyH;wBACzH,8HAA8H;wBAC9H,uHAAuH;wBAEvH,IAAI,QAAQ,IAAI,CAAC,gBAAgB;wBACjC,IAAI,cAAc,IAAI,CAAC,eAAe,CAAC,OAAO,IAAI,EAAE,SAAS;wBAC7D,IAAI,kBACF,IAAI,CAAC,eAAe,CAAC,OAAO,QAAQ,EAAE,SAAS;wBAEjD,IAAI,mBAAmB,CAAC,aAAa;4BACnC,SAAS,KAAK,GAAG,OAAO,QAAQ;wBAClC,OAAO;4BACL,SAAS,KAAK,GAAG,OAAO,IAAI;wBAC9B;oBACF,OAAO,IAAI,OAAO,OAAO,IAAI,KAAK,UAAU;wBAC1C,SAAS,KAAK,GAAG,OAAO,IAAI,CAAC,IAAI;oBACnC,OAAO,IAAI,OAAO,OAAO,QAAQ,KAAK,UAAU;wBAC9C,SAAS,KAAK,GAAG,OAAO,QAAQ,CAAC,IAAI;oBACvC;oBACA,IAAI,OAAO,MAAM,EAAE;wBACjB,IAAI,OAAO,OAAO,MAAM,CAAC,IAAI,KAAK,UAAU;4BAC1C,SAAS,MAAM,GAAG,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI;wBAC3C,OAAO,IACL,MAAM,OAAO,CAAC,OAAO,MAAM,KAC3B,OAAO,MAAM,CAAC,EAAE,IAChB,OAAO,OAAO,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,UACjC;4BACA,SAAS,MAAM,GAAG,OAAO,MAAM,CAC5B,MAAM,CAAC,SAAU,MAAM;gCACtB,OAAO,UAAU,OAAO,OAAO,IAAI,KAAK;4BAC1C,GACC,GAAG,CAAC,SAAU,MAAM;gCACnB,OAAO,OAAO,IAAI,CAAC,IAAI;4BACzB,GACC,IAAI,CAAC;wBACV;oBACF;oBACA,IAAI,OAAO,OAAO,WAAW,KAAK,UAAU;wBAC1C,SAAS,OAAO,GAAG,OAAO,WAAW,CAAC,IAAI;oBAC5C;oBACA,IAAI,OAAO,SAAS,IAAI,OAAO,OAAO,SAAS,CAAC,IAAI,KAAK,UAAU;wBACjE,SAAS,QAAQ,GAAG,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI;oBAChD;oBACA,IAAI,OAAO,OAAO,aAAa,KAAK,UAAU;wBAC5C,SAAS,aAAa,GAAG,OAAO,aAAa,CAAC,IAAI;oBACpD;gBACF,EAAE,OAAO,KAAK;oBACZ,IAAI,CAAC,GAAG,CAAC,IAAI,OAAO;gBACtB;YACF;QACF;QACA,OAAO,WAAW,WAAW,CAAC;IAChC;IAEA;;;;;;;GAOC,GACD,qBAAoB,MAAM;QACxB,IAAI,WAAW,CAAC;QAChB,IAAI,SAAS,CAAC;QACd,IAAI,eAAe,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC;QAElD,+CAA+C;QAC/C,IAAI,kBACF;QAEF,yBAAyB;QACzB,IAAI,cACF;QAEF,yBAAyB;QACzB,IAAI,CAAC,YAAY,CAAC,cAAc,SAAU,OAAO;YAC/C,IAAI,cAAc,QAAQ,YAAY,CAAC;YACvC,IAAI,kBAAkB,QAAQ,YAAY,CAAC;YAC3C,IAAI,UAAU,QAAQ,YAAY,CAAC;YACnC,IAAI,CAAC,SAAS;gBACZ;YACF;YACA,IAAI,UAAU;YACd,IAAI,OAAO;YAEX,IAAI,iBAAiB;gBACnB,UAAU,gBAAgB,KAAK,CAAC;gBAChC,IAAI,SAAS;oBACX,kDAAkD;oBAClD,yBAAyB;oBACzB,OAAO,OAAO,CAAC,EAAE,CAAC,WAAW,GAAG,OAAO,CAAC,OAAO;oBAC/C,mBAAmB;oBACnB,MAAM,CAAC,KAAK,GAAG,QAAQ,IAAI;gBAC7B;YACF;YACA,IAAI,CAAC,WAAW,eAAe,YAAY,IAAI,CAAC,cAAc;gBAC5D,OAAO;gBACP,IAAI,SAAS;oBACX,gEAAgE;oBAChE,mCAAmC;oBACnC,OAAO,KAAK,WAAW,GAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO;oBAC5D,MAAM,CAAC,KAAK,GAAG,QAAQ,IAAI;gBAC7B;YACF;QACF;QAEA,YAAY;QACZ,SAAS,KAAK,GACZ,OAAO,KAAK,IACZ,MAAM,CAAC,WAAW,IAClB,MAAM,CAAC,eAAe,IACtB,MAAM,CAAC,WAAW,IAClB,MAAM,CAAC,sBAAsB,IAC7B,MAAM,CAAC,sBAAsB,IAC7B,OAAO,KAAK,IACZ,MAAM,CAAC,gBAAgB,IACvB,MAAM,CAAC,gBAAgB;QAEzB,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,SAAS,KAAK,GAAG,IAAI,CAAC,gBAAgB;QACxC;QAEA,MAAM,gBACJ,OAAO,MAAM,CAAC,iBAAiB,KAAK,YACpC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,IACjC,MAAM,CAAC,iBAAiB,GACxB;QAEN,aAAa;QACb,SAAS,MAAM,GACb,OAAO,MAAM,IACb,MAAM,CAAC,aAAa,IACpB,MAAM,CAAC,iBAAiB,IACxB,OAAO,MAAM,IACb,MAAM,CAAC,iBAAiB,IACxB;QAEF,kBAAkB;QAClB,SAAS,OAAO,GACd,OAAO,OAAO,IACd,MAAM,CAAC,iBAAiB,IACxB,MAAM,CAAC,qBAAqB,IAC5B,MAAM,CAAC,iBAAiB,IACxB,MAAM,CAAC,4BAA4B,IACnC,MAAM,CAAC,4BAA4B,IACnC,OAAO,WAAW,IAClB,MAAM,CAAC,sBAAsB;QAE/B,gBAAgB;QAChB,SAAS,QAAQ,GAAG,OAAO,QAAQ,IAAI,MAAM,CAAC,eAAe;QAE7D,6BAA6B;QAC7B,SAAS,aAAa,GACpB,OAAO,aAAa,IACpB,MAAM,CAAC,yBAAyB,IAChC,MAAM,CAAC,mBAAmB,IAC1B;QAEF,8DAA8D;QAC9D,iCAAiC;QACjC,SAAS,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,SAAS,KAAK;QAC1D,SAAS,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,SAAS,MAAM;QAC5D,SAAS,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,SAAS,OAAO;QAC9D,SAAS,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,SAAS,QAAQ;QAChE,SAAS,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,SAAS,aAAa;QAE1E,OAAO;IACT;IAEA;;;;;IAKE,GACF,gBAAe,IAAI;QACjB,MAAO,KAAM;YACX,IAAI,KAAK,OAAO,KAAK,OAAO;gBAC1B,OAAO;YACT;YACA,IAAI,KAAK,QAAQ,CAAC,MAAM,KAAK,KAAK,KAAK,WAAW,CAAC,IAAI,OAAO,IAAI;gBAChE,OAAO;YACT;YACA,OAAO,KAAK,QAAQ,CAAC,EAAE;QACzB;QACA,OAAO;IACT;IAEA;;;;;;;IAOE,GACF,uBAAsB,GAAG;QACvB,kFAAkF;QAClF,2FAA2F;QAC3F,IAAI,OAAO,MAAM,IAAI,CAAC,IAAI,oBAAoB,CAAC;QAC/C,IAAI,CAAC,YAAY,CAAC,MAAM,SAAU,GAAG;YACnC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,UAAU,CAAC,MAAM,EAAE,IAAK;gBAC9C,IAAI,OAAO,IAAI,UAAU,CAAC,EAAE;gBAC5B,OAAQ,KAAK,IAAI;oBACf,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;wBACH;gBACJ;gBAEA,IAAI,yBAAyB,IAAI,CAAC,KAAK,KAAK,GAAG;oBAC7C;gBACF;YACF;YAEA,IAAI,MAAM;QACZ;QAEA,kDAAkD;QAClD,IAAI,YAAY,MAAM,IAAI,CAAC,IAAI,oBAAoB,CAAC;QACpD,IAAI,CAAC,YAAY,CAAC,WAAW,SAAU,QAAQ;YAC7C,iEAAiE;YACjE,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW;gBAClC;YACF;YACA,IAAI,MAAM,IAAI,aAAa,CAAC;YAC5B,8DAA8D;YAC9D,mDAAmD;YACnD,6DAA6D;YAC7D,kCAAkC;YAClC,mDAAmD;YACnD,IAAI,SAAS,GAAG,SAAS,SAAS;YAElC,+DAA+D;YAC/D,6DAA6D;YAC7D,wCAAwC;YACxC,IAAI,cAAc,SAAS,sBAAsB;YACjD,IAAI,eAAe,IAAI,CAAC,cAAc,CAAC,cAAc;gBACnD,IAAI,UAAU;gBACd,IAAI,QAAQ,OAAO,KAAK,OAAO;oBAC7B,UAAU,YAAY,oBAAoB,CAAC,MAAM,CAAC,EAAE;gBACtD;gBAEA,IAAI,SAAS,IAAI,oBAAoB,CAAC,MAAM,CAAC,EAAE;gBAC/C,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,UAAU,CAAC,MAAM,EAAE,IAAK;oBAClD,IAAI,OAAO,QAAQ,UAAU,CAAC,EAAE;oBAChC,IAAI,KAAK,KAAK,KAAK,IAAI;wBACrB;oBACF;oBAEA,IACE,KAAK,IAAI,KAAK,SACd,KAAK,IAAI,KAAK,YACd,yBAAyB,IAAI,CAAC,KAAK,KAAK,GACxC;wBACA,IAAI,OAAO,YAAY,CAAC,KAAK,IAAI,MAAM,KAAK,KAAK,EAAE;4BACjD;wBACF;wBAEA,IAAI,WAAW,KAAK,IAAI;wBACxB,IAAI,OAAO,YAAY,CAAC,WAAW;4BACjC,WAAW,cAAc;wBAC3B;wBAEA,OAAO,YAAY,CAAC,UAAU,KAAK,KAAK;oBAC1C;gBACF;gBAEA,SAAS,UAAU,CAAC,YAAY,CAAC,IAAI,iBAAiB,EAAE;YAC1D;QACF;IACF;IAEA;;;;IAIE,GACF,gBAAe,GAAG;QAChB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK;YAAC;YAAU;SAAW;IACxE;IAEA;;;;;;;IAOE,GACF,4BAA2B,OAAO,EAAE,GAAG;QACrC,yDAAyD;QACzD,IAAI,QAAQ,QAAQ,CAAC,MAAM,IAAI,KAAK,QAAQ,QAAQ,CAAC,EAAE,CAAC,OAAO,KAAK,KAAK;YACvE,OAAO;QACT;QAEA,sDAAsD;QACtD,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,UAAU,EAAE,SAAU,IAAI;YACvD,OACE,KAAK,QAAQ,KAAK,IAAI,CAAC,SAAS,IAChC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,WAAW;QAEjD;IACF;IAEA,0BAAyB,IAAI;QAC3B,OACE,KAAK,QAAQ,KAAK,IAAI,CAAC,YAAY,IACnC,CAAC,KAAK,WAAW,CAAC,IAAI,GAAG,MAAM,IAC/B,CAAC,CAAC,KAAK,QAAQ,CAAC,MAAM,IACpB,KAAK,QAAQ,CAAC,MAAM,IAClB,KAAK,oBAAoB,CAAC,MAAM,MAAM,GACpC,KAAK,oBAAoB,CAAC,MAAM,MAAM;IAEhD;IAEA;;;;GAIC,GACD,uBAAsB,OAAO;QAC3B,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,UAAU,EAAE,SAAU,IAAI;YACtD,OACE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,OAAO,KACpC,IAAI,CAAC,qBAAqB,CAAC;QAE/B;IACF;IAEA;;;IAGE,GACF,oBAAmB,IAAI;QACrB,OACE,KAAK,QAAQ,KAAK,IAAI,CAAC,SAAS,IAChC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,OAAO,KACxC,CAAC,KAAK,OAAO,KAAK,OACjB,KAAK,OAAO,KAAK,SACjB,KAAK,OAAO,KAAK,KAAK,KACtB,IAAI,CAAC,UAAU,CAAC,KAAK,UAAU,EAAE,IAAI,CAAC,kBAAkB;IAE9D;IAEA,eAAc,IAAI;QAChB,OACE,AAAC,KAAK,QAAQ,KAAK,IAAI,CAAC,SAAS,IAC/B,KAAK,WAAW,CAAC,IAAI,GAAG,MAAM,KAAK,KACpC,KAAK,QAAQ,KAAK,IAAI,CAAC,YAAY,IAAI,KAAK,OAAO,KAAK;IAE7D;IAEA;;;;;;;IAOE,GACF,eAAc,CAAC,EAAE,eAAe;QAC9B,kBACE,OAAO,oBAAoB,cAAc,OAAO;QAClD,IAAI,cAAc,EAAE,WAAW,CAAC,IAAI;QAEpC,IAAI,iBAAiB;YACnB,OAAO,YAAY,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;QACrD;QACA,OAAO;IACT;IAEA;;;;;;IAME,GACF,eAAc,CAAC,EAAE,CAAC;QAChB,IAAI,KAAK;QACT,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,GAAG;IACjD;IAEA;;;;;;IAME,GACF,cAAa,CAAC;QACZ,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,OAAO,OAAO;YAC3C;QACF;QAEA,0DAA0D;QAC1D,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,IAAK;YAC9D,EAAE,eAAe,CAAC,IAAI,CAAC,yBAAyB,CAAC,EAAE;QACrD;QAEA,IAAI,IAAI,CAAC,+BAA+B,CAAC,QAAQ,CAAC,EAAE,OAAO,GAAG;YAC5D,EAAE,eAAe,CAAC;YAClB,EAAE,eAAe,CAAC;QACpB;QAEA,IAAI,MAAM,EAAE,iBAAiB;QAC7B,MAAO,QAAQ,KAAM;YACnB,IAAI,CAAC,YAAY,CAAC;YAClB,MAAM,IAAI,kBAAkB;QAC9B;IACF;IAEA;;;;;;IAME,GACF,iBAAgB,OAAO;QACrB,IAAI,aAAa,IAAI,CAAC,aAAa,CAAC,SAAS,MAAM;QACnD,IAAI,eAAe,GAAG;YACpB,OAAO;QACT;QAEA,IAAI,aAAa;QAEjB,iCAAiC;QACjC,IAAI,CAAC,YAAY,CAAC,QAAQ,oBAAoB,CAAC,MAAM,SAAU,QAAQ;YACrE,IAAI,OAAO,SAAS,YAAY,CAAC;YACjC,IAAI,cAAc,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,MAAM;YAClE,cAAc,IAAI,CAAC,aAAa,CAAC,UAAU,MAAM,GAAG;QACtD;QAEA,OAAO,aAAa;IACtB;IAEA;;;;;;IAME,GACF,iBAAgB,CAAC;QACf,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,GAAG;YACjD,OAAO;QACT;QAEA,IAAI,SAAS;QAEb,+BAA+B;QAC/B,IAAI,OAAO,EAAE,SAAS,KAAK,YAAY,EAAE,SAAS,KAAK,IAAI;YACzD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,SAAS,GAAG;gBAC3C,UAAU;YACZ;YAEA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,SAAS,GAAG;gBAC3C,UAAU;YACZ;QACF;QAEA,wBAAwB;QACxB,IAAI,OAAO,EAAE,EAAE,KAAK,YAAY,EAAE,EAAE,KAAK,IAAI;YAC3C,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG;gBACpC,UAAU;YACZ;YAEA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG;gBACpC,UAAU;YACZ;QACF;QAEA,OAAO;IACT;IAEA;;;;;;;IAOE,GACF,QAAO,CAAC,EAAE,GAAG;QACX,IAAI,UAAU;YAAC;YAAU;YAAS;SAAS,CAAC,QAAQ,CAAC;QAErD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG;YAAC;SAAI,GAAG,SAAU,OAAO;YACrE,8EAA8E;YAC9E,IAAI,SAAS;gBACX,sFAAsF;gBACtF,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,UAAU,CAAC,MAAM,EAAE,IAAK;oBAClD,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,UAAU,CAAC,EAAE,CAAC,KAAK,GAAG;wBAC7D,OAAO;oBACT;gBACF;gBAEA,yDAAyD;gBACzD,IACE,QAAQ,OAAO,KAAK,YACpB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,SAAS,GAC9C;oBACA,OAAO;gBACT;YACF;YAEA,OAAO;QACT;IACF;IAEA;;;;;;;;GAQC,GACD,iBAAgB,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ;QAC/C,WAAW,YAAY;QACvB,UAAU,QAAQ,WAAW;QAC7B,IAAI,QAAQ;QACZ,MAAO,KAAK,UAAU,CAAE;YACtB,IAAI,WAAW,KAAK,QAAQ,UAAU;gBACpC,OAAO;YACT;YACA,IACE,KAAK,UAAU,CAAC,OAAO,KAAK,WAC5B,CAAC,CAAC,YAAY,SAAS,KAAK,UAAU,CAAC,GACvC;gBACA,OAAO;YACT;YACA,OAAO,KAAK,UAAU;YACtB;QACF;QACA,OAAO;IACT;IAEA;;GAEC,GACD,uBAAsB,KAAK;QACzB,IAAI,OAAO;QACX,IAAI,UAAU;QACd,IAAI,MAAM,MAAM,oBAAoB,CAAC;QACrC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACnC,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC,YAAY,CAAC,cAAc;YAChD,IAAI,SAAS;gBACX,UAAU,SAAS,SAAS;YAC9B;YACA,QAAQ,WAAW;YAEnB,mCAAmC;YACnC,IAAI,mBAAmB;YACvB,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC,oBAAoB,CAAC;YACxC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACrC,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,cAAc;gBAClD,IAAI,SAAS;oBACX,UAAU,SAAS,SAAS;gBAC9B;gBACA,oBAAoB,WAAW;YACjC;YACA,UAAU,KAAK,GAAG,CAAC,SAAS;QAC9B;QACA,OAAO;YAAE;YAAM;QAAQ;IACzB;IAEA;;;;GAIC,GACD,iBAAgB,IAAI;QAClB,IAAI,SAAS,KAAK,oBAAoB,CAAC;QACvC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,IAAI,QAAQ,MAAM,CAAC,EAAE;YACrB,IAAI,OAAO,MAAM,YAAY,CAAC;YAC9B,IAAI,QAAQ,gBAAgB;gBAC1B,MAAM,qBAAqB,GAAG;gBAC9B;YACF;YACA,IAAI,YAAY,MAAM,YAAY,CAAC;YACnC,IAAI,aAAa,KAAK;gBACpB,MAAM,qBAAqB,GAAG;gBAC9B;YACF;YACA,IAAI,UAAU,MAAM,YAAY,CAAC;YACjC,IAAI,SAAS;gBACX,MAAM,qBAAqB,GAAG;gBAC9B;YACF;YAEA,IAAI,UAAU,MAAM,oBAAoB,CAAC,UAAU,CAAC,EAAE;YACtD,IAAI,WAAW,QAAQ,UAAU,CAAC,MAAM,EAAE;gBACxC,MAAM,qBAAqB,GAAG;gBAC9B;YACF;YAEA,+EAA+E;YAC/E,IAAI,uBAAuB;gBAAC;gBAAO;gBAAY;gBAAS;gBAAS;aAAK;YACtE,IAAI,mBAAmB,SAAU,GAAG;gBAClC,OAAO,CAAC,CAAC,MAAM,oBAAoB,CAAC,IAAI,CAAC,EAAE;YAC7C;YACA,IAAI,qBAAqB,IAAI,CAAC,mBAAmB;gBAC/C,IAAI,CAAC,GAAG,CAAC;gBACT,MAAM,qBAAqB,GAAG;gBAC9B;YACF;YAEA,yCAAyC;YACzC,IAAI,MAAM,oBAAoB,CAAC,QAAQ,CAAC,EAAE,EAAE;gBAC1C,MAAM,qBAAqB,GAAG;gBAC9B;YACF;YAEA,IAAI,WAAW,IAAI,CAAC,qBAAqB,CAAC;YAE1C,IAAI,SAAS,OAAO,IAAI,KAAK,SAAS,IAAI,IAAI,GAAG;gBAC/C,sEAAsE;gBACtE,MAAM,qBAAqB,GAAG;gBAC9B;YACF;YAEA,IAAI,SAAS,IAAI,IAAI,MAAM,SAAS,OAAO,GAAG,GAAG;gBAC/C,MAAM,qBAAqB,GAAG;gBAC9B;YACF;YACA,gCAAgC;YAChC,MAAM,qBAAqB,GAAG,SAAS,IAAI,GAAG,SAAS,OAAO,GAAG;QACnE;IACF;IAEA,2GAA2G,GAC3G,gBAAe,IAAI;QACjB,IAAI,CAAC,YAAY,CACf,IAAI,CAAC,mBAAmB,CAAC,MAAM;YAAC;YAAO;YAAW;SAAS,GAC3D,SAAU,IAAI;YACZ,kGAAkG;YAClG,gFAAgF;YAChF,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG;gBACtD,sFAAsF;gBACtF,IAAI,QAAQ,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG;gBACjD,IAAI,KAAK,CAAC,EAAE,KAAK,iBAAiB;oBAChC;gBACF;gBAEA,oEAAoE;gBACpE,sEAAsE;gBACtE,IAAI,oBAAoB;gBACxB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,UAAU,CAAC,MAAM,EAAE,IAAK;oBAC/C,IAAI,OAAO,KAAK,UAAU,CAAC,EAAE;oBAC7B,IAAI,KAAK,IAAI,KAAK,OAAO;wBACvB;oBACF;oBAEA,IAAI,yBAAyB,IAAI,CAAC,KAAK,KAAK,GAAG;wBAC7C,oBAAoB;wBACpB;oBACF;gBACF;gBAEA,kFAAkF;gBAClF,iEAAiE;gBACjE,IAAI,mBAAmB;oBACrB,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC,MAAM;oBAC/B,IAAI,YAAY,KAAK,GAAG,CAAC,MAAM,GAAG;oBAClC,IAAI,YAAY,KAAK;wBACnB,KAAK,eAAe,CAAC;oBACvB;gBACF;YACF;YAEA,kFAAkF;YAClF,IACE,CAAC,KAAK,GAAG,IAAK,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,MAAO,KACnD,CAAC,KAAK,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,SACvC;gBACA;YACF;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,UAAU,CAAC,MAAM,EAAE,IAAK;gBAC/C,OAAO,KAAK,UAAU,CAAC,EAAE;gBACzB,IACE,KAAK,IAAI,KAAK,SACd,KAAK,IAAI,KAAK,YACd,KAAK,IAAI,KAAK,OACd;oBACA;gBACF;gBACA,IAAI,SAAS;gBACb,IAAI,6BAA6B,IAAI,CAAC,KAAK,KAAK,GAAG;oBACjD,SAAS;gBACX,OAAO,IAAI,sCAAsC,IAAI,CAAC,KAAK,KAAK,GAAG;oBACjE,SAAS;gBACX;gBACA,IAAI,QAAQ;oBACV,0DAA0D;oBAC1D,IAAI,KAAK,OAAO,KAAK,SAAS,KAAK,OAAO,KAAK,WAAW;wBACxD,KAAK,YAAY,CAAC,QAAQ,KAAK,KAAK;oBACtC,OAAO,IACL,KAAK,OAAO,KAAK,YACjB,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM;wBAAC;wBAAO;qBAAU,EAAE,MAAM,EAC1D;wBACA,gHAAgH;wBAChH,2CAA2C;wBAC3C,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;wBAClC,IAAI,YAAY,CAAC,QAAQ,KAAK,KAAK;wBACnC,KAAK,WAAW,CAAC;oBACnB;gBACF;YACF;QACF;IAEJ;IAEA,iBAAgB,CAAC,EAAE,IAAI;QACrB,IAAI,aAAa,IAAI,CAAC,aAAa,CAAC,GAAG,MAAM,MAAM;QACnD,IAAI,eAAe,GAAG;YACpB,OAAO;QACT;QACA,IAAI,iBAAiB;QACrB,IAAI,WAAW,IAAI,CAAC,mBAAmB,CAAC,GAAG;QAC3C,IAAI,CAAC,YAAY,CACf,UACA,CAAA,QAAU,kBAAkB,IAAI,CAAC,aAAa,CAAC,OAAO,MAAM,MAAM;QAEpE,OAAO,iBAAiB;IAC1B;IAEA;;;;;IAKE,GACF,qBAAoB,CAAC,EAAE,GAAG;QACxB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,wBAAwB,GAAG;YACtD;QACF;QAEA,4DAA4D;QAC5D,6DAA6D;QAC7D,mCAAmC;QACnC,EAAE;QACF,iEAAiE;QACjE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG;YAAC;SAAI,GAAG,SAAU,IAAI;YAClE,yEAAyE;YACzE,IAAI,cAAc,SAAU,CAAC;gBAC3B,OAAO,EAAE,qBAAqB;YAChC;YAEA,IAAI,SAAS,QAAQ,QAAQ,QAAQ;YACrC,IAAI,CAAC,QAAQ;gBACX,IAAI,aAAa;gBACjB,IAAI,YAAY,IAAI,CAAC,mBAAmB,CAAC,MAAM;oBAAC;oBAAM;iBAAK;gBAC3D,IAAI,CAAC,YAAY,CACf,WACA,CAAA,OAAS,cAAc,IAAI,CAAC,aAAa,CAAC,MAAM,MAAM;gBAExD,SAAS,aAAa,IAAI,CAAC,aAAa,CAAC,MAAM,MAAM,GAAG;YAC1D;YAEA,IAAI,QAAQ,WAAW,YAAY,OAAO;gBACxC,OAAO;YACT;YAEA,kFAAkF;YAClF,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,SAAS,CAAC,GAAG,cAAc;gBACxD,OAAO;YACT;YAEA,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,SAAS;gBACtC,OAAO;YACT;YAEA,uCAAuC;YACvC,IACE;mBAAI,KAAK,oBAAoB,CAAC;aAAS,CAAC,IAAI,CAC1C,CAAA,MAAO,IAAI,qBAAqB,GAElC;gBACA,OAAO;YACT;YAEA,IAAI,SAAS,IAAI,CAAC,eAAe,CAAC;YAElC,IAAI,CAAC,GAAG,CAAC,0BAA0B;YAEnC,IAAI,eAAe;YAEnB,IAAI,SAAS,eAAe,GAAG;gBAC7B,OAAO;YACT;YAEA,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,OAAO,IAAI;gBACtC,uDAAuD;gBACvD,0DAA0D;gBAC1D,qCAAqC;gBACrC,IAAI,IAAI,KAAK,oBAAoB,CAAC,KAAK,MAAM;gBAC7C,IAAI,MAAM,KAAK,oBAAoB,CAAC,OAAO,MAAM;gBACjD,IAAI,KAAK,KAAK,oBAAoB,CAAC,MAAM,MAAM,GAAG;gBAClD,IAAI,QAAQ,KAAK,oBAAoB,CAAC,SAAS,MAAM;gBACrD,IAAI,iBAAiB,IAAI,CAAC,eAAe,CAAC,MAAM;oBAC9C;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBAED,IAAI,aAAa;gBACjB,IAAI,SAAS,IAAI,CAAC,mBAAmB,CAAC,MAAM;oBAC1C;oBACA;oBACA;iBACD;gBAED,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;oBACtC,yEAAyE;oBACzE,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,IAAK;wBACpD,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,GAAG;4BAC/D,OAAO;wBACT;oBACF;oBAEA,yDAAyD;oBACzD,IACE,MAAM,CAAC,EAAE,CAAC,OAAO,KAAK,YACtB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,GAChD;wBACA,OAAO;oBACT;oBAEA;gBACF;gBAEA,IAAI,YAAY,IAAI,CAAC,aAAa,CAAC;gBAEnC,uEAAuE;gBACvE,IACE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,cAC1B,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,YAC/B;oBACA,OAAO;gBACT;gBAEA,IAAI,gBAAgB,UAAU,MAAM;gBACpC,IAAI,cAAc,IAAI,CAAC,eAAe,CAAC;gBACvC,IAAI,cAAc;oBAAC;oBAAQ;oBAAM;iBAAK,CAAC,MAAM,CAC3C,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc;gBAEhC,IAAI,cAAc,IAAI,CAAC,eAAe,CAAC,MAAM;gBAC7C,IAAI,gBAAgB,IAAI,CAAC,eAAe,CAAC,MAAM;gBAE/C,oDAAoD;gBACpD,MAAM,mBAAmB;oBACvB,MAAM,OAAO,EAAE;oBACf,IAAI,CAAC,iBAAiB,MAAM,KAAK,IAAI,MAAM,KAAK;wBAC9C,KAAK,IAAI,CAAC,CAAC,wBAAwB,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;oBACrD;oBACA,IAAI,CAAC,UAAU,KAAK,GAAG;wBACrB,KAAK,IAAI,CAAC,CAAC,qCAAqC,EAAE,GAAG,KAAK,EAAE,EAAE,CAAC,CAAC;oBAClE;oBACA,IAAI,QAAQ,KAAK,KAAK,CAAC,IAAI,IAAI;wBAC7B,KAAK,IAAI,CAAC,CAAC,8BAA8B,EAAE,MAAM,IAAI,EAAE,EAAE,CAAC,CAAC;oBAC7D;oBACA,IACE,CAAC,UACD,CAAC,iBACD,iBAAiB,OACjB,gBAAgB,MAChB,CAAC,QAAQ,KAAK,MAAM,CAAC,KACrB,cAAc,GACd;wBACA,KAAK,IAAI,CACP,CAAC,oCAAoC,EAAE,eAAe,MAAM,EAAE,IAAI,cAAc,EAAE,YAAY,CAAC,CAAC;oBAEpG;oBACA,IACE,CAAC,UACD,SAAS,MACT,cAAc,MAAM,IAAI,CAAC,oBAAoB,EAC7C;wBACA,KAAK,IAAI,CACP,CAAC,4CAA4C,EAAE,YAAY,CAAC,CAAC;oBAEjE;oBACA,IAAI,UAAU,MAAM,cAAc,MAAM,IAAI,CAAC,oBAAoB,EAAE;wBACjE,KAAK,IAAI,CACP,CAAC,2CAA2C,EAAE,YAAY,CAAC,CAAC;oBAEhE;oBACA,IAAI,AAAC,eAAe,KAAK,gBAAgB,MAAO,aAAa,GAAG;wBAC9D,KAAK,IAAI,CACP,CAAC,8BAA8B,EAAE,WAAW,gBAAgB,EAAE,cAAc,CAAC,CAAC;oBAElF;oBACA,IAAI,QAAQ,KAAK,gBAAgB,GAAG;wBAClC,KAAK,IAAI,CACP,CAAC,wBAAwB,EAAE,IAAI,cAAc,EAAE,YAAY,CAAC,CAAC;oBAEjE;oBAEA,IAAI,KAAK,MAAM,EAAE;wBACf,IAAI,CAAC,GAAG,CAAC,iBAAiB;wBAC1B,OAAO;oBACT;oBAEA,OAAO;gBACT;gBAEA,IAAI,eAAe;gBAEnB,kDAAkD;gBAClD,IAAI,UAAU,cAAc;oBAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,QAAQ,CAAC,MAAM,EAAE,IAAK;wBAC7C,IAAI,QAAQ,KAAK,QAAQ,CAAC,EAAE;wBAC5B,mEAAmE;wBACnE,IAAI,MAAM,QAAQ,CAAC,MAAM,GAAG,GAAG;4BAC7B,OAAO;wBACT;oBACF;oBACA,IAAI,WAAW,KAAK,oBAAoB,CAAC,MAAM,MAAM;oBACrD,8DAA8D;oBAC9D,IAAI,OAAO,UAAU;wBACnB,OAAO;oBACT;gBACF;gBACA,OAAO;YACT;YACA,OAAO;QACT;IACF;IAEA;;;;;;IAME,GACF,oBAAmB,CAAC,EAAE,MAAM;QAC1B,IAAI,wBAAwB,IAAI,CAAC,YAAY,CAAC,GAAG;QACjD,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC;QAC7B,MAAO,QAAQ,QAAQ,sBAAuB;YAC5C,IAAI,OAAO,IAAI,CAAC,IAAI,EAAE,MAAM,KAAK,SAAS,GAAG,MAAM,KAAK,EAAE,GAAG;gBAC3D,OAAO,IAAI,CAAC,iBAAiB,CAAC;YAChC,OAAO;gBACL,OAAO,IAAI,CAAC,YAAY,CAAC;YAC3B;QACF;IACF;IAEA;;;;;IAKE,GACF,eAAc,CAAC;QACb,IAAI,eAAe,IAAI,CAAC,mBAAmB,CAAC,GAAG;YAAC;YAAM;SAAK;QAC3D,IAAI,CAAC,YAAY,CAAC,cAAc,SAAU,IAAI;YAC5C,IAAI,eAAe,IAAI,CAAC,eAAe,CAAC,QAAQ;YAChD,IAAI,cAAc;gBAChB,IAAI,CAAC,GAAG,CAAC,0CAA0C;YACrD;YACA,OAAO;QACT;IACF;IAEA;;;;;;GAMC,GACD,wBAAuB,IAAI;QACzB,IAAI,KAAK,OAAO,IAAI,QAAQ,KAAK,OAAO,IAAI,MAAM;YAChD,OAAO;QACT;QACA,IAAI,UAAU,IAAI,CAAC,aAAa,CAAC,MAAM;QACvC,IAAI,CAAC,GAAG,CAAC,oCAAoC,SAAS,IAAI,CAAC,aAAa;QACxE,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW;IAC7D;IAEA,eAAc,IAAI;QAChB,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI;IAChC;IAEA,aAAY,IAAI;QACd,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC;IAC/B;IAEA,oBAAmB,IAAI;QACrB,+FAA+F;QAC/F,OACE,CAAC,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,OAAO,IAAI,MAAM,KAC5C,CAAC,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,UAAU,IAAI,QAAQ,KACjD,CAAC,KAAK,YAAY,CAAC,aACnB,wEAAwE;QACxE,CAAC,CAAC,KAAK,YAAY,CAAC,kBAClB,KAAK,YAAY,CAAC,kBAAkB,UACnC,KAAK,SAAS,IACb,KAAK,SAAS,CAAC,QAAQ,IACvB,KAAK,SAAS,CAAC,QAAQ,CAAC,iBAAkB;IAElD;IAEA;;;;;;;;;;;IAWE,GACF;QACE,iEAAiE;QACjE,IAAI,IAAI,CAAC,gBAAgB,GAAG,GAAG;YAC7B,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,KAAK,MAAM;YACxD,IAAI,UAAU,IAAI,CAAC,gBAAgB,EAAE;gBACnC,MAAM,IAAI,MACR,gCAAgC,UAAU;YAE9C;QACF;QAEA,6BAA6B;QAC7B,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI;QAEpC,mDAAmD;QACnD,IAAI,SAAS,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;QAEjE,wCAAwC;QACxC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI;QAE7B,IAAI,CAAC,aAAa;QAElB,IAAI,WAAW,IAAI,CAAC,mBAAmB,CAAC;QACxC,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,aAAa,GAAG,SAAS,KAAK;QAEnC,IAAI,iBAAiB,IAAI,CAAC,YAAY;QACtC,IAAI,CAAC,gBAAgB;YACnB,OAAO;QACT;QAEA,IAAI,CAAC,GAAG,CAAC,cAAc,eAAe,SAAS;QAE/C,IAAI,CAAC,mBAAmB,CAAC;QAEzB,8EAA8E;QAC9E,2EAA2E;QAC3E,yBAAyB;QACzB,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,IAAI,aAAa,eAAe,oBAAoB,CAAC;YACrD,IAAI,WAAW,MAAM,EAAE;gBACrB,SAAS,OAAO,GAAG,UAAU,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI;YACnD;QACF;QAEA,IAAI,cAAc,eAAe,WAAW;QAC5C,OAAO;YACL,OAAO,IAAI,CAAC,aAAa;YACzB,QAAQ,SAAS,MAAM,IAAI,IAAI,CAAC,cAAc;YAC9C,KAAK,IAAI,CAAC,WAAW;YACrB,MAAM,IAAI,CAAC,YAAY;YACvB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B;YACA,QAAQ,YAAY,MAAM;YAC1B,SAAS,SAAS,OAAO;YACzB,UAAU,SAAS,QAAQ,IAAI,IAAI,CAAC,gBAAgB;YACpD,eAAe,SAAS,aAAa;QACvC;IACF;AACF;AAEA,wCAAgC;IAC9B,yCAAyC,GACzC,iBAAiB,GACjB,OAAO,OAAO,GAAG;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2190, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/%40mozilla/readability/Readability-readerable.js"], "sourcesContent": ["/*\n * Copyright (c) 2010 Arc90 Inc\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/*\n * This code is heavily based on Arc90's readability.js (1.7.1) script\n * available at: http://code.google.com/p/arc90labs-readability\n */\n\nvar REGEXPS = {\n  // NOTE: These two regular expressions are duplicated in\n  // Readability.js. Please keep both copies in sync.\n  unlikelyCandidates:\n    /-ad-|ai2html|banner|breadcrumbs|combx|comment|community|cover-wrap|disqus|extra|footer|gdpr|header|legends|menu|related|remark|replies|rss|shoutbox|sidebar|skyscraper|social|sponsor|supplemental|ad-break|agegate|pagination|pager|popup|yom-remote/i,\n  okMaybeItsACandidate: /and|article|body|column|content|main|shadow/i,\n};\n\nfunction isNodeVisible(node) {\n  // Have to null-check node.style and node.className.includes to deal with SVG and MathML nodes.\n  return (\n    (!node.style || node.style.display != \"none\") &&\n    !node.hasAttribute(\"hidden\") &&\n    //check for \"fallback-image\" so that wikimedia math images are displayed\n    (!node.hasAttribute(\"aria-hidden\") ||\n      node.getAttribute(\"aria-hidden\") != \"true\" ||\n      (node.className &&\n        node.className.includes &&\n        node.className.includes(\"fallback-image\")))\n  );\n}\n\n/**\n * Decides whether or not the document is reader-able without parsing the whole thing.\n * @param {Object} options Configuration object.\n * @param {number} [options.minContentLength=140] The minimum node content length used to decide if the document is readerable.\n * @param {number} [options.minScore=20] The minumum cumulated 'score' used to determine if the document is readerable.\n * @param {Function} [options.visibilityChecker=isNodeVisible] The function used to determine if a node is visible.\n * @return {boolean} Whether or not we suspect Readability.parse() will suceeed at returning an article object.\n */\nfunction isProbablyReaderable(doc, options = {}) {\n  // For backward compatibility reasons 'options' can either be a configuration object or the function used\n  // to determine if a node is visible.\n  if (typeof options == \"function\") {\n    options = { visibilityChecker: options };\n  }\n\n  var defaultOptions = {\n    minScore: 20,\n    minContentLength: 140,\n    visibilityChecker: isNodeVisible,\n  };\n  options = Object.assign(defaultOptions, options);\n\n  var nodes = doc.querySelectorAll(\"p, pre, article\");\n\n  // Get <div> nodes which have <br> node(s) and append them into the `nodes` variable.\n  // Some articles' DOM structures might look like\n  // <div>\n  //   Sentences<br>\n  //   <br>\n  //   Sentences<br>\n  // </div>\n  var brNodes = doc.querySelectorAll(\"div > br\");\n  if (brNodes.length) {\n    var set = new Set(nodes);\n    [].forEach.call(brNodes, function (node) {\n      set.add(node.parentNode);\n    });\n    nodes = Array.from(set);\n  }\n\n  var score = 0;\n  // This is a little cheeky, we use the accumulator 'score' to decide what to return from\n  // this callback:\n  return [].some.call(nodes, function (node) {\n    if (!options.visibilityChecker(node)) {\n      return false;\n    }\n\n    var matchString = node.className + \" \" + node.id;\n    if (\n      REGEXPS.unlikelyCandidates.test(matchString) &&\n      !REGEXPS.okMaybeItsACandidate.test(matchString)\n    ) {\n      return false;\n    }\n\n    if (node.matches(\"li p\")) {\n      return false;\n    }\n\n    var textContentLength = node.textContent.trim().length;\n    if (textContentLength < options.minContentLength) {\n      return false;\n    }\n\n    score += Math.sqrt(textContentLength - options.minContentLength);\n\n    if (score > options.minScore) {\n      return true;\n    }\n    return false;\n  });\n}\n\nif (typeof module === \"object\") {\n  /* eslint-disable-next-line no-redeclare */\n  /* global module */\n  module.exports = isProbablyReaderable;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GAED;;;CAGC,GAED,IAAI,UAAU;IACZ,wDAAwD;IACxD,mDAAmD;IACnD,oBACE;IACF,sBAAsB;AACxB;AAEA,SAAS,cAAc,IAAI;IACzB,+FAA+F;IAC/F,OACE,CAAC,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,OAAO,IAAI,MAAM,KAC5C,CAAC,KAAK,YAAY,CAAC,aACnB,wEAAwE;IACxE,CAAC,CAAC,KAAK,YAAY,CAAC,kBAClB,KAAK,YAAY,CAAC,kBAAkB,UACnC,KAAK,SAAS,IACb,KAAK,SAAS,CAAC,QAAQ,IACvB,KAAK,SAAS,CAAC,QAAQ,CAAC,iBAAkB;AAElD;AAEA;;;;;;;CAOC,GACD,SAAS,qBAAqB,GAAG,EAAE,UAAU,CAAC,CAAC;IAC7C,yGAAyG;IACzG,qCAAqC;IACrC,IAAI,OAAO,WAAW,YAAY;QAChC,UAAU;YAAE,mBAAmB;QAAQ;IACzC;IAEA,IAAI,iBAAiB;QACnB,UAAU;QACV,kBAAkB;QAClB,mBAAmB;IACrB;IACA,UAAU,OAAO,MAAM,CAAC,gBAAgB;IAExC,IAAI,QAAQ,IAAI,gBAAgB,CAAC;IAEjC,qFAAqF;IACrF,gDAAgD;IAChD,QAAQ;IACR,kBAAkB;IAClB,SAAS;IACT,kBAAkB;IAClB,SAAS;IACT,IAAI,UAAU,IAAI,gBAAgB,CAAC;IACnC,IAAI,QAAQ,MAAM,EAAE;QAClB,IAAI,MAAM,IAAI,IAAI;QAClB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,SAAU,IAAI;YACrC,IAAI,GAAG,CAAC,KAAK,UAAU;QACzB;QACA,QAAQ,MAAM,IAAI,CAAC;IACrB;IAEA,IAAI,QAAQ;IACZ,wFAAwF;IACxF,iBAAiB;IACjB,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,SAAU,IAAI;QACvC,IAAI,CAAC,QAAQ,iBAAiB,CAAC,OAAO;YACpC,OAAO;QACT;QAEA,IAAI,cAAc,KAAK,SAAS,GAAG,MAAM,KAAK,EAAE;QAChD,IACE,QAAQ,kBAAkB,CAAC,IAAI,CAAC,gBAChC,CAAC,QAAQ,oBAAoB,CAAC,IAAI,CAAC,cACnC;YACA,OAAO;QACT;QAEA,IAAI,KAAK,OAAO,CAAC,SAAS;YACxB,OAAO;QACT;QAEA,IAAI,oBAAoB,KAAK,WAAW,CAAC,IAAI,GAAG,MAAM;QACtD,IAAI,oBAAoB,QAAQ,gBAAgB,EAAE;YAChD,OAAO;QACT;QAEA,SAAS,KAAK,IAAI,CAAC,oBAAoB,QAAQ,gBAAgB;QAE/D,IAAI,QAAQ,QAAQ,QAAQ,EAAE;YAC5B,OAAO;QACT;QACA,OAAO;IACT;AACF;AAEA,wCAAgC;IAC9B,yCAAyC,GACzC,iBAAiB,GACjB,OAAO,OAAO,GAAG;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2288, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/node_modules/%40mozilla/readability/index.js"], "sourcesContent": ["/* eslint-env node */\nvar Readability = require(\"./Readability\");\nvar isProbablyReaderable = require(\"./Readability-readerable\");\n\nmodule.exports = {\n  Readability,\n  isProbablyReaderable,\n};\n"], "names": [], "mappings": "AAAA,mBAAmB,GACnB,IAAI;AACJ,IAAI;AAEJ,OAAO,OAAO,GAAG;IACf;IACA;AACF", "ignoreList": [0], "debugId": null}}]}