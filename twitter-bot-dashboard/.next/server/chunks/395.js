"use strict";exports.id=395,exports.ids=[395],exports.modules={73395:(e,t,s)=>{s.d(t,{Yl:()=>t_});let r="https://api.x.com/2/",i="https://api.x.com/labs/2/",a="https://api.x.com/1.1/",n="https://upload.x.com/1.1/",o="https://ads-api.x.com/12/",u="https://ads-api-sandbox.twitter.com/12/";class l{constructor({realData:e,rateLimit:t,instance:s,queryParams:r,sharedParams:i}){this._maxResultsWhenFetchLast=100,this._realData=e,this._rateLimit=t,this._instance=s,this._queryParams=r,this._sharedParams=i}get _isRateLimitOk(){return!!(!this._rateLimit||1e3*this._rateLimit.reset<Date.now())||this._rateLimit.remaining>0}makeRequest(e){return this._instance.get(this.getEndpoint(),e,{fullResponse:!0,params:this._sharedParams})}makeNewInstanceFromResult(e,t){return new this.constructor({realData:e.data,rateLimit:e.rateLimit,instance:this._instance,queryParams:t,sharedParams:this._sharedParams})}getEndpoint(){return this._endpoint}injectQueryParams(e){return{...e?{max_results:e}:{},...this._queryParams}}async next(e){let t=this.getNextQueryParams(e),s=await this.makeRequest(t);return this.makeNewInstanceFromResult(s,t)}async fetchNext(e){let t=this.getNextQueryParams(e),s=await this.makeRequest(t);return await this.refreshInstanceFromResult(s,!0),this}async fetchLast(e=1/0){let t=this.getNextQueryParams(this._maxResultsWhenFetchLast),s=0;for(;s<e&&this._isRateLimitOk;){let e=await this.makeRequest(t);if(await this.refreshInstanceFromResult(e,!0),s+=this.getPageLengthFromRequest(e),this.isFetchLastOver(e))break;t=this.getNextQueryParams(this._maxResultsWhenFetchLast)}return this}get rateLimit(){var e;return{...null!=(e=this._rateLimit)?e:{}}}get data(){return this._realData}get done(){return!this.canFetchNextPage(this._realData)}*[Symbol.iterator](){yield*this.getItemArray()}async *[Symbol.asyncIterator](){yield*this.getItemArray();let e=this,t=this.canFetchNextPage(this._realData);for(;t&&this._isRateLimitOk&&e.getItemArray().length>0;){let s=await e.next(this._maxResultsWhenFetchLast);this.refreshInstanceFromResult({data:s._realData,headers:{},rateLimit:s._rateLimit},!0),t=this.canFetchNextPage(s._realData);let r=s.getItemArray();yield*r,e=s}}async *fetchAndIterate(){for(let e of this.getItemArray())yield[e,this];let e=this,t=this.canFetchNextPage(this._realData);for(;t&&this._isRateLimitOk&&e.getItemArray().length>0;){let s=await e.next(this._maxResultsWhenFetchLast);for(let e of(this.refreshInstanceFromResult({data:s._realData,headers:{},rateLimit:s._rateLimit},!0),t=this.canFetchNextPage(s._realData),s.getItemArray()))yield[e,s];this._rateLimit=s._rateLimit,e=s}}}class d extends l{async previous(e){let t=this.getPreviousQueryParams(e),s=await this.makeRequest(t);return this.makeNewInstanceFromResult(s,t)}async fetchPrevious(e){let t=this.getPreviousQueryParams(e),s=await this.makeRequest(t);return await this.refreshInstanceFromResult(s,!1),this}}let c=l;class h extends c{getNextQueryParams(e){var t;return{...this._queryParams,cursor:null!=(t=this._realData.next_cursor_str)?t:this._realData.next_cursor,...e?{count:e}:{}}}isFetchLastOver(e){return!this.canFetchNextPage(e.data)}canFetchNextPage(e){return!this.isNextCursorInvalid(e.next_cursor)||!this.isNextCursorInvalid(e.next_cursor_str)}isNextCursorInvalid(e){return void 0===e||0===e||-1===e||"0"===e||"-1"===e}}class m extends h{constructor(){super(...arguments),this._endpoint="direct_messages/events/list.json"}refreshInstanceFromResult(e,t){let s=e.data;this._rateLimit=e.rateLimit,t&&(this._realData.events.push(...s.events),this._realData.next_cursor=s.next_cursor)}getPageLengthFromRequest(e){return e.data.events.length}getItemArray(){return this.events}get events(){return this._realData.events}}class p extends h{constructor(){super(...arguments),this._endpoint="direct_messages/welcome_messages/list.json"}refreshInstanceFromResult(e,t){let s=e.data;this._rateLimit=e.rateLimit,t&&(this._realData.welcome_messages.push(...s.welcome_messages),this._realData.next_cursor=s.next_cursor)}getPageLengthFromRequest(e){return e.data.welcome_messages.length}getItemArray(){return this.welcomeMessages}get welcomeMessages(){return this._realData.welcome_messages}}!function(e){e.Jpeg="image/jpeg",e.Mp4="video/mp4",e.Mov="video/quicktime",e.Gif="image/gif",e.Png="image/png",e.Srt="text/plain",e.Webp="image/webp"}(v||(v={})),function(e){e.Create="message_create",e.WelcomeCreate="welcome_message"}(b||(b={})),function(e){e.Request="request",e.PartialResponse="partial-response",e.Response="response"}(R||(R={}));class g extends Error{constructor(){super(...arguments),this.error=!0}}class _ extends g{constructor(e,t){super(e),this.type=R.Request,Error.captureStackTrace(this,this.constructor),Object.defineProperty(this,"_options",{value:t})}get request(){return this._options.request}get requestError(){return this._options.requestError}toJSON(){return{type:this.type,error:this.requestError}}}class f extends g{constructor(e,t){super(e),this.type=R.PartialResponse,Error.captureStackTrace(this,this.constructor),Object.defineProperty(this,"_options",{value:t})}get request(){return this._options.request}get response(){return this._options.response}get responseError(){return this._options.responseError}get rawContent(){return this._options.rawContent}toJSON(){return{type:this.type,error:this.responseError}}}class w extends g{constructor(e,t){if(super(e),this.type=R.Response,Error.captureStackTrace(this,this.constructor),Object.defineProperty(this,"_options",{value:t}),this.code=t.code,this.headers=t.headers,this.rateLimit=t.rateLimit,t.data&&"object"==typeof t.data&&"error"in t.data&&!t.data.errors){let e={...t.data};e.errors=[{code:x.InternalError,message:e.error}],this.data=e}else this.data=t.data}get request(){return this._options.request}get response(){return this._options.response}hasErrorCode(...e){let t=this.errors;if(!(null==t?void 0:t.length))return!1;if("code"in t[0])return t.some(t=>e.includes(t.code));let s=this.data;return e.includes(s.type)}get errors(){var e;return null==(e=this.data)?void 0:e.errors}get rateLimitError(){return 420===this.code||429===this.code}get isAuthError(){return 401===this.code||this.hasErrorCode(x.AuthTimestampInvalid,x.AuthenticationFail,x.BadAuthenticationData,x.InvalidOrExpiredToken)}toJSON(){return{type:this.type,code:this.code,error:this.data,rateLimit:this.rateLimit,headers:this.headers}}}!function(e){e[e.InvalidCoordinates=3]="InvalidCoordinates",e[e.NoLocationFound=13]="NoLocationFound",e[e.AuthenticationFail=32]="AuthenticationFail",e[e.InvalidOrExpiredToken=89]="InvalidOrExpiredToken",e[e.UnableToVerifyCredentials=99]="UnableToVerifyCredentials",e[e.AuthTimestampInvalid=135]="AuthTimestampInvalid",e[e.BadAuthenticationData=215]="BadAuthenticationData",e[e.NoUserMatch=17]="NoUserMatch",e[e.UserNotFound=50]="UserNotFound",e[e.ResourceNotFound=34]="ResourceNotFound",e[e.TweetNotFound=144]="TweetNotFound",e[e.TweetNotVisible=179]="TweetNotVisible",e[e.NotAllowedResource=220]="NotAllowedResource",e[e.MediaIdNotFound=325]="MediaIdNotFound",e[e.TweetNoLongerAvailable=421]="TweetNoLongerAvailable",e[e.TweetViolatedRules=422]="TweetViolatedRules",e[e.TargetUserSuspended=63]="TargetUserSuspended",e[e.YouAreSuspended=64]="YouAreSuspended",e[e.AccountUpdateFailed=120]="AccountUpdateFailed",e[e.NoSelfSpamReport=36]="NoSelfSpamReport",e[e.NoSelfMute=271]="NoSelfMute",e[e.AccountLocked=326]="AccountLocked",e[e.RateLimitExceeded=88]="RateLimitExceeded",e[e.NoDMRightForApp=93]="NoDMRightForApp",e[e.OverCapacity=130]="OverCapacity",e[e.InternalError=131]="InternalError",e[e.TooManyFollowings=161]="TooManyFollowings",e[e.TweetLimitExceeded=185]="TweetLimitExceeded",e[e.DuplicatedTweet=187]="DuplicatedTweet",e[e.TooManySpamReports=205]="TooManySpamReports",e[e.RequestLooksLikeSpam=226]="RequestLooksLikeSpam",e[e.NoWriteRightForApp=261]="NoWriteRightForApp",e[e.TweetActionsDisabled=425]="TweetActionsDisabled",e[e.TweetRepliesRestricted=433]="TweetRepliesRestricted",e[e.NamedParameterMissing=38]="NamedParameterMissing",e[e.InvalidAttachmentUrl=44]="InvalidAttachmentUrl",e[e.TweetTextTooLong=186]="TweetTextTooLong",e[e.MissingUrlParameter=195]="MissingUrlParameter",e[e.NoMultipleGifs=323]="NoMultipleGifs",e[e.InvalidMediaIds=324]="InvalidMediaIds",e[e.InvalidUrl=407]="InvalidUrl",e[e.TooManyTweetAttachments=386]="TooManyTweetAttachments",e[e.StatusAlreadyFavorited=139]="StatusAlreadyFavorited",e[e.FollowRequestAlreadySent=160]="FollowRequestAlreadySent",e[e.CannotUnmuteANonMutedAccount=272]="CannotUnmuteANonMutedAccount",e[e.TweetAlreadyRetweeted=327]="TweetAlreadyRetweeted",e[e.ReplyToDeletedTweet=385]="ReplyToDeletedTweet",e[e.DMReceiverNotFollowingYou=150]="DMReceiverNotFollowingYou",e[e.UnableToSendDM=151]="UnableToSendDM",e[e.MustAllowDMFromAnyone=214]="MustAllowDMFromAnyone",e[e.CannotSendDMToThisUser=349]="CannotSendDMToThisUser",e[e.DMTextTooLong=354]="DMTextTooLong",e[e.SubscriptionAlreadyExists=355]="SubscriptionAlreadyExists",e[e.CallbackUrlNotApproved=415]="CallbackUrlNotApproved",e[e.SuspendedApplication=416]="SuspendedApplication",e[e.OobOauthIsNotAllowed=417]="OobOauthIsNotAllowed"}(x||(x={})),function(e){e.InvalidRequest="https://developer.x.com/en/support/x-api/error-troubleshooting#invalid-request",e.ClientForbidden="https://developer.x.com/en/support/x-api/error-troubleshooting#client-forbidden",e.UnsupportedAuthentication="https://developer.x.com/en/support/x-api/error-troubleshooting#unsupported-authentication",e.InvalidRules="https://developer.x.com/en/support/x-api/error-troubleshooting#invalid-rules",e.TooManyRules="https://developer.x.com/en/support/x-api/error-troubleshooting#rule-cap",e.DuplicatedRules="https://developer.x.com/en/support/x-api/error-troubleshooting#duplicate-rules",e.RateLimitExceeded="https://developer.x.com/en/support/x-api/error-troubleshooting#usage-capped",e.ConnectionError="https://developer.x.com/en/support/x-api/error-troubleshooting#streaming-connection",e.ClientDisconnected="https://developer.x.com/en/support/x-api/error-troubleshooting#client-disconnected",e.TwitterDisconnectedYou="https://developer.x.com/en/support/x-api/error-troubleshooting#operational-disconnect",e.ResourceNotFound="https://developer.x.com/en/support/x-api/error-troubleshooting#resource-not-found",e.ResourceUnauthorized="https://developer.x.com/en/support/x-api/error-troubleshooting#not-authorized-for-resource",e.DisallowedResource="https://developer.x.com/en/support/x-api/error-troubleshooting#disallowed-resource"}(D||(D={})),function(e){e.Connected="connected",e.ConnectError="connect error",e.ConnectionError="connection error",e.ConnectionClosed="connection closed",e.ConnectionLost="connection lost",e.ReconnectAttempt="reconnect attempt",e.Reconnected="reconnected",e.ReconnectError="reconnect error",e.ReconnectLimitExceeded="reconnect limit exceeded",e.DataKeepAlive="data keep-alive",e.Data="data event content",e.DataError="data twitter error",e.TweetParseError="data tweet parse error",e.Error="stream error"}(L||(L={}));class y{constructor(e){this.value=e}}var v,b,R,x,D,L,k,q=s(29021);let T={debug:!1,deprecationWarnings:!0,logger:{log:console.log.bind(console)}};function S(e){let t={value:void 0,promise:e().then(e=>(t.value=e,e))};return t}function P(e){return Array.isArray(e)?e:[e]}function E(e){return"object"==typeof e&&"errors"in e&&!("data"in e)}function A(e){return!!Array.isArray(e)&&e.length>1||e.toString().includes(",")}let I=new Set;function j(e){if("undefined"==typeof console||!console.warn||!T.deprecationWarnings)return;let t=`${e.instance}-${e.method}-${e.problem}`;I.has(t)||(console.warn(`[twitter-api-v2] Deprecation warning: In ${e.instance}.${e.method}() call, ${e.problem}.
${e.resolution}.`),console.warn("To disable this message, import variable TwitterApiV2Settings from twitter-api-v2 and set TwitterApiV2Settings.deprecationWarnings to false."),I.add(t))}var M=s(94735),F=s(55591),C=s(74075);class U{constructor(e){this.requestData=e,this.requestErrorHandled=!1,this.responseData=[]}get hrefPathname(){let e=this.requestData.url;return e.hostname+e.pathname}isCompressionDisabled(){return!this.requestData.compression||"identity"===this.requestData.compression}isFormEncodedEndpoint(){return this.requestData.url.href.startsWith("https://api.x.com/oauth/")}createRequestError(e){return T.debug&&T.logger.log("Request error:",e),new _("Request failed.",{request:this.req,error:e})}createPartialResponseError(e,t){let s=this.res,r=`Request failed with partial response with HTTP code ${s.statusCode}`;return t?r+=" (connection abruptly closed)":r+=" (parse error)",new f(r,{request:this.req,response:this.res,responseError:e,rawContent:Buffer.concat(this.responseData).toString()})}formatV1Errors(e){return e.map(({code:e,message:t})=>`${t} (Twitter code ${e})`).join(", ")}formatV2Error(e){return`${e.title}: ${e.detail} (see ${e.type})`}createResponseError({res:e,data:t,rateLimit:s,code:r}){var i;T.debug&&(T.logger.log(`Request failed with code ${r}, data:`,t),T.logger.log("Response headers:",e.headers));let a=`Request failed with code ${r}`;if(null==(i=null==t?void 0:t.errors)?void 0:i.length){let e=t.errors;"object"==typeof e[0]&&"code"in e[0]?a+=" - "+this.formatV1Errors(e):a+=" - "+this.formatV2Error(t)}return new w(a,{code:r,data:t,headers:e.headers,request:this.req,response:e,rateLimit:s})}getResponseDataStream(e){if(this.isCompressionDisabled())return e;let t=(e.headers["content-encoding"]||"identity").trim().toLowerCase();if("br"===t){let t=C.createBrotliDecompress({flush:C.constants.BROTLI_OPERATION_FLUSH,finishFlush:C.constants.BROTLI_OPERATION_FLUSH});return e.pipe(t),t}if("gzip"===t){let t=C.createGunzip({flush:C.constants.Z_SYNC_FLUSH,finishFlush:C.constants.Z_SYNC_FLUSH});return e.pipe(t),t}if("deflate"===t){let t=C.createInflate({flush:C.constants.Z_SYNC_FLUSH,finishFlush:C.constants.Z_SYNC_FLUSH});return e.pipe(t),t}return e}detectResponseType(e){var t,s;return(null==(t=e.headers["content-type"])?void 0:t.includes("application/json"))||(null==(s=e.headers["content-type"])?void 0:s.includes("application/problem+json"))?"json":this.isFormEncodedEndpoint()?"url":"text"}getParsedResponse(e){let t=this.responseData,s=this.requestData.forceParseMode||this.detectResponseType(e);if("buffer"===s)return Buffer.concat(t);if("text"===s)return Buffer.concat(t).toString();if("json"===s){let e=Buffer.concat(t).toString();return e.length?JSON.parse(e):void 0}{if("url"!==s)return;let e=Buffer.concat(t).toString(),r={};for(let[t,s]of new URLSearchParams(e))r[t]=s;return r}}getRateLimitFromResponse(e){let t;return e.headers["x-rate-limit-limit"]&&(t={limit:Number(e.headers["x-rate-limit-limit"]),remaining:Number(e.headers["x-rate-limit-remaining"]),reset:Number(e.headers["x-rate-limit-reset"])},e.headers["x-app-limit-24hour-limit"]&&(t.day={limit:Number(e.headers["x-app-limit-24hour-limit"]),remaining:Number(e.headers["x-app-limit-24hour-remaining"]),reset:Number(e.headers["x-app-limit-24hour-reset"])}),this.requestData.rateLimitSaver&&this.requestData.rateLimitSaver(t)),t}onSocketEventHandler(e,t,s){let r=this.onSocketCloseHandler.bind(this,e);s.on("close",r),t.on("complete",()=>s.off("close",r))}onSocketCloseHandler(e){if(this.req.removeAllListeners("timeout"),!this.res&&!this.requestErrorHandled)return e(this.createRequestError(Error("Socket closed without any information.")))}requestErrorHandler(e,t){var s,r;null==(r=(s=this.requestData).requestEventDebugHandler)||r.call(s,"request-error",{requestError:t}),this.requestErrorHandled=!0,e(this.createRequestError(t))}timeoutErrorHandler(){this.requestErrorHandled=!0,this.req.destroy(Error("Request timeout."))}classicResponseHandler(e,t,s){this.res=s;let r=this.getResponseDataStream(s);r.on("data",e=>this.responseData.push(e)),r.on("end",this.onResponseEndHandler.bind(this,e,t)),r.on("close",this.onResponseCloseHandler.bind(this,e,t)),this.requestData.requestEventDebugHandler&&(this.requestData.requestEventDebugHandler("response",{res:s}),s.on("aborted",e=>this.requestData.requestEventDebugHandler("response-aborted",{error:e})),s.on("error",e=>this.requestData.requestEventDebugHandler("response-error",{error:e})),s.on("close",()=>this.requestData.requestEventDebugHandler("response-close",{data:this.responseData})),s.on("end",()=>this.requestData.requestEventDebugHandler("response-end")))}onResponseEndHandler(e,t){let s,r=this.getRateLimitFromResponse(this.res);try{s=this.getParsedResponse(this.res)}catch(e){t(this.createPartialResponseError(e,!1));return}let i=this.res.statusCode;if(i>=400)return void t(this.createResponseError({data:s,res:this.res,rateLimit:r,code:i}));T.debug&&(T.logger.log(`[${this.requestData.options.method} ${this.hrefPathname}]: Request succeeds with code ${this.res.statusCode}`),T.logger.log("Response body:",s)),e({data:s,headers:this.res.headers,rateLimit:r})}onResponseCloseHandler(e,t){let s=this.res;if(s.aborted)try{return this.getParsedResponse(this.res),this.onResponseEndHandler(e,t)}catch(e){return t(this.createPartialResponseError(e,!0))}if(!s.complete)return t(this.createPartialResponseError(Error("Response has been interrupted before response could be parsed."),!0))}streamResponseHandler(e,t,s){if(s.statusCode<400){T.debug&&T.logger.log(`[${this.requestData.options.method} ${this.hrefPathname}]: Request succeeds with code ${s.statusCode} (starting stream)`);let t=this.getResponseDataStream(s);e({req:this.req,res:t,originalResponse:s,requestData:this.requestData})}else this.classicResponseHandler(()=>void 0,t,s)}debugRequest(){let e=this.requestData.url;T.logger.log(`[${this.requestData.options.method} ${this.hrefPathname}]`,this.requestData.options),e.search&&T.logger.log("Request parameters:",[...e.searchParams.entries()].map(([e,t])=>`${e}: ${t}`)),this.requestData.body&&T.logger.log("Request body:",this.requestData.body)}buildRequest(){var e;let t=this.requestData.url,s=t.username?`${t.username}:${t.password}`:void 0,r=null!=(e=this.requestData.options.headers)?e:{};!0===this.requestData.compression||"brotli"===this.requestData.compression?r["accept-encoding"]="br;q=1.0, gzip;q=0.8, deflate;q=0.5, *;q=0.1":"gzip"===this.requestData.compression?r["accept-encoding"]="gzip;q=1, deflate;q=0.5, *;q=0.1":"deflate"===this.requestData.compression&&(r["accept-encoding"]="deflate;q=1, *;q=0.1"),T.debug&&this.debugRequest(),this.req=(0,F.request)({...this.requestData.options,host:t.hostname,port:t.port||void 0,path:t.pathname+t.search,protocol:t.protocol,auth:s,headers:r})}registerRequestEventDebugHandlers(e){e.on("close",()=>this.requestData.requestEventDebugHandler("close")),e.on("abort",()=>this.requestData.requestEventDebugHandler("abort")),e.on("socket",e=>{this.requestData.requestEventDebugHandler("socket",{socket:e}),e.on("error",t=>this.requestData.requestEventDebugHandler("socket-error",{socket:e,error:t})),e.on("connect",()=>this.requestData.requestEventDebugHandler("socket-connect",{socket:e})),e.on("close",t=>this.requestData.requestEventDebugHandler("socket-close",{socket:e,withError:t})),e.on("end",()=>this.requestData.requestEventDebugHandler("socket-end",{socket:e})),e.on("lookup",(...t)=>this.requestData.requestEventDebugHandler("socket-lookup",{socket:e,data:t})),e.on("timeout",()=>this.requestData.requestEventDebugHandler("socket-timeout",{socket:e}))})}makeRequest(){return this.buildRequest(),new Promise((e,t)=>{let s=e=>{r.emit("complete"),t(e)},r=new M.EventEmitter,i=this.req;i.on("error",this.requestErrorHandler.bind(this,s)),i.on("socket",this.onSocketEventHandler.bind(this,s,r)),i.on("response",this.classicResponseHandler.bind(this,t=>{r.emit("complete"),e(t)},s)),this.requestData.options.timeout&&i.on("timeout",this.timeoutErrorHandler.bind(this)),this.requestData.requestEventDebugHandler&&this.registerRequestEventDebugHandlers(i),this.requestData.body&&i.write(this.requestData.body),i.end()})}async makeRequestAsStream(){let{req:e,res:t,requestData:s,originalResponse:r}=await this.makeRequestAndResolveWhenReady();return new $(s,{req:e,res:t,originalResponse:r})}makeRequestAndResolveWhenReady(){return this.buildRequest(),new Promise((e,t)=>{let s=this.req;s.on("error",this.requestErrorHandler.bind(this,t)),s.on("response",this.streamResponseHandler.bind(this,e,t)),this.requestData.body&&s.write(this.requestData.body),s.end()})}}class O extends M.EventEmitter{constructor(e){super(),this.stream=e,this.stack=[],this.onStreamData=this.onStreamData.bind(this),this.onStreamError=this.onStreamError.bind(this),this.onceNewEvent=this.once.bind(this,"event"),e.on(L.Data,this.onStreamData),e.on(L.ConnectionError,this.onStreamError),e.on(L.TweetParseError,this.onStreamError),e.on(L.ConnectionClosed,this.onStreamError)}nextEvent(){return new Promise(this.onceNewEvent)}hasStack(){return this.stack.length>0}popStack(){let e=this.stack;return this.stack=[],e}destroy(){this.removeAllListeners(),this.stream.off(L.Data,this.onStreamData),this.stream.off(L.ConnectionError,this.onStreamError),this.stream.off(L.TweetParseError,this.onStreamError),this.stream.off(L.ConnectionClosed,this.onStreamError)}emitEvent(e,t){this.emit("event",{type:e,payload:t})}onStreamError(e){this.emitEvent("error",e)}onStreamData(e){this.stack.push(e),this.emitEvent("data",e)}}class N extends M.EventEmitter{constructor(){super(...arguments),this.currentMessage=""}push(e){this.currentMessage+=e;let t=(e=this.currentMessage).length,s=0,r=0;for(;r<t;){if("\r\n"===e.slice(r,r+2)){let t=e.slice(s,r);if(s=r+=2,!t.length)continue;try{let e=JSON.parse(t);if(e){this.emit(k.ParsedData,e);continue}}catch(e){this.emit(k.ParseError,e)}}r++}this.currentMessage=e.slice(s,t)}reset(){this.currentMessage=""}}!function(e){e.ParsedData="parsed data",e.ParseError="parse error"}(k||(k={}));let B=[5,15,30,60,90,120,180,300,600,900],H=e=>e>B.length?901e3:1e3*B[e-1];class W extends M.EventEmitter{constructor(e,t){super(),this.requestData=e,this.autoReconnect=!1,this.autoReconnectRetries=5,this.keepAliveTimeoutMs=12e4,this.nextRetryTimeout=H,this.parser=new N,this.connectionProcessRunning=!1,this.onKeepAliveTimeout=this.onKeepAliveTimeout.bind(this),this.initEventsFromParser(),t&&(this.req=t.req,this.res=t.res,this.originalResponse=t.originalResponse,this.initEventsFromRequest())}on(e,t){return super.on(e,t)}initEventsFromRequest(){if(!this.req||!this.res)throw Error("TweetStream error: You cannot init TweetStream without a request and response object.");let e=e=>{this.emit(L.ConnectionError,e),this.emit(L.Error,{type:L.ConnectionError,error:e,message:"Connection lost or closed by Twitter."}),this.onConnectionError()};this.req.on("error",e),this.res.on("error",e),this.res.on("close",()=>e(Error("Connection closed by Twitter."))),this.res.on("data",e=>{if(this.resetKeepAliveTimeout(),"\r\n"===e.toString())return this.emit(L.DataKeepAlive);this.parser.push(e.toString())}),this.resetKeepAliveTimeout()}initEventsFromParser(){let e=this.requestData.payloadIsError;this.parser.on(k.ParsedData,t=>{e&&e(t)?(this.emit(L.DataError,t),this.emit(L.Error,{type:L.DataError,error:t,message:"Twitter sent a payload that is detected as an error payload."})):this.emit(L.Data,t)}),this.parser.on(k.ParseError,e=>{this.emit(L.TweetParseError,e),this.emit(L.Error,{type:L.TweetParseError,error:e,message:"Failed to parse stream data."})})}resetKeepAliveTimeout(){this.unbindKeepAliveTimeout(),this.keepAliveTimeoutMs!==1/0&&(this.keepAliveTimeout=setTimeout(this.onKeepAliveTimeout,this.keepAliveTimeoutMs))}onKeepAliveTimeout(){this.emit(L.ConnectionLost),this.onConnectionError()}unbindTimeouts(){this.unbindRetryTimeout(),this.unbindKeepAliveTimeout()}unbindKeepAliveTimeout(){this.keepAliveTimeout&&(clearTimeout(this.keepAliveTimeout),this.keepAliveTimeout=void 0)}unbindRetryTimeout(){this.retryTimeout&&(clearTimeout(this.retryTimeout),this.retryTimeout=void 0)}closeWithoutEmit(){this.unbindTimeouts(),this.res&&(this.res.removeAllListeners(),this.res.destroy()),this.req&&(this.req.removeAllListeners(),this.req.destroy())}close(){this.emit(L.ConnectionClosed),this.closeWithoutEmit()}destroy(){this.removeAllListeners(),this.close()}async clone(){let e=new U(this.requestData),t=await e.makeRequestAsStream();for(let e of this.eventNames())for(let s of this.listeners(e))t.on(e,s);return t}async connect(e={}){void 0!==e.autoReconnect&&(this.autoReconnect=e.autoReconnect),void 0!==e.autoReconnectRetries&&(this.autoReconnectRetries="unlimited"===e.autoReconnectRetries?1/0:e.autoReconnectRetries),void 0!==e.keepAliveTimeout&&(this.keepAliveTimeoutMs="disable"===e.keepAliveTimeout?1/0:e.keepAliveTimeout),void 0!==e.nextRetryTimeout&&(this.nextRetryTimeout=e.nextRetryTimeout),this.unbindTimeouts();try{await this.reconnect()}catch(e){if(this.emit(L.ConnectError,0),this.emit(L.Error,{type:L.ConnectError,error:e,message:"Connect error - Initial connection just failed."}),this.autoReconnect)this.makeAutoReconnectRetry(0,e);else throw e}return this}async reconnect(){if(this.connectionProcessRunning)throw Error("Connection process is already running.");this.connectionProcessRunning=!0;try{let e=!0;this.req&&(e=!1,this.closeWithoutEmit());let{req:t,res:s,originalResponse:r}=await new U(this.requestData).makeRequestAndResolveWhenReady();this.req=t,this.res=s,this.originalResponse=r,this.emit(e?L.Connected:L.Reconnected),this.parser.reset(),this.initEventsFromRequest()}finally{this.connectionProcessRunning=!1}}async onConnectionError(e=0){if(this.unbindTimeouts(),this.closeWithoutEmit(),!this.autoReconnect)return void this.emit(L.ConnectionClosed);if(e>=this.autoReconnectRetries){this.emit(L.ReconnectLimitExceeded),this.emit(L.ConnectionClosed);return}try{this.emit(L.ReconnectAttempt,e),await this.reconnect()}catch(t){this.emit(L.ReconnectError,e),this.emit(L.Error,{type:L.ReconnectError,error:t,message:`Reconnect error - ${e+1} attempts made yet.`}),this.makeAutoReconnectRetry(e,t)}}makeAutoReconnectRetry(e,t){let s=this.nextRetryTimeout(e+1,t);this.retryTimeout=setTimeout(()=>{this.onConnectionError(e+1)},s)}async *[Symbol.asyncIterator](){let e=new O(this);try{for(;;){if(!this.req||this.req.aborted)throw Error("Connection closed");e.hasStack()&&(yield*e.popStack());let{type:t,payload:s}=await e.nextEvent();if("error"===t)throw s}}finally{e.destroy()}}}let $=W;async function V(e,t,s,r){let i;return(r instanceof _||r instanceof f?i=await this.applyPluginMethod("onRequestError",{client:this,url:this.getUrlObjectFromUrlString(e.url),params:e,computedParams:t,requestOptions:s,error:r}):r instanceof w&&(i=await this.applyPluginMethod("onResponseError",{client:this,url:this.getUrlObjectFromUrlString(e.url),params:e,computedParams:t,requestOptions:s,error:r})),i&&i instanceof y)?i.value:Promise.reject(r)}var z=s(55511);class K{constructor(e){this.nonceLength=32,this.consumerKeys=e.consumerKeys}static percentEncode(e){return encodeURIComponent(e).replace(/!/g,"%21").replace(/\*/g,"%2A").replace(/'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29")}hash(e,t){return z.createHmac("sha1",t).update(e).digest("base64")}authorize(e,t={}){let s={oauth_consumer_key:this.consumerKeys.key,oauth_nonce:this.getNonce(),oauth_signature_method:"HMAC-SHA1",oauth_timestamp:this.getTimestamp(),oauth_version:"1.0"};return void 0!==t.key&&(s.oauth_token=t.key),e.data||(e.data={}),s.oauth_signature=this.getSignature(e,t.secret,s),s}toHeader(e){let t=Y(e),s="OAuth ";for(let e of t)0===e.key.indexOf("oauth_")&&(s+=K.percentEncode(e.key)+'="'+K.percentEncode(e.value)+'",');return{Authorization:s.slice(0,s.length-1)}}getNonce(){let e="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="";for(let s=0;s<this.nonceLength;s++)t+=e[Math.trunc(Math.random()*e.length)];return t}getTimestamp(){return Math.trunc(new Date().getTime()/1e3)}getSignature(e,t,s){return this.hash(this.getBaseString(e,s),this.getSigningKey(t))}getSigningKey(e){return K.percentEncode(this.consumerKeys.secret)+"&"+K.percentEncode(e||"")}getBaseString(e,t){return e.method.toUpperCase()+"&"+K.percentEncode(this.getBaseUrl(e.url))+"&"+K.percentEncode(this.getParameterString(e,t))}getParameterString(e,t){let s=Y(function(e){let t={};for(let s in e){let r=e[s];r=r&&Array.isArray(r)?r.map(e=>K.percentEncode(e)):K.percentEncode(r),t[K.percentEncode(s)]=r}return t}(Q(t,Q(e.data,function(e){let t=e.split("?");return 1===t.length?{}:function(e){let t=e.split("&"),s={};for(let e of t){let[t,r=""]=e.split("=");s[t]?(Array.isArray(s[t])||(s[t]=[s[t]]),s[t].push(decodeURIComponent(r))):s[t]=decodeURIComponent(r)}return s}(t[1])}(e.url))))),r="";for(let{key:e,value:t}of s)if(t&&Array.isArray(t)){t.sort();let s="";t.forEach((r,i)=>{s+=e+"="+r,i<t.length&&(s+="&")}),r+=s}else r+=e+"="+t+"&";return r.slice(0,r.length-1)}getBaseUrl(e){return e.split("?")[0]}}function Q(e,t){return{...e||{},...t||{}}}function Y(e){return Object.keys(e).sort().map(t=>({key:t,value:e[t]}))}class J{constructor(){this._boundary="",this._chunks=[]}bodyAppend(...e){let t=e.map(e=>e instanceof Buffer?e:Buffer.from(e));this._chunks.push(...t)}append(e,t,s){let r=t instanceof Buffer?t:t.toString(),i=this.getMultipartHeader(e,r,s);this.bodyAppend(i,r,J.LINE_BREAK)}getHeaders(){return{"content-type":"multipart/form-data; boundary="+this.getBoundary()}}getLength(){return this._chunks.reduce((e,t)=>e+t.length,this.getMultipartFooter().length)}getBuffer(){let e=[...this._chunks,this.getMultipartFooter()],t=Buffer.alloc(this.getLength()),s=0;for(let r of e)for(let e=0;e<r.length;s++,e++)t[s]=r[e];return t}getBoundary(){return this._boundary||this.generateBoundary(),this._boundary}generateBoundary(){let e="--------------------------";for(let t=0;t<24;t++)e+=Math.floor(10*Math.random()).toString(16);this._boundary=e}getMultipartHeader(e,t,s){s||(s=t instanceof Buffer?J.DEFAULT_CONTENT_TYPE:"");let r="";for(let[t,i]of Object.entries({"Content-Disposition":["form-data",`name="${e}"`],"Content-Type":s}))i.length&&(r+=t+": "+P(i).join("; ")+J.LINE_BREAK);return"--"+this.getBoundary()+J.LINE_BREAK+r+J.LINE_BREAK}getMultipartFooter(){return this._footerChunk?this._footerChunk:this._footerChunk=Buffer.from("--"+this.getBoundary()+"--"+J.LINE_BREAK)}}J.LINE_BREAK="\r\n",J.DEFAULT_CONTENT_TYPE="application/octet-stream";class G{static formatQueryToString(e){let t={};for(let s in e)"string"==typeof e[s]?t[s]=e[s]:void 0!==e[s]&&(t[s]=String(e[s]));return t}static autoDetectBodyType(e){if(e.pathname.startsWith("/2/")||e.pathname.startsWith("/labs/2/"))return e.password.startsWith("/2/oauth2")?"url":"json";if("upload.x.com"===e.hostname)return"/1.1/media/upload.json"===e.pathname?"form-data":"json";let t=e.pathname.split("/1.1/",2)[1];return this.JSON_1_1_ENDPOINTS.has(t)?"json":"url"}static addQueryParamsToUrl(e,t){let s=Object.entries(t);if(s.length){let t="";for(let[e,r]of s)t+=(t.length?"&":"?")+`${K.percentEncode(e)}=${K.percentEncode(r)}`;e.search=t}}static constructBodyParams(e,t,s){if(e instanceof Buffer)return e;if("json"===s)return t["content-type"]||(t["content-type"]="application/json;charset=UTF-8"),JSON.stringify(e);if("url"===s)return(t["content-type"]||(t["content-type"]="application/x-www-form-urlencoded;charset=UTF-8"),Object.keys(e).length)?new URLSearchParams(e).toString().replace(/\*/g,"%2A"):"";if("raw"===s)throw Error("You can only use raw body mode with Buffers. To give a string, use Buffer.from(str).");{let s=new J;for(let t in e)s.append(t,e[t]);if(!t["content-type"]){let e=s.getHeaders();t["content-type"]=e["content-type"]}return s.getBuffer()}}static setBodyLengthHeader(e,t){var s;e.headers=null!=(s=e.headers)?s:{},"string"==typeof t?e.headers["content-length"]=Buffer.byteLength(t):e.headers["content-length"]=t.length}static isOAuthSerializable(e){return!(e instanceof Buffer)}static mergeQueryAndBodyForOAuth(e,t){let s={};for(let t in e)s[t]=e[t];if(this.isOAuthSerializable(t))for(let e in t){let r=t[e];this.isOAuthSerializable(r)&&(s[e]="object"==typeof r&&null!==r&&"toString"in r?r.toString():r)}return s}static moveUrlQueryParamsIntoObject(e,t){for(let[s,r]of e.searchParams)t[s]=r;return e.search="",e}static applyRequestParametersToUrl(e,t){return e.pathname=e.pathname.replace(/:([A-Z_-]+)/ig,(e,s)=>void 0!==t[s]?String(t[s]):e),e}}G.JSON_1_1_ENDPOINTS=new Set(["direct_messages/events/new.json","direct_messages/welcome_messages/new.json","direct_messages/welcome_messages/rules/new.json","media/metadata/create.json","collections/entries/curate.json"]);class Z{static getCodeVerifier(){return this.generateRandomString(128)}static getCodeChallengeFromVerifier(e){return this.escapeBase64Url(z.createHash("sha256").update(e).digest("base64"))}static getAuthHeader(e,t){let s=encodeURIComponent(e)+":"+encodeURIComponent(t);return Buffer.from(s).toString("base64")}static generateRandomString(e){let t="",s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~";for(let r=0;r<e;r++)t+=s[Math.floor(Math.random()*s.length)];return t}static escapeBase64Url(e){return e.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}}class X{constructor(e){this.rateLimits={},this.clientSettings={},e&&(this.clientSettings=e)}getRateLimits(){return this.rateLimits}saveRateLimit(e,t){this.rateLimits[e]=t}async send(e){var t,s,r,i,a;if(null==(t=this.clientSettings.plugins)?void 0:t.length){let t=await this.applyPreRequestConfigHooks(e);if(t)return t}let n=this.getHttpRequestArgs(e),o={method:n.method,headers:n.headers,timeout:e.timeout,agent:this.clientSettings.httpAgent},u=!1!==e.enableRateLimitSave;n.body&&G.setBodyLengthHeader(o,n.body),(null==(s=this.clientSettings.plugins)?void 0:s.length)&&await this.applyPreRequestHooks(e,n,o);let l=new U({url:n.url,options:o,body:n.body,rateLimitSaver:u?this.saveRateLimit.bind(this,n.rawUrl):void 0,requestEventDebugHandler:e.requestEventDebugHandler,compression:null==(i=null!=(r=e.compression)?r:this.clientSettings.compression)||i,forceParseMode:e.forceParseMode}).makeRequest();(function(e){var t;if(!(null==(t=e.clientSettings.plugins)?void 0:t.length))return!1;for(let t of e.clientSettings.plugins)if(t.onRequestError||t.onResponseError)return!0;return!1})(this)&&(l=this.applyResponseErrorHooks(e,n,o,l));let d=await l;if(null==(a=this.clientSettings.plugins)?void 0:a.length){let t=await this.applyPostRequestHooks(e,n,o,d);if(t)return t.value}return d}sendStream(e){var t,s;this.clientSettings.plugins&&this.applyPreStreamRequestConfigHooks(e);let r=this.getHttpRequestArgs(e),i={method:r.method,headers:r.headers,agent:this.clientSettings.httpAgent},a=!1!==e.enableRateLimitSave,n=!1!==e.autoConnect;r.body&&G.setBodyLengthHeader(i,r.body);let o=new $({url:r.url,options:i,body:r.body,rateLimitSaver:a?this.saveRateLimit.bind(this,r.rawUrl):void 0,payloadIsError:e.payloadIsError,compression:null==(s=null!=(t=e.compression)?t:this.clientSettings.compression)||s});return n?o.connect():o}initializeToken(e){if("string"==typeof e)this.bearerToken=e;else if("object"==typeof e&&"appKey"in e)this.consumerToken=e.appKey,this.consumerSecret=e.appSecret,e.accessToken&&e.accessSecret&&(this.accessToken=e.accessToken,this.accessSecret=e.accessSecret),this._oauth=this.buildOAuth();else if("object"==typeof e&&"username"in e){let t=encodeURIComponent(e.username)+":"+encodeURIComponent(e.password);this.basicToken=Buffer.from(t).toString("base64")}else"object"==typeof e&&"clientId"in e&&(this.clientId=e.clientId,this.clientSecret=e.clientSecret)}getActiveTokens(){return this.bearerToken?{type:"oauth2",bearerToken:this.bearerToken}:this.basicToken?{type:"basic",token:this.basicToken}:this.consumerSecret&&this._oauth?{type:"oauth-1.0a",appKey:this.consumerToken,appSecret:this.consumerSecret,accessToken:this.accessToken,accessSecret:this.accessSecret}:this.clientId?{type:"oauth2-user",clientId:this.clientId}:{type:"none"}}buildOAuth(){if(!this.consumerSecret||!this.consumerToken)throw Error("Invalid consumer tokens");return new K({consumerKeys:{key:this.consumerToken,secret:this.consumerSecret}})}getOAuthAccessTokens(){if(this.accessSecret&&this.accessToken)return{key:this.accessToken,secret:this.accessSecret}}getPlugins(){var e;return null!=(e=this.clientSettings.plugins)?e:[]}hasPlugins(){var e;return!!(null==(e=this.clientSettings.plugins)?void 0:e.length)}async applyPluginMethod(e,t){var s;let r;for(let i of this.getPlugins()){let a=await (null==(s=i[e])?void 0:s.call(i,t));a&&a instanceof y&&(r=a)}return r}writeAuthHeaders({headers:e,bodyInSignature:t,url:s,method:r,query:i,body:a}){if(e={...e},this.bearerToken)e.Authorization="Bearer "+this.bearerToken;else if(this.basicToken)e.Authorization="Basic "+this.basicToken;else if(this.clientId&&this.clientSecret)e.Authorization="Basic "+Z.getAuthHeader(this.clientId,this.clientSecret);else if(this.consumerSecret&&this._oauth){let n=t?G.mergeQueryAndBodyForOAuth(i,a):i,o=this._oauth.authorize({url:s.toString(),method:r,data:n},this.getOAuthAccessTokens());e={...e,...this._oauth.toHeader(o)}}return e}getUrlObjectFromUrlString(e){return e.startsWith("http")||(e="https://"+e),new URL(e)}getHttpRequestArgs({url:e,method:t,query:s={},body:r={},headers:i,forceBodyMode:a,enableAuth:n,params:o}){let u;t=t.toUpperCase(),(i=null!=i?i:{})["x-user-agent"]||(i["x-user-agent"]="Node.twitter-api-v2");let l=this.getUrlObjectFromUrlString(e),d=l.origin+l.pathname;o&&G.applyRequestParametersToUrl(l,o);let c=G.formatQueryToString(s);G.moveUrlQueryParamsIntoObject(l,c),r instanceof Buffer||function(e){for(let t in e)void 0===e[t]&&delete e[t]}(r);let h=null!=a?a:G.autoDetectBodyType(l);if(!1!==n){let e=X.BODY_METHODS.has(t)&&"url"===h;i=this.writeAuthHeaders({headers:i,bodyInSignature:e,method:t,query:c,url:l,body:r})}return X.BODY_METHODS.has(t)&&(u=G.constructBodyParams(r,i,h)||void 0),G.addQueryParamsToUrl(l,c),{rawUrl:d,url:l,method:t,headers:i,body:u}}async applyPreRequestConfigHooks(e){var t;let s=this.getUrlObjectFromUrlString(e.url);for(let r of this.getPlugins()){let i=await (null==(t=r.onBeforeRequestConfig)?void 0:t.call(r,{client:this,url:s,params:e}));if(i)return i}}applyPreStreamRequestConfigHooks(e){var t;let s=this.getUrlObjectFromUrlString(e.url);for(let r of this.getPlugins())null==(t=r.onBeforeStreamRequestConfig)||t.call(r,{client:this,url:s,params:e})}async applyPreRequestHooks(e,t,s){await this.applyPluginMethod("onBeforeRequest",{client:this,url:this.getUrlObjectFromUrlString(e.url),params:e,computedParams:t,requestOptions:s})}async applyPostRequestHooks(e,t,s,r){return await this.applyPluginMethod("onAfterRequest",{client:this,url:this.getUrlObjectFromUrlString(e.url),params:e,computedParams:t,requestOptions:s,response:r})}applyResponseErrorHooks(e,t,s,r){return r.catch(V.bind(this,e,t,s))}}X.BODY_METHODS=new Set(["POST","PUT","PATCH"]);class ee{constructor(e,t={}){this._currentUser=null,this._currentUserV2=null,e instanceof ee?this._requestMaker=e._requestMaker:(this._requestMaker=new X(t),this._requestMaker.initializeToken(e))}setPrefix(e){this._prefix=e}cloneWithPrefix(e){let t=this.constructor(this);return t.setPrefix(e),t}getActiveTokens(){return this._requestMaker.getActiveTokens()}getPlugins(){return this._requestMaker.getPlugins()}getPluginOfType(e){return this.getPlugins().find(t=>t instanceof e)}hasHitRateLimit(e){var t;return!this.isRateLimitStatusObsolete(e)&&(null==(t=this.getLastRateLimitStatus(e))?void 0:t.remaining)===0}isRateLimitStatusObsolete(e){let t=this.getLastRateLimitStatus(e);return void 0===t||1e3*t.reset<Date.now()}getLastRateLimitStatus(e){let t=e.match(/^https?:\/\//)?e:this._prefix+e;return this._requestMaker.getRateLimits()[t]}getCurrentUserObject(e=!1){return!e&&this._currentUser?this._currentUser.value?Promise.resolve(this._currentUser.value):this._currentUser.promise:(this._currentUser=S(()=>this.get("account/verify_credentials.json",{tweet_mode:"extended"},{prefix:a})),this._currentUser.promise)}getCurrentUserV2Object(e=!1){return!e&&this._currentUserV2?this._currentUserV2.value?Promise.resolve(this._currentUserV2.value):this._currentUserV2.promise:(this._currentUserV2=S(()=>this.get("users/me",void 0,{prefix:r})),this._currentUserV2.promise)}async get(e,t={},{fullResponse:s,prefix:r=this._prefix,...i}={}){r&&(e=r+e);let a=await this._requestMaker.send({url:e,method:"GET",query:t,...i});return s?a:a.data}async delete(e,t={},{fullResponse:s,prefix:r=this._prefix,...i}={}){r&&(e=r+e);let a=await this._requestMaker.send({url:e,method:"DELETE",query:t,...i});return s?a:a.data}async post(e,t,{fullResponse:s,prefix:r=this._prefix,...i}={}){r&&(e=r+e);let a=await this._requestMaker.send({url:e,method:"POST",body:t,...i});return s?a:a.data}async put(e,t,{fullResponse:s,prefix:r=this._prefix,...i}={}){r&&(e=r+e);let a=await this._requestMaker.send({url:e,method:"PUT",body:t,...i});return s?a:a.data}async patch(e,t,{fullResponse:s,prefix:r=this._prefix,...i}={}){r&&(e=r+e);let a=await this._requestMaker.send({url:e,method:"PATCH",body:t,...i});return s?a:a.data}getStream(e,t,{prefix:s=this._prefix,...r}={}){return this._requestMaker.sendStream({url:s?s+e:e,method:"GET",query:t,...r})}postStream(e,t,{prefix:s=this._prefix,...r}={}){return this._requestMaker.sendStream({url:s?s+e:e,method:"POST",body:t,...r})}}class et extends ee{constructor(e){if(!(e instanceof ee))throw Error("You must instance SubTwitterApi instance from existing TwitterApi instance.");super(e)}}class es extends c{constructor(){super(...arguments),this.hasFinishedFetch=!1}refreshInstanceFromResult(e,t){let s=e.data;this._rateLimit=e.rateLimit,t&&(this._realData.push(...s),this.hasFinishedFetch=0===s.length)}getNextQueryParams(e){let t=BigInt(this._realData[this._realData.length-1].id_str);return{...this.injectQueryParams(e),max_id:(t-BigInt(1)).toString()}}getPageLengthFromRequest(e){return e.data.length}isFetchLastOver(e){return!e.data.length}canFetchNextPage(e){return e.length>0}getItemArray(){return this.tweets}get tweets(){return this._realData}get done(){return super.done||this.hasFinishedFetch}}class er extends es{constructor(){super(...arguments),this._endpoint="statuses/home_timeline.json"}}class ei extends es{constructor(){super(...arguments),this._endpoint="statuses/mentions_timeline.json"}}class ea extends es{constructor(){super(...arguments),this._endpoint="statuses/user_timeline.json"}}class en extends es{constructor(){super(...arguments),this._endpoint="lists/statuses.json"}}class eo extends es{constructor(){super(...arguments),this._endpoint="favorites/list.json"}}class eu extends h{constructor(){super(...arguments),this._endpoint="mutes/users/list.json"}refreshInstanceFromResult(e,t){let s=e.data;this._rateLimit=e.rateLimit,t&&(this._realData.users.push(...s.users),this._realData.next_cursor=s.next_cursor)}getPageLengthFromRequest(e){return e.data.users.length}getItemArray(){return this.users}get users(){return this._realData.users}}class el extends h{constructor(){super(...arguments),this._endpoint="mutes/users/ids.json",this._maxResultsWhenFetchLast=5e3}refreshInstanceFromResult(e,t){let s=e.data;this._rateLimit=e.rateLimit,t&&(this._realData.ids.push(...s.ids),this._realData.next_cursor=s.next_cursor)}getPageLengthFromRequest(e){return e.data.ids.length}getItemArray(){return this.ids}get ids(){return this._realData.ids}}class ed extends h{constructor(){super(...arguments),this._endpoint="followers/list.json"}refreshInstanceFromResult(e,t){let s=e.data;this._rateLimit=e.rateLimit,t&&(this._realData.users.push(...s.users),this._realData.next_cursor=s.next_cursor)}getPageLengthFromRequest(e){return e.data.users.length}getItemArray(){return this.users}get users(){return this._realData.users}}class ec extends h{constructor(){super(...arguments),this._endpoint="followers/ids.json",this._maxResultsWhenFetchLast=5e3}refreshInstanceFromResult(e,t){let s=e.data;this._rateLimit=e.rateLimit,t&&(this._realData.ids.push(...s.ids),this._realData.next_cursor=s.next_cursor)}getPageLengthFromRequest(e){return e.data.ids.length}getItemArray(){return this.ids}get ids(){return this._realData.ids}}class eh extends h{constructor(){super(...arguments),this._endpoint="friends/list.json"}refreshInstanceFromResult(e,t){let s=e.data;this._rateLimit=e.rateLimit,t&&(this._realData.users.push(...s.users),this._realData.next_cursor=s.next_cursor)}getPageLengthFromRequest(e){return e.data.users.length}getItemArray(){return this.users}get users(){return this._realData.users}}class em extends h{constructor(){super(...arguments),this._endpoint="friends/ids.json",this._maxResultsWhenFetchLast=5e3}refreshInstanceFromResult(e,t){let s=e.data;this._rateLimit=e.rateLimit,t&&(this._realData.ids.push(...s.ids),this._realData.next_cursor=s.next_cursor)}getPageLengthFromRequest(e){return e.data.ids.length}getItemArray(){return this.ids}get ids(){return this._realData.ids}}class ep extends c{constructor(){super(...arguments),this._endpoint="users/search.json"}refreshInstanceFromResult(e,t){let s=e.data;this._rateLimit=e.rateLimit,t&&this._realData.push(...s)}getNextQueryParams(e){var t;let s=Number(null!=(t=this._queryParams.page)?t:"1");return{...this._queryParams,page:s+1,...e?{count:e}:{}}}getPageLengthFromRequest(e){return e.data.length}isFetchLastOver(e){return!e.data.length}canFetchNextPage(e){return e.length>0}getItemArray(){return this.users}get users(){return this._realData}}class eg extends h{constructor(){super(...arguments),this._endpoint="friendships/incoming.json",this._maxResultsWhenFetchLast=5e3}refreshInstanceFromResult(e,t){let s=e.data;this._rateLimit=e.rateLimit,t&&(this._realData.ids.push(...s.ids),this._realData.next_cursor=s.next_cursor)}getPageLengthFromRequest(e){return e.data.ids.length}getItemArray(){return this.ids}get ids(){return this._realData.ids}}class e_ extends eg{constructor(){super(...arguments),this._endpoint="friendships/outgoing.json"}}class ef extends h{refreshInstanceFromResult(e,t){let s=e.data;this._rateLimit=e.rateLimit,t&&(this._realData.lists.push(...s.lists),this._realData.next_cursor=s.next_cursor)}getPageLengthFromRequest(e){return e.data.lists.length}getItemArray(){return this.lists}get lists(){return this._realData.lists}}class ew extends ef{constructor(){super(...arguments),this._endpoint="lists/memberships.json"}}class ey extends ef{constructor(){super(...arguments),this._endpoint="lists/ownerships.json"}}class ev extends ef{constructor(){super(...arguments),this._endpoint="lists/subscriptions.json"}}class eb extends h{refreshInstanceFromResult(e,t){let s=e.data;this._rateLimit=e.rateLimit,t&&(this._realData.users.push(...s.users),this._realData.next_cursor=s.next_cursor)}getPageLengthFromRequest(e){return e.data.users.length}getItemArray(){return this.users}get users(){return this._realData.users}}class eR extends eb{constructor(){super(...arguments),this._endpoint="lists/members.json"}}class ex extends eb{constructor(){super(...arguments),this._endpoint="lists/subscribers.json"}}class eD extends et{constructor(){super(...arguments),this._prefix=a}singleTweet(e,t={}){return this.get("statuses/show.json",{tweet_mode:"extended",id:e,...t})}tweets(e,t={}){return this.post("statuses/lookup.json",{tweet_mode:"extended",id:e,...t})}oembedTweet(e,t={}){return this.get("oembed",{url:`https://x.com/i/statuses/${e}`,...t},{prefix:"https://publish.x.com/"})}async homeTimeline(e={}){let t={tweet_mode:"extended",...e},s=await this.get("statuses/home_timeline.json",t,{fullResponse:!0});return new er({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t})}async mentionTimeline(e={}){let t={tweet_mode:"extended",...e},s=await this.get("statuses/mentions_timeline.json",t,{fullResponse:!0});return new ei({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t})}async userTimeline(e,t={}){let s={tweet_mode:"extended",user_id:e,...t},r=await this.get("statuses/user_timeline.json",s,{fullResponse:!0});return new ea({realData:r.data,rateLimit:r.rateLimit,instance:this,queryParams:s})}async userTimelineByUsername(e,t={}){let s={tweet_mode:"extended",screen_name:e,...t},r=await this.get("statuses/user_timeline.json",s,{fullResponse:!0});return new ea({realData:r.data,rateLimit:r.rateLimit,instance:this,queryParams:s})}async favoriteTimeline(e,t={}){let s={tweet_mode:"extended",user_id:e,...t},r=await this.get("favorites/list.json",s,{fullResponse:!0});return new eo({realData:r.data,rateLimit:r.rateLimit,instance:this,queryParams:s})}async favoriteTimelineByUsername(e,t={}){let s={tweet_mode:"extended",screen_name:e,...t},r=await this.get("favorites/list.json",s,{fullResponse:!0});return new eo({realData:r.data,rateLimit:r.rateLimit,instance:this,queryParams:s})}user(e){return this.get("users/show.json",{tweet_mode:"extended",...e})}users(e){return this.get("users/lookup.json",{tweet_mode:"extended",...e})}verifyCredentials(e={}){return this.get("account/verify_credentials.json",e)}async listMutedUsers(e={}){let t={tweet_mode:"extended",...e},s=await this.get("mutes/users/list.json",t,{fullResponse:!0});return new eu({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t})}async listMutedUserIds(e={}){let t={stringify_ids:!0,...e},s=await this.get("mutes/users/ids.json",t,{fullResponse:!0});return new el({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t})}async userFriendList(e={}){let t={...e},s=await this.get("friends/list.json",t,{fullResponse:!0});return new eh({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t})}async userFollowerList(e={}){let t={...e},s=await this.get("followers/list.json",t,{fullResponse:!0});return new ed({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t})}async userFollowerIds(e={}){let t={stringify_ids:!0,...e},s=await this.get("followers/ids.json",t,{fullResponse:!0});return new ec({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t})}async userFollowingIds(e={}){let t={stringify_ids:!0,...e},s=await this.get("friends/ids.json",t,{fullResponse:!0});return new em({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t})}async searchUsers(e,t={}){let s={q:e,tweet_mode:"extended",page:1,...t},r=await this.get("users/search.json",s,{fullResponse:!0});return new ep({realData:r.data,rateLimit:r.rateLimit,instance:this,queryParams:s})}friendship(e){return this.get("friendships/show.json",e)}friendships(e){return this.get("friendships/lookup.json",e)}friendshipsNoRetweets(){return this.get("friendships/no_retweets/ids.json",{stringify_ids:!0})}async friendshipsIncoming(e={}){let t={stringify_ids:!0,...e},s=await this.get("friendships/incoming.json",t,{fullResponse:!0});return new eg({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t})}async friendshipsOutgoing(e={}){let t={stringify_ids:!0,...e},s=await this.get("friendships/outgoing.json",t,{fullResponse:!0});return new e_({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t})}accountSettings(){return this.get("account/settings.json")}userProfileBannerSizes(e){return this.get("users/profile_banner.json",e)}list(e){return this.get("lists/show.json",{tweet_mode:"extended",...e})}lists(e={}){return this.get("lists/list.json",{tweet_mode:"extended",...e})}async listMembers(e={}){let t={tweet_mode:"extended",...e},s=await this.get("lists/members.json",t,{fullResponse:!0});return new eR({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t})}listGetMember(e){return this.get("lists/members/show.json",{tweet_mode:"extended",...e})}async listMemberships(e={}){let t={tweet_mode:"extended",...e},s=await this.get("lists/memberships.json",t,{fullResponse:!0});return new ew({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t})}async listOwnerships(e={}){let t={tweet_mode:"extended",...e},s=await this.get("lists/ownerships.json",t,{fullResponse:!0});return new ey({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t})}async listStatuses(e){let t={tweet_mode:"extended",...e},s=await this.get("lists/statuses.json",t,{fullResponse:!0});return new en({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t})}async listSubscribers(e={}){let t={tweet_mode:"extended",...e},s=await this.get("lists/subscribers.json",t,{fullResponse:!0});return new ex({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t})}listGetSubscriber(e){return this.get("lists/subscribers/show.json",{tweet_mode:"extended",...e})}async listSubscriptions(e={}){let t={tweet_mode:"extended",...e},s=await this.get("lists/subscriptions.json",t,{fullResponse:!0});return new ev({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t})}mediaInfo(e){return this.get("media/upload.json",{command:"STATUS",media_id:e},{prefix:n})}filterStream({autoConnect:e,...t}={}){let s={};for(let[e,r]of Object.entries(t))"follow"===e||"track"===e?s[e]=r.toString():"locations"===e?s.locations=P(r).map(e=>`${e.lng},${e.lat}`).join(","):s[e]=r;return this.stream.postStream("statuses/filter.json",s,{autoConnect:e})}sampleStream({autoConnect:e,...t}={}){return this.stream.getStream("statuses/sample.json",t,{autoConnect:e})}get stream(){let e=new eI(this);return e.setPrefix("https://stream.x.com/1.1/"),e}trendsByPlace(e,t={}){return this.get("trends/place.json",{id:e,...t})}trendsAvailable(){return this.get("trends/available.json")}trendsClosest(e,t){return this.get("trends/closest.json",{lat:e,long:t})}geoPlace(e){return this.get("geo/id/:place_id.json",void 0,{params:{place_id:e}})}geoSearch(e){return this.get("geo/search.json",e)}geoReverseGeoCode(e){return this.get("geo/reverse_geocode.json",e)}rateLimitStatuses(...e){return this.get("application/rate_limit_status.json",{resources:e})}supportedLanguages(){return this.get("help/languages.json")}}async function eL(e){let t=await ek(e);return"number"==typeof t?new Promise((e,s)=>{q.readFile(t,(t,r)=>{if(t)return s(t);e(r)})}):t instanceof Buffer?t:t.readFile()}function ek(e){if("string"==typeof e)return q.promises.open(e,"r");if("number"==typeof e)return e;if("object"==typeof e&&!(e instanceof Buffer))return e;if(e instanceof Buffer)return e;throw Error("Given file is not valid, please check its type.")}async function eq(e){return"number"==typeof e?(await new Promise((t,s)=>{q.fstat(e,(e,r)=>{e&&s(e),t(r)})})).size:e instanceof Buffer?e.length:(await e.stat()).size}function eT(e){return new Promise(t=>setTimeout(t,1e3*e))}async function eS(e,t,s=0,r){let i;if(e instanceof Buffer){let r=e.slice(s,s+t);return[r,r.length]}if(!r)throw Error("Well, we will need a buffer to store file content.");return i="number"==typeof e?await new Promise((i,a)=>{q.read(e,r,0,t,s,(e,t)=>{e&&a(e),i(t)})}):(await e.read(r,0,t,s)).bytesRead,[r,i]}let eP="media/upload.json";class eE extends eD{constructor(){super(...arguments),this._prefix=a}get readOnly(){return this}tweet(e,t={}){let s={status:e,tweet_mode:"extended",...t};return this.post("statuses/update.json",s)}async quote(e,t,s={}){return this.tweet(e,{...s,attachment_url:"https://x.com/i/statuses/"+t})}async tweetThread(e){let t=[];for(let s of e){let e=t.length?t[t.length-1]:null,r={..."string"==typeof s?{status:s}:s},i=e?e.id_str:r.in_reply_to_status_id,a=r.status;i?t.push(await this.reply(a,i,r)):t.push(await this.tweet(a,r))}return t}reply(e,t,s={}){return this.tweet(e,{auto_populate_reply_metadata:!0,in_reply_to_status_id:t,...s})}deleteTweet(e){return this.post("statuses/destroy/:id.json",{tweet_mode:"extended"},{params:{id:e}})}reportUserAsSpam(e){return this.post("users/report_spam.json",{tweet_mode:"extended",...e})}updateFriendship(e){return this.post("friendships/update.json",e)}createFriendship(e){return this.post("friendships/create.json",e)}destroyFriendship(e){return this.post("friendships/destroy.json",e)}updateAccountSettings(e){return this.post("account/settings.json",e)}updateAccountProfile(e){return this.post("account/update_profile.json",e)}async updateAccountProfileBanner(e,t={}){let s={banner:await eL(e),...t};return this.post("account/update_profile_banner.json",s,{forceBodyMode:"form-data"})}async updateAccountProfileImage(e,t={}){let s={tweet_mode:"extended",image:await eL(e),...t};return this.post("account/update_profile_image.json",s,{forceBodyMode:"form-data"})}removeAccountProfileBanner(){return this.post("account/remove_profile_banner.json")}createList(e){return this.post("lists/create.json",{tweet_mode:"extended",...e})}updateList(e){return this.post("lists/update.json",{tweet_mode:"extended",...e})}removeList(e){return this.post("lists/destroy.json",{tweet_mode:"extended",...e})}addListMembers(e){let t=e.user_id&&A(e.user_id)||e.screen_name&&A(e.screen_name);return this.post(t?"lists/members/create_all.json":"lists/members/create.json",e)}removeListMembers(e){let t=e.user_id&&A(e.user_id)||e.screen_name&&A(e.screen_name);return this.post(t?"lists/members/destroy_all.json":"lists/members/destroy.json",e)}subscribeToList(e){return this.post("lists/subscribers/create.json",{tweet_mode:"extended",...e})}unsubscribeOfList(e){return this.post("lists/subscribers/destroy.json",{tweet_mode:"extended",...e})}createMediaMetadata(e,t){return this.post("media/metadata/create.json",{media_id:e,...t},{prefix:n,forceBodyMode:"json"})}createMediaSubtitles(e,t){return this.post("media/subtitles/create.json",{media_id:e,media_category:"TweetVideo",subtitle_info:{subtitles:t}},{prefix:n,forceBodyMode:"json"})}deleteMediaSubtitles(e,...t){return this.post("media/subtitles/delete.json",{media_id:e,media_category:"TweetVideo",subtitle_info:{subtitles:t.map(e=>({language_code:e}))}},{prefix:n,forceBodyMode:"json"})}async uploadMedia(e,t={},s=!1){var r;let i=null!=(r=t.chunkLength)?r:1048576,{fileHandle:a,mediaCategory:o,fileSize:u,mimeType:l}=await this.getUploadMediaRequirements(e,t);try{let e=await this.post(eP,{command:"INIT",total_bytes:u,media_type:l,media_category:o,additional_owners:t.additionalOwners,shared:!!t.shared||void 0},{prefix:n});await this.mediaChunkedUpload(a,i,e.media_id_string,t.maxConcurrentUploads);let r=await this.post(eP,{command:"FINALIZE",media_id:e.media_id_string},{prefix:n});if(r.processing_info&&"succeeded"!==r.processing_info.state&&await this.awaitForMediaProcessingCompletion(r),s)return r;return r.media_id_string}finally{"number"==typeof e?q.close(e,()=>{}):"object"!=typeof a||a instanceof Buffer||a.close()}}async awaitForMediaProcessingCompletion(e){for(var t;;){let{processing_info:s}=e=await this.mediaInfo(e.media_id_string);if(!s||"succeeded"===s.state)return;if(null==(t=s.error)?void 0:t.code){let{name:e,message:t}=s.error;throw Error(`Failed to process media: ${e} - ${t}.`)}if("failed"===s.state)throw Error("Failed to process the media.");s.check_after_secs?await eT(s.check_after_secs):await eT(5)}}async getUploadMediaRequirements(e,{mimeType:t,type:s,target:r,longVideo:i}={}){let a;try{let o;a=await ek(e);let u=function(e,t,s){var r,i;if("string"==typeof s)return s;if("string"==typeof e&&!t){return(r=e).endsWith(".jpeg")||r.endsWith(".jpg")?v.Jpeg:r.endsWith(".png")?v.Png:r.endsWith(".webp")?v.Webp:r.endsWith(".gif")?v.Gif:r.endsWith(".mpeg4")||r.endsWith(".mp4")?v.Mp4:r.endsWith(".mov")||r.endsWith(".mov")?v.Mov:r.endsWith(".srt")?v.Srt:(j({instance:"TwitterApiv1ReadWrite",method:"uploadMedia",problem:"options.mimeType is missing and filename couldn't help to resolve MIME type, so it will fallback to image/jpeg",resolution:"If you except to give filenames without extensions, please specify explicitlty the MIME type using options.mimeType"}),v.Jpeg)}if("string"==typeof t){return i=t,(j({instance:"TwitterApiv1ReadWrite",method:"uploadMedia",problem:"you're using options.type",resolution:"Remove options.type argument and migrate to options.mimeType which takes the real MIME type. If you're using type=longmp4, add options.longVideo alongside of mimeType=EUploadMimeType.Mp4"}),"gif"===i)?v.Gif:"jpg"===i?v.Jpeg:"png"===i?v.Png:"webp"===i?v.Webp:"srt"===i?v.Srt:"mp4"===i||"longmp4"===i?v.Mp4:"mov"===i?v.Mov:i}throw Error("You must specify type if file is a file handle or Buffer.")}(e,s,t);if(u!==v.Mp4||(t||s||"dm"===r)&&!i){var n;n=null!=r?r:"tweet",o=u===v.Mp4||u===v.Mov?"tweet"===n?"TweetVideo":"DmVideo":u===v.Gif?"tweet"===n?"TweetGif":"DmGif":u===v.Srt?"Subtitles":"tweet"===n?"TweetImage":"DmImage"}else o="amplify_video";return{fileHandle:a,mediaCategory:o,fileSize:await eq(a),mimeType:u}}catch(t){throw"number"==typeof e?q.close(e,()=>{}):"object"!=typeof a||a instanceof Buffer||a.close(),t}}async mediaChunkedUpload(e,t,s,r=3){let i,a,o=0;if(r<1)throw RangeError("Bad maxConcurrentUploads parameter.");let u=e instanceof Buffer?void 0:Buffer.alloc(t),l=0;[i,a]=await eS(e,t,l,u),l+=a;let d=new Set;for(;a;){let c=i.slice(0,a);if(c.length){let e=this.post(eP,{command:"APPEND",media_id:s,segment_index:o,media:c},{prefix:n});d.add(e),e.then(()=>{d.delete(e)}),o++}d.size>=r&&await Promise.race(d),[i,a]=await eS(e,t,l,u),l+=a}await Promise.all([...d])}}class eA extends eE{constructor(){super(...arguments),this._prefix=a}get readWrite(){return this}sendDm({recipient_id:e,custom_profile_id:t,...s}){let r={event:{type:b.Create,[b.Create]:{target:{recipient_id:e},message_data:s}}};return t&&(r.event[b.Create].custom_profile_id=t),this.post("direct_messages/events/new.json",r,{forceBodyMode:"json"})}getDmEvent(e){return this.get("direct_messages/events/show.json",{id:e})}deleteDm(e){return this.delete("direct_messages/events/destroy.json",{id:e})}async listDmEvents(e={}){let t={...e},s=await this.get("direct_messages/events/list.json",t,{fullResponse:!0});return new m({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t})}newWelcomeDm(e,t){let s={[b.WelcomeCreate]:{name:e,message_data:t}};return this.post("direct_messages/welcome_messages/new.json",s,{forceBodyMode:"json"})}getWelcomeDm(e){return this.get("direct_messages/welcome_messages/show.json",{id:e})}deleteWelcomeDm(e){return this.delete("direct_messages/welcome_messages/destroy.json",{id:e})}updateWelcomeDm(e,t){return this.put("direct_messages/welcome_messages/update.json",{message_data:t},{forceBodyMode:"json",query:{id:e}})}async listWelcomeDms(e={}){let t={...e},s=await this.get("direct_messages/welcome_messages/list.json",t,{fullResponse:!0});return new p({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t})}newWelcomeDmRule(e){return this.post("direct_messages/welcome_messages/rules/new.json",{welcome_message_rule:{welcome_message_id:e}},{forceBodyMode:"json"})}getWelcomeDmRule(e){return this.get("direct_messages/welcome_messages/rules/show.json",{id:e})}deleteWelcomeDmRule(e){return this.delete("direct_messages/welcome_messages/rules/destroy.json",{id:e})}async listWelcomeDmRules(e={}){let t={...e};return this.get("direct_messages/welcome_messages/rules/list.json",t)}async setWelcomeDm(e,t=!0){var s;let r=await this.listWelcomeDmRules();if(null==(s=r.welcome_message_rules)?void 0:s.length)for(let e of r.welcome_message_rules)await this.deleteWelcomeDmRule(e.id),t&&await this.deleteWelcomeDm(e.welcome_message_id);return this.newWelcomeDmRule(e)}markDmAsRead(e,t){return this.post("direct_messages/mark_read.json",{last_read_event_id:e,recipient_id:t},{forceBodyMode:"url"})}indicateDmTyping(e){return this.post("direct_messages/indicate_typing.json",{recipient_id:e},{forceBodyMode:"url"})}async downloadDmImage(e){if("string"!=typeof e){let t=e[b.Create].message_data.attachment;if(!t)throw Error("The given direct message doesn't contain any attachment");e=t.media.media_url_https}let t=await this.get(e,void 0,{forceParseMode:"buffer",prefix:""});if(!t.length)throw Error("Image not found. Make sure you are logged with credentials able to access direct messages, and check the URL.");return t}}let eI=eA;class ej{constructor(e){this.result=e}get tweets(){return ej.tweets(this.result)}static tweets(e){var t,s;return null!=(s=null==(t=e.includes)?void 0:t.tweets)?s:[]}tweetById(e){return ej.tweetById(this.result,e)}static tweetById(e,t){return this.tweets(e).find(e=>e.id===t)}retweet(e){return ej.retweet(this.result,e)}static retweet(e,t){var s;let r=(null!=(s=t.referenced_tweets)?s:[]).filter(e=>"retweeted"===e.type).map(e=>e.id);return this.tweets(e).find(e=>r.includes(e.id))}quote(e){return ej.quote(this.result,e)}static quote(e,t){var s;let r=(null!=(s=t.referenced_tweets)?s:[]).filter(e=>"quoted"===e.type).map(e=>e.id);return this.tweets(e).find(e=>r.includes(e.id))}repliedTo(e){return ej.repliedTo(this.result,e)}static repliedTo(e,t){var s;let r=(null!=(s=t.referenced_tweets)?s:[]).filter(e=>"replied_to"===e.type).map(e=>e.id);return this.tweets(e).find(e=>r.includes(e.id))}author(e){return ej.author(this.result,e)}static author(e,t){let s=t.author_id;return s?this.users(e).find(e=>e.id===s):void 0}repliedToAuthor(e){return ej.repliedToAuthor(this.result,e)}static repliedToAuthor(e,t){let s=t.in_reply_to_user_id;return s?this.users(e).find(e=>e.id===s):void 0}get users(){return ej.users(this.result)}static users(e){var t,s;return null!=(s=null==(t=e.includes)?void 0:t.users)?s:[]}userById(e){return ej.userById(this.result,e)}static userById(e,t){return this.users(e).find(e=>e.id===t)}pinnedTweet(e){return ej.pinnedTweet(this.result,e)}static pinnedTweet(e,t){return t.pinned_tweet_id?this.tweets(e).find(e=>e.id===t.pinned_tweet_id):void 0}get media(){return ej.media(this.result)}static media(e){var t,s;return null!=(s=null==(t=e.includes)?void 0:t.media)?s:[]}medias(e){return ej.medias(this.result,e)}static medias(e,t){var s,r;let i=null!=(r=null==(s=t.attachments)?void 0:s.media_keys)?r:[];return this.media(e).filter(e=>i.includes(e.media_key))}get polls(){return ej.polls(this.result)}static polls(e){var t,s;return null!=(s=null==(t=e.includes)?void 0:t.polls)?s:[]}poll(e){return ej.poll(this.result,e)}static poll(e,t){var s,r;let i=null!=(r=null==(s=t.attachments)?void 0:s.poll_ids)?r:[];if(i.length){let t=i[0];return this.polls(e).find(e=>e.id===t)}}get places(){return ej.places(this.result)}static places(e){var t,s;return null!=(s=null==(t=e.includes)?void 0:t.places)?s:[]}place(e){return ej.place(this.result,e)}static place(e,t){var s;let r=null==(s=t.geo)?void 0:s.place_id;return r?this.places(e).find(e=>e.id===r):void 0}listOwner(e){return ej.listOwner(this.result,e)}static listOwner(e,t){let s=t.owner_id;return s?this.users(e).find(e=>e.id===s):void 0}spaceCreator(e){return ej.spaceCreator(this.result,e)}static spaceCreator(e,t){let s=t.creator_id;return s?this.users(e).find(e=>e.id===s):void 0}spaceHosts(e){return ej.spaceHosts(this.result,e)}static spaceHosts(e,t){var s;let r=null!=(s=t.host_ids)?s:[];return this.users(e).filter(e=>r.includes(e.id))}spaceSpeakers(e){return ej.spaceSpeakers(this.result,e)}static spaceSpeakers(e,t){var s;let r=null!=(s=t.speaker_ids)?s:[];return this.users(e).filter(e=>r.includes(e.id))}spaceInvitedUsers(e){return ej.spaceInvitedUsers(this.result,e)}static spaceInvitedUsers(e,t){var s;let r=null!=(s=t.invited_user_ids)?s:[];return this.users(e).filter(e=>r.includes(e.id))}}class eM extends d{updateIncludes(e){if(e.errors&&(this._realData.errors||(this._realData.errors=[]),this._realData.errors=[...this._realData.errors,...e.errors]),!e.includes)return;this._realData.includes||(this._realData.includes={});let t=this._realData.includes;for(let[s,r]of Object.entries(e.includes))t[s]||(t[s]=[]),t[s]=[...t[s],...r]}assertUsable(){if(this.unusable)throw Error("Unable to use this paginator to fetch more data, as it does not contain any metadata. Check .errors property for more details.")}get meta(){return this._realData.meta}get includes(){var e;return(null==(e=this._realData)?void 0:e.includes)?this._includesInstance?this._includesInstance:this._includesInstance=new ej(this._realData):new ej(this._realData)}get errors(){var e;return null!=(e=this._realData.errors)?e:[]}get unusable(){return this.errors.length>0&&!this._realData.meta&&!this._realData.data}}class eF extends eM{refreshInstanceFromResult(e,t){var s;let r=e.data,i=null!=(s=r.data)?s:[];this._rateLimit=e.rateLimit,this._realData.data||(this._realData.data=[]),t?(this._realData.meta.result_count+=r.meta.result_count,this._realData.meta.next_token=r.meta.next_token,this._realData.data.push(...i)):(this._realData.meta.result_count+=r.meta.result_count,this._realData.meta.previous_token=r.meta.previous_token,this._realData.data.unshift(...i)),this.updateIncludes(r)}getNextQueryParams(e){return this.assertUsable(),{...this.injectQueryParams(e),pagination_token:this._realData.meta.next_token}}getPreviousQueryParams(e){return this.assertUsable(),{...this.injectQueryParams(e),pagination_token:this._realData.meta.previous_token}}getPageLengthFromRequest(e){var t,s;return null!=(s=null==(t=e.data.data)?void 0:t.length)?s:0}isFetchLastOver(e){var t;return!(null==(t=e.data.data)?void 0:t.length)||!this.canFetchNextPage(e.data)}canFetchNextPage(e){var t;return!!(null==(t=e.meta)?void 0:t.next_token)}}class eC extends eM{refreshInstanceFromResult(e,t){var s;let r=e.data,i=null!=(s=r.data)?s:[];this._rateLimit=e.rateLimit,this._realData.data||(this._realData.data=[]),t?(this._realData.meta.oldest_id=r.meta.oldest_id,this._realData.meta.result_count+=r.meta.result_count,this._realData.meta.next_token=r.meta.next_token,this._realData.data.push(...i)):(this._realData.meta.newest_id=r.meta.newest_id,this._realData.meta.result_count+=r.meta.result_count,this._realData.data.unshift(...i)),this.updateIncludes(r)}getNextQueryParams(e){this.assertUsable();let t={...this.injectQueryParams(e)};return this._realData.meta.next_token?t.next_token=this._realData.meta.next_token:(t.start_time&&(t.since_id=this.dateStringToSnowflakeId(t.start_time),delete t.start_time),t.end_time&&delete t.end_time,t.until_id=this._realData.meta.oldest_id),t}getPreviousQueryParams(e){return this.assertUsable(),{...this.injectQueryParams(e),since_id:this._realData.meta.newest_id}}getPageLengthFromRequest(e){var t,s;return null!=(s=null==(t=e.data.data)?void 0:t.length)?s:0}isFetchLastOver(e){var t;return!(null==(t=e.data.data)?void 0:t.length)||!this.canFetchNextPage(e.data)}canFetchNextPage(e){return!!e.meta.next_token}getItemArray(){return this.tweets}dateStringToSnowflakeId(e){let t=BigInt("1288834974657"),s=new Date(e);if(isNaN(s.valueOf()))throw Error("Unable to convert start_time/end_time to a valid date. A ISO 8601 DateTime is excepted, please check your input.");return(BigInt(s.valueOf())-t<<BigInt("22")).toString()}get tweets(){var e;return null!=(e=this._realData.data)?e:[]}get meta(){return super.meta}}class eU extends eF{refreshInstanceFromResult(e,t){super.refreshInstanceFromResult(e,t);let s=e.data;t?this._realData.meta.oldest_id=s.meta.oldest_id:this._realData.meta.newest_id=s.meta.newest_id}getItemArray(){return this.tweets}get tweets(){var e;return null!=(e=this._realData.data)?e:[]}get meta(){return super.meta}}class eO extends eC{constructor(){super(...arguments),this._endpoint="tweets/search/recent"}}class eN extends eC{constructor(){super(...arguments),this._endpoint="tweets/search/all"}}class eB extends eU{constructor(){super(...arguments),this._endpoint="tweets/:id/quote_tweets"}}class eH extends eU{constructor(){super(...arguments),this._endpoint="users/:id/timelines/reverse_chronological"}}class eW extends eU{constructor(){super(...arguments),this._endpoint="users/:id/tweets"}}class e$ extends eU{constructor(){super(...arguments),this._endpoint="users/:id/mentions"}}class eV extends eU{constructor(){super(...arguments),this._endpoint="users/:id/bookmarks"}}class ez extends eF{get tweets(){var e;return null!=(e=this._realData.data)?e:[]}get meta(){return super.meta}getItemArray(){return this.tweets}}class eK extends ez{constructor(){super(...arguments),this._endpoint="users/:id/liked_tweets"}}class eQ extends ez{constructor(){super(...arguments),this._endpoint="lists/:id/tweets"}}class eY extends eF{getItemArray(){return this.users}get users(){var e;return null!=(e=this._realData.data)?e:[]}get meta(){return super.meta}}class eJ extends eY{constructor(){super(...arguments),this._endpoint="users/:id/blocking"}}class eG extends eY{constructor(){super(...arguments),this._endpoint="users/:id/muting"}}class eZ extends eY{constructor(){super(...arguments),this._endpoint="users/:id/followers"}}class eX extends eY{constructor(){super(...arguments),this._endpoint="users/:id/following"}}class e0 extends eY{constructor(){super(...arguments),this._endpoint="lists/:id/members"}}class e1 extends eY{constructor(){super(...arguments),this._endpoint="lists/:id/followers"}}class e2 extends eY{constructor(){super(...arguments),this._endpoint="tweets/:id/liking_users"}}class e4 extends eY{constructor(){super(...arguments),this._endpoint="tweets/:id/retweeted_by"}}class e3 extends eF{getItemArray(){return this.lists}get lists(){var e;return null!=(e=this._realData.data)?e:[]}get meta(){return super.meta}}class e5 extends e3{constructor(){super(...arguments),this._endpoint="users/:id/owned_lists"}}class e6 extends e3{constructor(){super(...arguments),this._endpoint="users/:id/list_memberships"}}class e8 extends e3{constructor(){super(...arguments),this._endpoint="users/:id/followed_lists"}}class e9 extends et{constructor(){super(...arguments),this._prefix=i}}class e7 extends eF{getItemArray(){return this.events}get events(){var e;return null!=(e=this._realData.data)?e:[]}get meta(){return super.meta}}class te extends e7{constructor(){super(...arguments),this._endpoint="dm_events"}}class tt extends e7{constructor(){super(...arguments),this._endpoint="dm_conversations/with/:participant_id/dm_events"}}class ts extends e7{constructor(){super(...arguments),this._endpoint="dm_conversations/:dm_conversation_id/dm_events"}}class tr extends et{constructor(){super(...arguments),this._prefix=r}get labs(){return this._labs?this._labs:this._labs=new e9(this)}async search(e,t={}){let s="string"==typeof e?{...t,query:e}:{...e},r=await this.get("tweets/search/recent",s,{fullResponse:!0});return new eO({realData:r.data,rateLimit:r.rateLimit,instance:this,queryParams:s})}async searchAll(e,t={}){let s={...t,query:e},r=await this.get("tweets/search/all",s,{fullResponse:!0});return new eN({realData:r.data,rateLimit:r.rateLimit,instance:this,queryParams:s})}singleTweet(e,t={}){return this.get("tweets/:id",t,{params:{id:e}})}tweets(e,t={}){return this.get("tweets",{ids:e,...t})}tweetCountRecent(e,t={}){return this.get("tweets/counts/recent",{query:e,...t})}tweetCountAll(e,t={}){return this.get("tweets/counts/all",{query:e,...t})}async tweetRetweetedBy(e,t={}){let{asPaginator:s,...r}=t,i=await this.get("tweets/:id/retweeted_by",r,{fullResponse:!0,params:{id:e}});return s?new e4({realData:i.data,rateLimit:i.rateLimit,instance:this,queryParams:r,sharedParams:{id:e}}):i.data}async tweetLikedBy(e,t={}){let{asPaginator:s,...r}=t,i=await this.get("tweets/:id/liking_users",r,{fullResponse:!0,params:{id:e}});return s?new e2({realData:i.data,rateLimit:i.rateLimit,instance:this,queryParams:r,sharedParams:{id:e}}):i.data}async homeTimeline(e={}){let t=await this.getCurrentUserV2Object(),s=await this.get("users/:id/timelines/reverse_chronological",e,{fullResponse:!0,params:{id:t.data.id}});return new eH({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:e,sharedParams:{id:t.data.id}})}async userTimeline(e,t={}){let s=await this.get("users/:id/tweets",t,{fullResponse:!0,params:{id:e}});return new eW({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t,sharedParams:{id:e}})}async userMentionTimeline(e,t={}){let s=await this.get("users/:id/mentions",t,{fullResponse:!0,params:{id:e}});return new e$({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t,sharedParams:{id:e}})}async quotes(e,t={}){let s=await this.get("tweets/:id/quote_tweets",t,{fullResponse:!0,params:{id:e}});return new eB({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:t,sharedParams:{id:e}})}async bookmarks(e={}){let t=await this.getCurrentUserV2Object(),s=await this.get("users/:id/bookmarks",e,{fullResponse:!0,params:{id:t.data.id}});return new eV({realData:s.data,rateLimit:s.rateLimit,instance:this,queryParams:e,sharedParams:{id:t.data.id}})}me(e={}){return this.get("users/me",e)}user(e,t={}){return this.get("users/:id",t,{params:{id:e}})}users(e,t={}){let s=Array.isArray(e)?e.join(","):e;return this.get("users",{...t,ids:s})}userByUsername(e,t={}){return this.get("users/by/username/:username",t,{params:{username:e}})}usersByUsernames(e,t={}){return e=Array.isArray(e)?e.join(","):e,this.get("users/by",{...t,usernames:e})}async followers(e,t={}){let{asPaginator:s,...r}=t,i={id:e};if(!s)return this.get("users/:id/followers",r,{params:i});let a=await this.get("users/:id/followers",r,{fullResponse:!0,params:i});return new eZ({realData:a.data,rateLimit:a.rateLimit,instance:this,queryParams:r,sharedParams:i})}async following(e,t={}){let{asPaginator:s,...r}=t,i={id:e};if(!s)return this.get("users/:id/following",r,{params:i});let a=await this.get("users/:id/following",r,{fullResponse:!0,params:i});return new eX({realData:a.data,rateLimit:a.rateLimit,instance:this,queryParams:r,sharedParams:i})}async userLikedTweets(e,t={}){let s={id:e},r=await this.get("users/:id/liked_tweets",t,{fullResponse:!0,params:s});return new eK({realData:r.data,rateLimit:r.rateLimit,instance:this,queryParams:{...t},sharedParams:s})}async userBlockingUsers(e,t={}){let s={id:e},r=await this.get("users/:id/blocking",t,{fullResponse:!0,params:s});return new eJ({realData:r.data,rateLimit:r.rateLimit,instance:this,queryParams:{...t},sharedParams:s})}async userMutingUsers(e,t={}){let s={id:e},r=await this.get("users/:id/muting",t,{fullResponse:!0,params:s});return new eG({realData:r.data,rateLimit:r.rateLimit,instance:this,queryParams:{...t},sharedParams:s})}list(e,t={}){return this.get("lists/:id",t,{params:{id:e}})}async listsOwned(e,t={}){let s={id:e},r=await this.get("users/:id/owned_lists",t,{fullResponse:!0,params:s});return new e5({realData:r.data,rateLimit:r.rateLimit,instance:this,queryParams:{...t},sharedParams:s})}async listMemberships(e,t={}){let s={id:e},r=await this.get("users/:id/list_memberships",t,{fullResponse:!0,params:s});return new e6({realData:r.data,rateLimit:r.rateLimit,instance:this,queryParams:{...t},sharedParams:s})}async listFollowed(e,t={}){let s={id:e},r=await this.get("users/:id/followed_lists",t,{fullResponse:!0,params:s});return new e8({realData:r.data,rateLimit:r.rateLimit,instance:this,queryParams:{...t},sharedParams:s})}async listTweets(e,t={}){let s={id:e},r=await this.get("lists/:id/tweets",t,{fullResponse:!0,params:s});return new eQ({realData:r.data,rateLimit:r.rateLimit,instance:this,queryParams:{...t},sharedParams:s})}async listMembers(e,t={}){let s={id:e},r=await this.get("lists/:id/members",t,{fullResponse:!0,params:s});return new e0({realData:r.data,rateLimit:r.rateLimit,instance:this,queryParams:{...t},sharedParams:s})}async listFollowers(e,t={}){let s={id:e},r=await this.get("lists/:id/followers",t,{fullResponse:!0,params:s});return new e1({realData:r.data,rateLimit:r.rateLimit,instance:this,queryParams:{...t},sharedParams:s})}async listDmEvents(e={}){let t=await this.get("dm_events",e,{fullResponse:!0});return new te({realData:t.data,rateLimit:t.rateLimit,instance:this,queryParams:{...e}})}async listDmEventsWithParticipant(e,t={}){let s={participant_id:e},r=await this.get("dm_conversations/with/:participant_id/dm_events",t,{fullResponse:!0,params:s});return new tt({realData:r.data,rateLimit:r.rateLimit,instance:this,queryParams:{...t},sharedParams:s})}async listDmEventsOfConversation(e,t={}){let s={dm_conversation_id:e},r=await this.get("dm_conversations/:dm_conversation_id/dm_events",t,{fullResponse:!0,params:s});return new ts({realData:r.data,rateLimit:r.rateLimit,instance:this,queryParams:{...t},sharedParams:s})}space(e,t={}){return this.get("spaces/:id",t,{params:{id:e}})}spaces(e,t={}){return this.get("spaces",{ids:e,...t})}spacesByCreators(e,t={}){return this.get("spaces/by/creator_ids",{user_ids:e,...t})}searchSpaces(e){return this.get("spaces/search",e)}spaceBuyers(e,t={}){return this.get("spaces/:id/buyers",t,{params:{id:e}})}spaceTweets(e,t={}){return this.get("spaces/:id/tweets",t,{params:{id:e}})}searchStream({autoConnect:e,...t}={}){return this.getStream("tweets/search/stream",t,{payloadIsError:E,autoConnect:e})}streamRules(e={}){return this.get("tweets/search/stream/rules",e)}updateStreamRules(e,t={}){return this.post("tweets/search/stream/rules",e,{query:t})}sampleStream({autoConnect:e,...t}={}){return this.getStream("tweets/sample/stream",t,{payloadIsError:E,autoConnect:e})}sample10Stream({autoConnect:e,...t}={}){return this.getStream("tweets/sample10/stream",t,{payloadIsError:E,autoConnect:e})}complianceJobs(e){return this.get("compliance/jobs",e)}complianceJob(e){return this.get("compliance/jobs/:id",void 0,{params:{id:e}})}async sendComplianceJob(e){let t=await this.post("compliance/jobs",{type:e.type,name:e.name}),s=e.ids instanceof Buffer?e.ids:Buffer.from(e.ids.join("\n"));return await this.put(t.data.upload_url,s,{forceBodyMode:"raw",enableAuth:!1,headers:{"Content-Type":"text/plain"},prefix:""}),t}async complianceJobResult(e){let t=e;for(;"complete"!==t.status;){if("expired"===t.status||"failed"===t.status)throw Error("Job failed to be completed.");await new Promise(e=>setTimeout(e,3500)),t=(await this.complianceJob(e.id)).data}return(await this.get(e.download_url,void 0,{enableAuth:!1,prefix:""})).trim().split("\n").filter(e=>e).map(e=>JSON.parse(e))}async usage(e={}){return this.get("usage/tweets",e)}community(e,t={}){return this.get("communities/:id",t,{params:{id:e}})}searchCommunities(e,t={}){return this.get("communities/search",{query:e,...t})}}class ti extends e9{constructor(){super(...arguments),this._prefix=i}get readOnly(){return this}}class ta extends tr{constructor(){super(...arguments),this._prefix=r}get readOnly(){return this}get labs(){return this._labs?this._labs:this._labs=new ti(this)}hideReply(e,t){return this.put("tweets/:id/hidden",{hidden:t},{params:{id:e}})}like(e,t){return this.post("users/:id/likes",{tweet_id:t},{params:{id:e}})}unlike(e,t){return this.delete("users/:id/likes/:tweet_id",void 0,{params:{id:e,tweet_id:t}})}retweet(e,t){return this.post("users/:id/retweets",{tweet_id:t},{params:{id:e}})}unretweet(e,t){return this.delete("users/:id/retweets/:tweet_id",void 0,{params:{id:e,tweet_id:t}})}tweet(e,t={}){return t="object"==typeof e?e:{text:e,...t},this.post("tweets",t)}async uploadMedia(e,t,s=1048576){let r=t.media_category;!t.media_category&&(t.media_type.includes("gif")?r="tweet_gif":t.media_type.includes("image")?r="tweet_image":t.media_type.includes("video")&&(r="tweet_video"));let i={additional_owners:t.additional_owners,media_type:t.media_type,total_bytes:e.length,media_category:r},a=(await this.post("media/upload/initialize",i)).data.id,n=Math.ceil(e.length/s),o=new Uint8Array(e);for(let t=0;t<n;t++){let r=t*s,i=Math.min(r+s,e.length),n=o.slice(r,i),u={segment_index:t,media:Buffer.from(n)};await this.post(`media/upload/${a}/append`,u,{forceBodyMode:"form-data"})}return(await this.post(`media/upload/${a}/finalize`)).data.processing_info&&await this.waitForMediaProcessing(a),a}async waitForMediaProcessing(e){var t;let s=(await this.get("media/upload",{command:"STATUS",media_id:e})).data.processing_info;if(s)switch(s.state){case"succeeded":return;case"failed":throw Error(`Media processing failed: ${null==(t=s.error)?void 0:t.message}`);case"pending":case"in_progress":{let t=null==s?void 0:s.check_after_secs;t&&t>0&&(await new Promise(e=>setTimeout(e,1e3*t)),await this.waitForMediaProcessing(e))}}}createMediaMetadata(e,t){return this.post("media/metadata",{id:e,metadata:t})}reply(e,t,s={}){var r;let i={in_reply_to_tweet_id:t,...null!=(r=s.reply)?r:{}};return this.post("tweets",{text:e,...s,reply:i})}quote(e,t,s={}){return this.tweet(e,{...s,quote_tweet_id:t})}async tweetThread(e){var t,s;let r=[];for(let i of e){let e=r.length?r[r.length-1]:null,a={..."string"==typeof i?{text:i}:i},n=e?e.data.id:null==(t=a.reply)?void 0:t.in_reply_to_tweet_id,o=null!=(s=a.text)?s:"";n?r.push(await this.reply(o,n,a)):r.push(await this.tweet(o,a))}return r}deleteTweet(e){return this.delete("tweets/:id",void 0,{params:{id:e}})}async bookmark(e){let t=await this.getCurrentUserV2Object();return this.post("users/:id/bookmarks",{tweet_id:e},{params:{id:t.data.id}})}async deleteBookmark(e){let t=await this.getCurrentUserV2Object();return this.delete("users/:id/bookmarks/:tweet_id",void 0,{params:{id:t.data.id,tweet_id:e}})}follow(e,t){return this.post("users/:id/following",{target_user_id:t},{params:{id:e}})}unfollow(e,t){return this.delete("users/:source_user_id/following/:target_user_id",void 0,{params:{source_user_id:e,target_user_id:t}})}block(e,t){return this.post("users/:id/blocking",{target_user_id:t},{params:{id:e}})}unblock(e,t){return this.delete("users/:source_user_id/blocking/:target_user_id",void 0,{params:{source_user_id:e,target_user_id:t}})}mute(e,t){return this.post("users/:id/muting",{target_user_id:t},{params:{id:e}})}unmute(e,t){return this.delete("users/:source_user_id/muting/:target_user_id",void 0,{params:{source_user_id:e,target_user_id:t}})}createList(e){return this.post("lists",e)}updateList(e,t={}){return this.put("lists/:id",t,{params:{id:e}})}removeList(e){return this.delete("lists/:id",void 0,{params:{id:e}})}addListMember(e,t){return this.post("lists/:id/members",{user_id:t},{params:{id:e}})}removeListMember(e,t){return this.delete("lists/:id/members/:user_id",void 0,{params:{id:e,user_id:t}})}subscribeToList(e,t){return this.post("users/:id/followed_lists",{list_id:t},{params:{id:e}})}unsubscribeOfList(e,t){return this.delete("users/:id/followed_lists/:list_id",void 0,{params:{id:e,list_id:t}})}pinList(e,t){return this.post("users/:id/pinned_lists",{list_id:t},{params:{id:e}})}unpinList(e,t){return this.delete("users/:id/pinned_lists/:list_id",void 0,{params:{id:e,list_id:t}})}sendDmInConversation(e,t){return this.post("dm_conversations/:dm_conversation_id/messages",t,{params:{dm_conversation_id:e}})}sendDmToParticipant(e,t){return this.post("dm_conversations/with/:participant_id/messages",t,{params:{participant_id:e}})}createDmConversation(e){return this.post("dm_conversations",e)}}class tn extends ti{constructor(){super(...arguments),this._prefix=i}get readWrite(){return this}}class to extends ta{constructor(){super(...arguments),this._prefix=r}get readWrite(){return this}get labs(){return this._labs?this._labs:this._labs=new tn(this)}}class tu extends ee{get v1(){return this._v1?this._v1:this._v1=new eD(this)}get v2(){return this._v2?this._v2:this._v2=new tr(this)}async currentUser(e=!1){return await this.getCurrentUserObject(e)}async currentUserV2(e=!1){return await this.getCurrentUserV2Object(e)}search(e,t){return this.v2.search(e,t)}async generateAuthLink(e="oob",{authAccessType:t,linkMode:s="authenticate",forceLogin:r,screenName:i}={}){let a=await this.post("https://api.x.com/oauth/request_token",{oauth_callback:e,x_auth_access_type:t}),n=`https://api.x.com/oauth/${s}?oauth_token=${encodeURIComponent(a.oauth_token)}`;return void 0!==r&&(n+=`&force_login=${encodeURIComponent(r)}`),void 0!==i&&(n+=`&screen_name=${encodeURIComponent(i)}`),this._requestMaker.hasPlugins()&&this._requestMaker.applyPluginMethod("onOAuth1RequestToken",{client:this._requestMaker,url:n,oauthResult:a}),{url:n,...a}}async login(e){let t=this.getActiveTokens();if("oauth-1.0a"!==t.type)throw Error("You must setup TwitterApi instance with consumer keys to accept OAuth 1.0 login");let s=await this.post("https://api.x.com/oauth/access_token",{oauth_token:t.accessToken,oauth_verifier:e}),r=new tf({appKey:t.appKey,appSecret:t.appSecret,accessToken:s.oauth_token,accessSecret:s.oauth_token_secret},this._requestMaker.clientSettings);return{accessToken:s.oauth_token,accessSecret:s.oauth_token_secret,userId:s.user_id,screenName:s.screen_name,client:r}}async appLogin(){let e=this.getActiveTokens();if("oauth-1.0a"!==e.type)throw Error("You must setup TwitterApi instance with consumer keys to accept app-only login");let t=new tf({username:e.appKey,password:e.appSecret},this._requestMaker.clientSettings);return new tf((await t.post("https://api.x.com/oauth2/token",{grant_type:"client_credentials"})).access_token,this._requestMaker.clientSettings)}generateOAuth2AuthLink(e,t={}){var s,r;if(!this._requestMaker.clientId)throw Error("Twitter API instance is not initialized with client ID. You can find your client ID in Twitter Developer Portal. Please build an instance with: new TwitterApi({ clientId: '<yourClientId>' })");let i=null!=(s=t.state)?s:Z.generateRandomString(32),a=Z.getCodeVerifier(),n=Z.getCodeChallengeFromVerifier(a),o=null!=(r=t.scope)?r:"",u=Array.isArray(o)?o.join(" "):o,l=new URL("https://x.com/i/oauth2/authorize"),d={response_type:"code",client_id:this._requestMaker.clientId,redirect_uri:e,state:i,code_challenge:n,code_challenge_method:"s256",scope:u};G.addQueryParamsToUrl(l,d);let c={url:l.toString(),state:i,codeVerifier:a,codeChallenge:n};return this._requestMaker.hasPlugins()&&this._requestMaker.applyPluginMethod("onOAuth2RequestToken",{client:this._requestMaker,result:c,redirectUri:e}),c}async loginWithOAuth2({code:e,codeVerifier:t,redirectUri:s}){if(!this._requestMaker.clientId)throw Error("Twitter API instance is not initialized with client ID. Please build an instance with: new TwitterApi({ clientId: '<yourClientId>' })");let r=await this.post("https://api.x.com/2/oauth2/token",{code:e,code_verifier:t,redirect_uri:s,grant_type:"authorization_code",client_id:this._requestMaker.clientId,client_secret:this._requestMaker.clientSecret});return this.parseOAuth2AccessTokenResult(r)}async refreshOAuth2Token(e){if(!this._requestMaker.clientId)throw Error("Twitter API instance is not initialized with client ID. Please build an instance with: new TwitterApi({ clientId: '<yourClientId>' })");let t=await this.post("https://api.x.com/2/oauth2/token",{refresh_token:e,grant_type:"refresh_token",client_id:this._requestMaker.clientId,client_secret:this._requestMaker.clientSecret});return this.parseOAuth2AccessTokenResult(t)}async revokeOAuth2Token(e,t="access_token"){if(!this._requestMaker.clientId)throw Error("Twitter API instance is not initialized with client ID. Please build an instance with: new TwitterApi({ clientId: '<yourClientId>' })");return await this.post("https://api.x.com/2/oauth2/revoke",{client_id:this._requestMaker.clientId,client_secret:this._requestMaker.clientSecret,token:e,token_type_hint:t})}parseOAuth2AccessTokenResult(e){let t=new tf(e.access_token,this._requestMaker.clientSettings),s=e.scope.split(" ").filter(e=>e);return{client:t,expiresIn:e.expires_in,accessToken:e.access_token,scope:s,refreshToken:e.refresh_token}}}class tl extends tu{get v1(){return this._v1?this._v1:this._v1=new eE(this)}get v2(){return this._v2?this._v2:this._v2=new ta(this)}get readOnly(){return this}}class td extends et{constructor(){super(...arguments),this._prefix=o}}class tc extends td{constructor(){super(...arguments),this._prefix=o}get readOnly(){return this}}class th extends et{constructor(){super(...arguments),this._prefix=u}}class tm extends th{constructor(){super(...arguments),this._prefix=u}get readOnly(){return this}}class tp extends tm{constructor(){super(...arguments),this._prefix=u}get readWrite(){return this}}class tg extends tc{constructor(){super(...arguments),this._prefix=o}get readWrite(){return this}get sandbox(){return this._sandbox?this._sandbox:this._sandbox=new tp(this)}}class t_ extends tl{get v1(){return this._v1?this._v1:this._v1=new eI(this)}get v2(){return this._v2?this._v2:this._v2=new to(this)}get readWrite(){return this}get ads(){return this._ads?this._ads:this._ads=new tg(this)}static getErrors(e){var t;return"object"==typeof e&&"data"in e&&null!=(t=e.data.errors)?t:[]}static getProfileImageInSize(e,t){let s=e.split("/").pop(),r=e;for(let t of["normal","bigger","mini"])if(s.includes(`_${t}`)){r=e.replace(`_${t}`,"");break}if("original"===t)return r;let i=r.lastIndexOf(".");if(-1===i)return r+"_"+t;{let e=r.slice(i+1);return r.slice(0,i)+"_"+t+"."+e}}}let tf=t_}};