[{"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/generate/route.ts": "1", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/post/route.ts": "2", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/queue/[id]/route.ts": "3", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/queue/route.ts": "4", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/refresh/route.ts": "5", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/migrate/route.ts": "6", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/preferences/route.ts": "7", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/feeds/[id]/route.ts": "8", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/feeds/route.ts": "9", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/items/route.ts": "10", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/scheduler/route.ts": "11", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/twitter/stats/route.ts": "12", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/content/page.tsx": "13", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/feeds/page.tsx": "14", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/layout.tsx": "15", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/page.tsx": "16", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/openai.ts": "17", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/rss.ts": "18", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/scheduler.ts": "19", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/supabase.ts": "20", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/twitter.ts": "21", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/scripts/migrate-existing-bot.ts": "22"}, {"size": 770, "mtime": 1751192507413, "results": "23", "hashOfConfig": "24"}, {"size": 2776, "mtime": 1751192527058, "results": "25", "hashOfConfig": "24"}, {"size": 611, "mtime": 1751193151672, "results": "26", "hashOfConfig": "24"}, {"size": 427, "mtime": 1751193444431, "results": "27", "hashOfConfig": "24"}, {"size": 2098, "mtime": 1751193456179, "results": "28", "hashOfConfig": "24"}, {"size": 548, "mtime": 1751193467743, "results": "29", "hashOfConfig": "24"}, {"size": 1146, "mtime": 1751193250363, "results": "30", "hashOfConfig": "24"}, {"size": 1125, "mtime": 1751193168874, "results": "31", "hashOfConfig": "24"}, {"size": 960, "mtime": 1751193260584, "results": "32", "hashOfConfig": "24"}, {"size": 1696, "mtime": 1751193271361, "results": "33", "hashOfConfig": "24"}, {"size": 1389, "mtime": 1751193281529, "results": "34", "hashOfConfig": "24"}, {"size": 3800, "mtime": 1751198871194, "results": "35", "hashOfConfig": "24"}, {"size": 12056, "mtime": 1751193334543, "results": "36", "hashOfConfig": "24"}, {"size": 12969, "mtime": 1751192362483, "results": "37", "hashOfConfig": "24"}, {"size": 689, "mtime": 1751190993312, "results": "38", "hashOfConfig": "24"}, {"size": 5778, "mtime": 1751193345178, "results": "39", "hashOfConfig": "24"}, {"size": 6643, "mtime": 1751193370400, "results": "40", "hashOfConfig": "24"}, {"size": 5298, "mtime": 1751193525736, "results": "41", "hashOfConfig": "24"}, {"size": 7649, "mtime": 1751198894892, "results": "42", "hashOfConfig": "24"}, {"size": 6111, "mtime": 1751192476538, "results": "43", "hashOfConfig": "24"}, {"size": 5506, "mtime": 1751199095836, "results": "44", "hashOfConfig": "24"}, {"size": 3069, "mtime": 1751198998588, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "15v4ex5", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/generate/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/post/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/queue/[id]/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/queue/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/refresh/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/migrate/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/preferences/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/feeds/[id]/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/feeds/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/items/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/scheduler/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/twitter/stats/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/content/page.tsx", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/feeds/page.tsx", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/layout.tsx", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/page.tsx", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/openai.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/rss.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/scheduler.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/supabase.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/twitter.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/scripts/migrate-existing-bot.ts", [], []]